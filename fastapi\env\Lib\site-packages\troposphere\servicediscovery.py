# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import double


class HttpNamespace(AWSObject):
    """
    `HttpNamespace <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicediscovery-httpnamespace.html>`__
    """

    resource_type = "AWS::ServiceDiscovery::HttpNamespace"

    props: PropsDictType = {
        "Description": (str, False),
        "Name": (str, True),
        "Tags": (Tags, False),
    }


class Instance(AWSObject):
    """
    `Instance <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicediscovery-instance.html>`__
    """

    resource_type = "AWS::ServiceDiscovery::Instance"

    props: PropsDictType = {
        "InstanceAttributes": (dict, True),
        "InstanceId": (str, False),
        "ServiceId": (str, True),
    }


class SOA(AWSProperty):
    """
    `SOA <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-servicediscovery-publicdnsnamespace-soa.html>`__
    """

    props: PropsDictType = {
        "TTL": (double, False),
    }


class PublicDnsPropertiesMutable(AWSProperty):
    """
    `PublicDnsPropertiesMutable <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-servicediscovery-publicdnsnamespace-publicdnspropertiesmutable.html>`__
    """

    props: PropsDictType = {
        "SOA": (SOA, False),
    }


class Properties(AWSProperty):
    """
    `Properties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-servicediscovery-publicdnsnamespace-properties.html>`__
    """

    props: PropsDictType = {
        "DnsProperties": (PublicDnsPropertiesMutable, False),
    }


class PrivateDnsNamespace(AWSObject):
    """
    `PrivateDnsNamespace <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicediscovery-privatednsnamespace.html>`__
    """

    resource_type = "AWS::ServiceDiscovery::PrivateDnsNamespace"

    props: PropsDictType = {
        "Description": (str, False),
        "Name": (str, True),
        "Properties": (Properties, False),
        "Tags": (Tags, False),
        "Vpc": (str, True),
    }


class PublicDnsNamespace(AWSObject):
    """
    `PublicDnsNamespace <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicediscovery-publicdnsnamespace.html>`__
    """

    resource_type = "AWS::ServiceDiscovery::PublicDnsNamespace"

    props: PropsDictType = {
        "Description": (str, False),
        "Name": (str, True),
        "Properties": (Properties, False),
        "Tags": (Tags, False),
    }


class DnsRecord(AWSProperty):
    """
    `DnsRecord <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-servicediscovery-service-dnsrecord.html>`__
    """

    props: PropsDictType = {
        "TTL": (double, True),
        "Type": (str, True),
    }


class DnsConfig(AWSProperty):
    """
    `DnsConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-servicediscovery-service-dnsconfig.html>`__
    """

    props: PropsDictType = {
        "DnsRecords": ([DnsRecord], True),
        "NamespaceId": (str, False),
        "RoutingPolicy": (str, False),
    }


class HealthCheckConfig(AWSProperty):
    """
    `HealthCheckConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-servicediscovery-service-healthcheckconfig.html>`__
    """

    props: PropsDictType = {
        "FailureThreshold": (double, False),
        "ResourcePath": (str, False),
        "Type": (str, True),
    }


class HealthCheckCustomConfig(AWSProperty):
    """
    `HealthCheckCustomConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-servicediscovery-service-healthcheckcustomconfig.html>`__
    """

    props: PropsDictType = {
        "FailureThreshold": (double, False),
    }


class Service(AWSObject):
    """
    `Service <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicediscovery-service.html>`__
    """

    resource_type = "AWS::ServiceDiscovery::Service"

    props: PropsDictType = {
        "Description": (str, False),
        "DnsConfig": (DnsConfig, False),
        "HealthCheckConfig": (HealthCheckConfig, False),
        "HealthCheckCustomConfig": (HealthCheckCustomConfig, False),
        "Name": (str, False),
        "NamespaceId": (str, False),
        "ServiceAttributes": (dict, False),
        "Tags": (Tags, False),
        "Type": (str, False),
    }


class PrivateDnsPropertiesMutable(AWSProperty):
    """
    `PrivateDnsPropertiesMutable <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-servicediscovery-privatednsnamespace-privatednspropertiesmutable.html>`__
    """

    props: PropsDictType = {
        "SOA": (SOA, False),
    }
