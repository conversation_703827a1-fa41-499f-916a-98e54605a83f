# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer


class MultitrackInputConfiguration(AWSProperty):
    """
    `MultitrackInputConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ivs-channel-multitrackinputconfiguration.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, False),
        "MaximumResolution": (str, False),
        "Policy": (str, False),
    }


class Channel(AWSObject):
    """
    `Channel <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ivs-channel.html>`__
    """

    resource_type = "AWS::IVS::Channel"

    props: PropsDictType = {
        "Authorized": (boolean, False),
        "ContainerFormat": (str, False),
        "InsecureIngest": (boolean, False),
        "LatencyMode": (str, False),
        "MultitrackInputConfiguration": (MultitrackInputConfiguration, False),
        "Name": (str, False),
        "Preset": (str, False),
        "RecordingConfigurationArn": (str, False),
        "Tags": (Tags, False),
        "Type": (str, False),
    }


class Video(AWSProperty):
    """
    `Video <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ivs-encoderconfiguration-video.html>`__
    """

    props: PropsDictType = {
        "Bitrate": (integer, False),
        "Framerate": (double, False),
        "Height": (integer, False),
        "Width": (integer, False),
    }


class EncoderConfiguration(AWSObject):
    """
    `EncoderConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ivs-encoderconfiguration.html>`__
    """

    resource_type = "AWS::IVS::EncoderConfiguration"

    props: PropsDictType = {
        "Name": (str, False),
        "Tags": (Tags, False),
        "Video": (Video, False),
    }


class IngestConfiguration(AWSObject):
    """
    `IngestConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ivs-ingestconfiguration.html>`__
    """

    resource_type = "AWS::IVS::IngestConfiguration"

    props: PropsDictType = {
        "IngestProtocol": (str, False),
        "InsecureIngest": (boolean, False),
        "Name": (str, False),
        "StageArn": (str, False),
        "Tags": (Tags, False),
        "UserId": (str, False),
    }


class PlaybackKeyPair(AWSObject):
    """
    `PlaybackKeyPair <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ivs-playbackkeypair.html>`__
    """

    resource_type = "AWS::IVS::PlaybackKeyPair"

    props: PropsDictType = {
        "Name": (str, False),
        "PublicKeyMaterial": (str, False),
        "Tags": (Tags, False),
    }


class PlaybackRestrictionPolicy(AWSObject):
    """
    `PlaybackRestrictionPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ivs-playbackrestrictionpolicy.html>`__
    """

    resource_type = "AWS::IVS::PlaybackRestrictionPolicy"

    props: PropsDictType = {
        "AllowedCountries": ([str], True),
        "AllowedOrigins": ([str], True),
        "EnableStrictOriginEnforcement": (boolean, False),
        "Name": (str, False),
        "Tags": (Tags, False),
    }


class PublicKey(AWSObject):
    """
    `PublicKey <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ivs-publickey.html>`__
    """

    resource_type = "AWS::IVS::PublicKey"

    props: PropsDictType = {
        "Name": (str, False),
        "PublicKeyMaterial": (str, False),
        "Tags": (Tags, False),
    }


class S3DestinationConfiguration(AWSProperty):
    """
    `S3DestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ivs-recordingconfiguration-s3destinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "BucketName": (str, True),
    }


class DestinationConfiguration(AWSProperty):
    """
    `DestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ivs-recordingconfiguration-destinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "S3": (S3DestinationConfiguration, False),
    }


class RenditionConfiguration(AWSProperty):
    """
    `RenditionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ivs-recordingconfiguration-renditionconfiguration.html>`__
    """

    props: PropsDictType = {
        "RenditionSelection": (str, False),
        "Renditions": ([str], False),
    }


class ThumbnailConfiguration(AWSProperty):
    """
    `ThumbnailConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ivs-recordingconfiguration-thumbnailconfiguration.html>`__
    """

    props: PropsDictType = {
        "RecordingMode": (str, False),
        "Resolution": (str, False),
        "Storage": ([str], False),
        "TargetIntervalSeconds": (integer, False),
    }


class RecordingConfiguration(AWSObject):
    """
    `RecordingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ivs-recordingconfiguration.html>`__
    """

    resource_type = "AWS::IVS::RecordingConfiguration"

    props: PropsDictType = {
        "DestinationConfiguration": (DestinationConfiguration, True),
        "Name": (str, False),
        "RecordingReconnectWindowSeconds": (integer, False),
        "RenditionConfiguration": (RenditionConfiguration, False),
        "Tags": (Tags, False),
        "ThumbnailConfiguration": (ThumbnailConfiguration, False),
    }


class AutoParticipantRecordingConfiguration(AWSProperty):
    """
    `AutoParticipantRecordingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ivs-stage-autoparticipantrecordingconfiguration.html>`__
    """

    props: PropsDictType = {
        "MediaTypes": ([str], False),
        "StorageConfigurationArn": (str, True),
    }


class Stage(AWSObject):
    """
    `Stage <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ivs-stage.html>`__
    """

    resource_type = "AWS::IVS::Stage"

    props: PropsDictType = {
        "AutoParticipantRecordingConfiguration": (
            AutoParticipantRecordingConfiguration,
            False,
        ),
        "Name": (str, False),
        "Tags": (Tags, False),
    }


class S3StorageConfiguration(AWSProperty):
    """
    `S3StorageConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ivs-storageconfiguration-s3storageconfiguration.html>`__
    """

    props: PropsDictType = {
        "BucketName": (str, True),
    }


class StorageConfiguration(AWSObject):
    """
    `StorageConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ivs-storageconfiguration.html>`__
    """

    resource_type = "AWS::IVS::StorageConfiguration"

    props: PropsDictType = {
        "Name": (str, False),
        "S3": (S3StorageConfiguration, True),
        "Tags": (Tags, False),
    }


class StreamKey(AWSObject):
    """
    `StreamKey <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ivs-streamkey.html>`__
    """

    resource_type = "AWS::IVS::StreamKey"

    props: PropsDictType = {
        "ChannelArn": (str, True),
        "Tags": (Tags, False),
    }
