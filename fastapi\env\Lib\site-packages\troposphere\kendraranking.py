# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import integer


class CapacityUnitsConfiguration(AWSProperty):
    """
    `CapacityUnitsConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kendraranking-executionplan-capacityunitsconfiguration.html>`__
    """

    props: PropsDictType = {
        "RescoreCapacityUnits": (integer, True),
    }


class ExecutionPlan(AWSObject):
    """
    `ExecutionPlan <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-kendraranking-executionplan.html>`__
    """

    resource_type = "AWS::KendraRanking::ExecutionPlan"

    props: PropsDictType = {
        "CapacityUnits": (CapacityUnitsConfiguration, False),
        "Description": (str, False),
        "Name": (str, True),
        "Tags": (Tags, False),
    }
