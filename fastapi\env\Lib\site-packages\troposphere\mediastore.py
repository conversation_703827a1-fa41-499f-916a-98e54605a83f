# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, integer
from .validators.mediastore import containerlevelmetrics_status


class CorsRule(AWSProperty):
    """
    `CorsRule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediastore-container-corsrule.html>`__
    """

    props: PropsDictType = {
        "AllowedHeaders": ([str], False),
        "AllowedMethods": ([str], False),
        "AllowedOrigins": ([str], False),
        "ExposeHeaders": ([str], False),
        "MaxAgeSeconds": (integer, False),
    }


class MetricPolicyRule(AWSProperty):
    """
    `MetricPolicyRule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediastore-container-metricpolicyrule.html>`__
    """

    props: PropsDictType = {
        "ObjectGroup": (str, True),
        "ObjectGroupName": (str, True),
    }


class MetricPolicy(AWSProperty):
    """
    `MetricPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediastore-container-metricpolicy.html>`__
    """

    props: PropsDictType = {
        "ContainerLevelMetrics": (containerlevelmetrics_status, True),
        "MetricPolicyRules": ([MetricPolicyRule], False),
    }


class Container(AWSObject):
    """
    `Container <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-mediastore-container.html>`__
    """

    resource_type = "AWS::MediaStore::Container"

    props: PropsDictType = {
        "AccessLoggingEnabled": (boolean, False),
        "ContainerName": (str, True),
        "CorsPolicy": ([CorsRule], False),
        "LifecyclePolicy": (str, False),
        "MetricPolicy": (MetricPolicy, False),
        "Policy": (str, False),
        "Tags": (Tags, False),
    }
