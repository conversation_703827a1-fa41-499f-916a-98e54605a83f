# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer


class EmailOutboundConfig(AWSProperty):
    """
    `EmailOutboundConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-emailoutboundconfig.html>`__
    """

    props: PropsDictType = {
        "ConnectSourceEmailAddress": (str, True),
        "SourceEmailAddressDisplayName": (str, False),
        "WisdomTemplateArn": (str, True),
    }


class EmailOutboundMode(AWSProperty):
    """
    `EmailOutboundMode <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-emailoutboundmode.html>`__
    """

    props: PropsDictType = {
        "AgentlessConfig": (dict, False),
    }


class EmailChannelSubtypeConfig(AWSProperty):
    """
    `EmailChannelSubtypeConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-emailchannelsubtypeconfig.html>`__
    """

    props: PropsDictType = {
        "Capacity": (double, False),
        "DefaultOutboundConfig": (EmailOutboundConfig, True),
        "OutboundMode": (EmailOutboundMode, True),
    }


class SmsOutboundConfig(AWSProperty):
    """
    `SmsOutboundConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-smsoutboundconfig.html>`__
    """

    props: PropsDictType = {
        "ConnectSourcePhoneNumberArn": (str, True),
        "WisdomTemplateArn": (str, True),
    }


class SmsOutboundMode(AWSProperty):
    """
    `SmsOutboundMode <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-smsoutboundmode.html>`__
    """

    props: PropsDictType = {
        "AgentlessConfig": (dict, False),
    }


class SmsChannelSubtypeConfig(AWSProperty):
    """
    `SmsChannelSubtypeConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-smschannelsubtypeconfig.html>`__
    """

    props: PropsDictType = {
        "Capacity": (double, False),
        "DefaultOutboundConfig": (SmsOutboundConfig, True),
        "OutboundMode": (SmsOutboundMode, True),
    }


class AnswerMachineDetectionConfig(AWSProperty):
    """
    `AnswerMachineDetectionConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-answermachinedetectionconfig.html>`__
    """

    props: PropsDictType = {
        "AwaitAnswerMachinePrompt": (boolean, False),
        "EnableAnswerMachineDetection": (boolean, True),
    }


class TelephonyOutboundConfig(AWSProperty):
    """
    `TelephonyOutboundConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-telephonyoutboundconfig.html>`__
    """

    props: PropsDictType = {
        "AnswerMachineDetectionConfig": (AnswerMachineDetectionConfig, False),
        "ConnectContactFlowId": (str, True),
        "ConnectSourcePhoneNumber": (str, False),
    }


class PredictiveConfig(AWSProperty):
    """
    `PredictiveConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-predictiveconfig.html>`__
    """

    props: PropsDictType = {
        "BandwidthAllocation": (double, True),
    }


class ProgressiveConfig(AWSProperty):
    """
    `ProgressiveConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-progressiveconfig.html>`__
    """

    props: PropsDictType = {
        "BandwidthAllocation": (double, True),
    }


class TelephonyOutboundMode(AWSProperty):
    """
    `TelephonyOutboundMode <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-telephonyoutboundmode.html>`__
    """

    props: PropsDictType = {
        "AgentlessConfig": (dict, False),
        "PredictiveConfig": (PredictiveConfig, False),
        "ProgressiveConfig": (ProgressiveConfig, False),
    }


class TelephonyChannelSubtypeConfig(AWSProperty):
    """
    `TelephonyChannelSubtypeConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-telephonychannelsubtypeconfig.html>`__
    """

    props: PropsDictType = {
        "Capacity": (double, False),
        "ConnectQueueId": (str, False),
        "DefaultOutboundConfig": (TelephonyOutboundConfig, True),
        "OutboundMode": (TelephonyOutboundMode, True),
    }


class ChannelSubtypeConfig(AWSProperty):
    """
    `ChannelSubtypeConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-channelsubtypeconfig.html>`__
    """

    props: PropsDictType = {
        "Email": (EmailChannelSubtypeConfig, False),
        "Sms": (SmsChannelSubtypeConfig, False),
        "Telephony": (TelephonyChannelSubtypeConfig, False),
    }


class CommunicationLimit(AWSProperty):
    """
    `CommunicationLimit <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-communicationlimit.html>`__
    """

    props: PropsDictType = {
        "Frequency": (integer, True),
        "MaxCountPerRecipient": (integer, True),
        "Unit": (str, True),
    }


class CommunicationLimits(AWSProperty):
    """
    `CommunicationLimits <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-communicationlimits.html>`__
    """

    props: PropsDictType = {
        "CommunicationLimitList": ([CommunicationLimit], False),
    }


class CommunicationLimitsConfig(AWSProperty):
    """
    `CommunicationLimitsConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-communicationlimitsconfig.html>`__
    """

    props: PropsDictType = {
        "AllChannelsSubtypes": (CommunicationLimits, False),
    }


class LocalTimeZoneConfig(AWSProperty):
    """
    `LocalTimeZoneConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-localtimezoneconfig.html>`__
    """

    props: PropsDictType = {
        "DefaultTimeZone": (str, False),
        "LocalTimeZoneDetection": ([str], False),
    }


class TimeRange(AWSProperty):
    """
    `TimeRange <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-timerange.html>`__
    """

    props: PropsDictType = {
        "EndTime": (str, True),
        "StartTime": (str, True),
    }


class DailyHour(AWSProperty):
    """
    `DailyHour <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-dailyhour.html>`__
    """

    props: PropsDictType = {
        "Key": (str, False),
        "Value": ([TimeRange], False),
    }


class OpenHours(AWSProperty):
    """
    `OpenHours <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-openhours.html>`__
    """

    props: PropsDictType = {
        "DailyHours": ([DailyHour], True),
    }


class RestrictedPeriod(AWSProperty):
    """
    `RestrictedPeriod <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-restrictedperiod.html>`__
    """

    props: PropsDictType = {
        "EndDate": (str, True),
        "Name": (str, False),
        "StartDate": (str, True),
    }


class RestrictedPeriods(AWSProperty):
    """
    `RestrictedPeriods <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-restrictedperiods.html>`__
    """

    props: PropsDictType = {
        "RestrictedPeriodList": ([RestrictedPeriod], True),
    }


class TimeWindow(AWSProperty):
    """
    `TimeWindow <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-timewindow.html>`__
    """

    props: PropsDictType = {
        "OpenHours": (OpenHours, True),
        "RestrictedPeriods": (RestrictedPeriods, False),
    }


class CommunicationTimeConfig(AWSProperty):
    """
    `CommunicationTimeConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-communicationtimeconfig.html>`__
    """

    props: PropsDictType = {
        "Email": (TimeWindow, False),
        "LocalTimeZoneConfig": (LocalTimeZoneConfig, True),
        "Sms": (TimeWindow, False),
        "Telephony": (TimeWindow, False),
    }


class Schedule(AWSProperty):
    """
    `Schedule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-schedule.html>`__
    """

    props: PropsDictType = {
        "EndTime": (str, True),
        "RefreshFrequency": (str, False),
        "StartTime": (str, True),
    }


class EventTrigger(AWSProperty):
    """
    `EventTrigger <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-eventtrigger.html>`__
    """

    props: PropsDictType = {
        "CustomerProfilesDomainArn": (str, False),
    }


class Source(AWSProperty):
    """
    `Source <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaignsv2-campaign-source.html>`__
    """

    props: PropsDictType = {
        "CustomerProfilesSegmentArn": (str, False),
        "EventTrigger": (EventTrigger, False),
    }


class Campaign(AWSObject):
    """
    `Campaign <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-connectcampaignsv2-campaign.html>`__
    """

    resource_type = "AWS::ConnectCampaignsV2::Campaign"

    props: PropsDictType = {
        "ChannelSubtypeConfig": (ChannelSubtypeConfig, True),
        "CommunicationLimitsOverride": (CommunicationLimitsConfig, False),
        "CommunicationTimeConfig": (CommunicationTimeConfig, False),
        "ConnectCampaignFlowArn": (str, False),
        "ConnectInstanceId": (str, True),
        "Name": (str, True),
        "Schedule": (Schedule, False),
        "Source": (Source, False),
        "Tags": (Tags, False),
    }
