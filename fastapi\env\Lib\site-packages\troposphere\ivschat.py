# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import integer


class CloudWatchLogsDestinationConfiguration(AWSProperty):
    """
    `CloudWatchLogsDestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ivschat-loggingconfiguration-cloudwatchlogsdestinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "LogGroupName": (str, True),
    }


class FirehoseDestinationConfiguration(AWSProperty):
    """
    `FirehoseDestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ivschat-loggingconfiguration-firehosedestinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "DeliveryStreamName": (str, True),
    }


class S3DestinationConfiguration(AWSProperty):
    """
    `S3DestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ivschat-loggingconfiguration-s3destinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "BucketName": (str, True),
    }


class DestinationConfiguration(AWSProperty):
    """
    `DestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ivschat-loggingconfiguration-destinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "CloudWatchLogs": (CloudWatchLogsDestinationConfiguration, False),
        "Firehose": (FirehoseDestinationConfiguration, False),
        "S3": (S3DestinationConfiguration, False),
    }


class LoggingConfiguration(AWSObject):
    """
    `LoggingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ivschat-loggingconfiguration.html>`__
    """

    resource_type = "AWS::IVSChat::LoggingConfiguration"

    props: PropsDictType = {
        "DestinationConfiguration": (DestinationConfiguration, True),
        "Name": (str, False),
        "Tags": (Tags, False),
    }


class MessageReviewHandler(AWSProperty):
    """
    `MessageReviewHandler <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ivschat-room-messagereviewhandler.html>`__
    """

    props: PropsDictType = {
        "FallbackResult": (str, False),
        "Uri": (str, False),
    }


class Room(AWSObject):
    """
    `Room <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ivschat-room.html>`__
    """

    resource_type = "AWS::IVSChat::Room"

    props: PropsDictType = {
        "LoggingConfigurationIdentifiers": ([str], False),
        "MaximumMessageLength": (integer, False),
        "MaximumMessageRatePerSecond": (integer, False),
        "MessageReviewHandler": (MessageReviewHandler, False),
        "Name": (str, False),
        "Tags": (Tags, False),
    }
