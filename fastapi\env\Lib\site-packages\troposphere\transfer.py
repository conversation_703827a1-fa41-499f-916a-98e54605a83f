# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import double, integer
from .validators.transfer import validate_homedirectory_type


class CustomDirectories(AWSProperty):
    """
    `CustomDirectories <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-agreement-customdirectories.html>`__
    """

    props: PropsDictType = {
        "FailedFilesDirectory": (str, True),
        "MdnFilesDirectory": (str, True),
        "PayloadFilesDirectory": (str, True),
        "StatusFilesDirectory": (str, True),
        "TemporaryFilesDirectory": (str, True),
    }


class Agreement(AWSObject):
    """
    `Agreement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-transfer-agreement.html>`__
    """

    resource_type = "AWS::Transfer::Agreement"

    props: PropsDictType = {
        "AccessRole": (str, True),
        "BaseDirectory": (str, False),
        "CustomDirectories": (CustomDirectories, False),
        "Description": (str, False),
        "EnforceMessageSigning": (str, False),
        "LocalProfileId": (str, True),
        "PartnerProfileId": (str, True),
        "PreserveFilename": (str, False),
        "ServerId": (str, True),
        "Status": (str, False),
        "Tags": (Tags, False),
    }


class Certificate(AWSObject):
    """
    `Certificate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-transfer-certificate.html>`__
    """

    resource_type = "AWS::Transfer::Certificate"

    props: PropsDictType = {
        "ActiveDate": (str, False),
        "Certificate": (str, True),
        "CertificateChain": (str, False),
        "Description": (str, False),
        "InactiveDate": (str, False),
        "PrivateKey": (str, False),
        "Tags": (Tags, False),
        "Usage": (str, True),
    }


class As2Config(AWSProperty):
    """
    `As2Config <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-connector-as2config.html>`__
    """

    props: PropsDictType = {
        "BasicAuthSecretId": (str, False),
        "Compression": (str, False),
        "EncryptionAlgorithm": (str, False),
        "LocalProfileId": (str, False),
        "MdnResponse": (str, False),
        "MdnSigningAlgorithm": (str, False),
        "MessageSubject": (str, False),
        "PartnerProfileId": (str, False),
        "PreserveContentType": (str, False),
        "SigningAlgorithm": (str, False),
    }


class SftpConfig(AWSProperty):
    """
    `SftpConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-connector-sftpconfig.html>`__
    """

    props: PropsDictType = {
        "TrustedHostKeys": ([str], False),
        "UserSecretId": (str, False),
    }


class Connector(AWSObject):
    """
    `Connector <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-transfer-connector.html>`__
    """

    resource_type = "AWS::Transfer::Connector"

    props: PropsDictType = {
        "AccessRole": (str, True),
        "As2Config": (As2Config, False),
        "LoggingRole": (str, False),
        "SecurityPolicyName": (str, False),
        "SftpConfig": (SftpConfig, False),
        "Tags": (Tags, False),
        "Url": (str, True),
    }


class Profile(AWSObject):
    """
    `Profile <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-transfer-profile.html>`__
    """

    resource_type = "AWS::Transfer::Profile"

    props: PropsDictType = {
        "As2Id": (str, True),
        "CertificateIds": ([str], False),
        "ProfileType": (str, True),
        "Tags": (Tags, False),
    }


class EndpointDetails(AWSProperty):
    """
    `EndpointDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-server-endpointdetails.html>`__
    """

    props: PropsDictType = {
        "AddressAllocationIds": ([str], False),
        "SecurityGroupIds": ([str], False),
        "SubnetIds": ([str], False),
        "VpcEndpointId": (str, False),
        "VpcId": (str, False),
    }


class IdentityProviderDetails(AWSProperty):
    """
    `IdentityProviderDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-server-identityproviderdetails.html>`__
    """

    props: PropsDictType = {
        "DirectoryId": (str, False),
        "Function": (str, False),
        "InvocationRole": (str, False),
        "SftpAuthenticationMethods": (str, False),
        "Url": (str, False),
    }


class ProtocolDetails(AWSProperty):
    """
    `ProtocolDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-server-protocoldetails.html>`__
    """

    props: PropsDictType = {
        "As2Transports": ([str], False),
        "PassiveIp": (str, False),
        "SetStatOption": (str, False),
        "TlsSessionResumptionMode": (str, False),
    }


class S3StorageOptions(AWSProperty):
    """
    `S3StorageOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-server-s3storageoptions.html>`__
    """

    props: PropsDictType = {
        "DirectoryListingOptimization": (str, False),
    }


class WorkflowDetail(AWSProperty):
    """
    `WorkflowDetail <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-server-workflowdetail.html>`__
    """

    props: PropsDictType = {
        "ExecutionRole": (str, True),
        "WorkflowId": (str, True),
    }


class WorkflowDetails(AWSProperty):
    """
    `WorkflowDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-server-workflowdetails.html>`__
    """

    props: PropsDictType = {
        "OnPartialUpload": ([WorkflowDetail], False),
        "OnUpload": ([WorkflowDetail], False),
    }


class Server(AWSObject):
    """
    `Server <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-transfer-server.html>`__
    """

    resource_type = "AWS::Transfer::Server"

    props: PropsDictType = {
        "Certificate": (str, False),
        "Domain": (str, False),
        "EndpointDetails": (EndpointDetails, False),
        "EndpointType": (str, False),
        "IdentityProviderDetails": (IdentityProviderDetails, False),
        "IdentityProviderType": (str, False),
        "LoggingRole": (str, False),
        "PostAuthenticationLoginBanner": (str, False),
        "PreAuthenticationLoginBanner": (str, False),
        "ProtocolDetails": (ProtocolDetails, False),
        "Protocols": ([str], False),
        "S3StorageOptions": (S3StorageOptions, False),
        "SecurityPolicyName": (str, False),
        "StructuredLogDestinations": ([str], False),
        "Tags": (Tags, False),
        "WorkflowDetails": (WorkflowDetails, False),
    }


class HomeDirectoryMapEntry(AWSProperty):
    """
    `HomeDirectoryMapEntry <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-user-homedirectorymapentry.html>`__
    """

    props: PropsDictType = {
        "Entry": (str, True),
        "Target": (str, True),
        "Type": (str, False),
    }


class PosixProfile(AWSProperty):
    """
    `PosixProfile <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-user-posixprofile.html>`__
    """

    props: PropsDictType = {
        "Gid": (double, True),
        "SecondaryGids": ([double], False),
        "Uid": (double, True),
    }


class User(AWSObject):
    """
    `User <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-transfer-user.html>`__
    """

    resource_type = "AWS::Transfer::User"

    props: PropsDictType = {
        "HomeDirectory": (str, False),
        "HomeDirectoryMappings": ([HomeDirectoryMapEntry], False),
        "HomeDirectoryType": (validate_homedirectory_type, False),
        "Policy": (str, False),
        "PosixProfile": (PosixProfile, False),
        "Role": (str, True),
        "ServerId": (str, True),
        "SshPublicKeys": ([str], False),
        "Tags": (Tags, False),
        "UserName": (str, True),
    }


class WebAppCustomization(AWSProperty):
    """
    `WebAppCustomization <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-webapp-webappcustomization.html>`__
    """

    props: PropsDictType = {
        "FaviconFile": (str, False),
        "LogoFile": (str, False),
        "Title": (str, False),
    }


class WebAppIdentityProviderDetails(AWSProperty):
    """
    `WebAppIdentityProviderDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-webapp-identityproviderdetails.html>`__
    """

    props: PropsDictType = {
        "ApplicationArn": (str, False),
        "InstanceArn": (str, False),
        "Role": (str, False),
    }


class WebAppUnits(AWSProperty):
    """
    `WebAppUnits <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-webapp-webappunits.html>`__
    """

    props: PropsDictType = {
        "Provisioned": (integer, True),
    }


class WebApp(AWSObject):
    """
    `WebApp <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-transfer-webapp.html>`__
    """

    resource_type = "AWS::Transfer::WebApp"

    props: PropsDictType = {
        "AccessEndpoint": (str, False),
        "IdentityProviderDetails": (WebAppIdentityProviderDetails, True),
        "Tags": (Tags, False),
        "WebAppCustomization": (WebAppCustomization, False),
        "WebAppUnits": (WebAppUnits, False),
    }


class S3InputFileLocation(AWSProperty):
    """
    `S3InputFileLocation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-workflow-s3inputfilelocation.html>`__
    """

    props: PropsDictType = {
        "Bucket": (str, False),
        "Key": (str, False),
    }


class S3FileLocation(AWSProperty):
    """
    `S3FileLocation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-workflow-s3filelocation.html>`__
    """

    props: PropsDictType = {
        "S3FileLocation": (S3InputFileLocation, False),
    }


class CopyStepDetails(AWSProperty):
    """
    `CopyStepDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-workflow-copystepdetails.html>`__
    """

    props: PropsDictType = {
        "DestinationFileLocation": (S3FileLocation, False),
        "Name": (str, False),
        "OverwriteExisting": (str, False),
        "SourceFileLocation": (str, False),
    }


class CustomStepDetails(AWSProperty):
    """
    `CustomStepDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-workflow-customstepdetails.html>`__
    """

    props: PropsDictType = {
        "Name": (str, False),
        "SourceFileLocation": (str, False),
        "Target": (str, False),
        "TimeoutSeconds": (integer, False),
    }


class EfsInputFileLocation(AWSProperty):
    """
    `EfsInputFileLocation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-workflow-efsinputfilelocation.html>`__
    """

    props: PropsDictType = {
        "FileSystemId": (str, False),
        "Path": (str, False),
    }


class InputFileLocation(AWSProperty):
    """
    `InputFileLocation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-workflow-inputfilelocation.html>`__
    """

    props: PropsDictType = {
        "EfsFileLocation": (EfsInputFileLocation, False),
        "S3FileLocation": (S3InputFileLocation, False),
    }


class DecryptStepDetails(AWSProperty):
    """
    `DecryptStepDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-workflow-decryptstepdetails.html>`__
    """

    props: PropsDictType = {
        "DestinationFileLocation": (InputFileLocation, True),
        "Name": (str, False),
        "OverwriteExisting": (str, False),
        "SourceFileLocation": (str, False),
        "Type": (str, True),
    }


class DeleteStepDetails(AWSProperty):
    """
    `DeleteStepDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-workflow-deletestepdetails.html>`__
    """

    props: PropsDictType = {
        "Name": (str, False),
        "SourceFileLocation": (str, False),
    }


class S3Tag(AWSProperty):
    """
    `S3Tag <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-workflow-s3tag.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (str, True),
    }


class TagStepDetails(AWSProperty):
    """
    `TagStepDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-workflow-tagstepdetails.html>`__
    """

    props: PropsDictType = {
        "Name": (str, False),
        "SourceFileLocation": (str, False),
        "Tags": ([S3Tag], False),
    }


class WorkflowStep(AWSProperty):
    """
    `WorkflowStep <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-transfer-workflow-workflowstep.html>`__
    """

    props: PropsDictType = {
        "CopyStepDetails": (CopyStepDetails, False),
        "CustomStepDetails": (CustomStepDetails, False),
        "DecryptStepDetails": (DecryptStepDetails, False),
        "DeleteStepDetails": (DeleteStepDetails, False),
        "TagStepDetails": (TagStepDetails, False),
        "Type": (str, False),
    }


class Workflow(AWSObject):
    """
    `Workflow <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-transfer-workflow.html>`__
    """

    resource_type = "AWS::Transfer::Workflow"

    props: PropsDictType = {
        "Description": (str, False),
        "OnExceptionSteps": ([WorkflowStep], False),
        "Steps": ([WorkflowStep], True),
        "Tags": (Tags, False),
    }
