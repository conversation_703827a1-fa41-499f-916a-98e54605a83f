{"pagination": {"ListUsers": {"result_key": "Users", "output_token": "NextToken", "input_token": "NextToken", "limit_key": "MaxResults"}, "ListGroupMembers": {"result_key": "Members", "output_token": "NextToken", "input_token": "NextToken", "limit_key": "MaxResults"}, "ListOrganizations": {"result_key": "OrganizationSummaries", "output_token": "NextToken", "input_token": "NextToken", "limit_key": "MaxResults"}, "ListGroups": {"result_key": "Groups", "output_token": "NextToken", "input_token": "NextToken", "limit_key": "MaxResults"}, "ListResources": {"result_key": "Resources", "output_token": "NextToken", "input_token": "NextToken", "limit_key": "MaxResults"}, "ListAliases": {"result_key": "Aliases", "output_token": "NextToken", "input_token": "NextToken", "limit_key": "MaxResults"}, "ListMailboxPermissions": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Permissions"}, "ListResourceDelegates": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Delegates"}, "ListAvailabilityConfigurations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "AvailabilityConfigurations"}, "ListPersonalAccessTokens": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "PersonalAccessTokenSummaries"}}}