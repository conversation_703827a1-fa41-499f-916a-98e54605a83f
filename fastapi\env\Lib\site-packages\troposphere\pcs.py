# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType
from .validators import integer


class Networking(AWSProperty):
    """
    `Networking <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-pcs-cluster-networking.html>`__
    """

    props: PropsDictType = {
        "SecurityGroupIds": ([str], False),
        "SubnetIds": ([str], False),
    }


class Scheduler(AWSProperty):
    """
    `Scheduler <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-pcs-cluster-scheduler.html>`__
    """

    props: PropsDictType = {
        "Type": (str, True),
        "Version": (str, True),
    }


class SlurmCustomSetting(AWSProperty):
    """
    `SlurmCustomSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-pcs-computenodegroup-slurmcustomsetting.html>`__
    """

    props: PropsDictType = {
        "ParameterName": (str, True),
        "ParameterValue": (str, True),
    }


class SlurmConfiguration(AWSProperty):
    """
    `SlurmConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-pcs-computenodegroup-slurmconfiguration.html>`__
    """

    props: PropsDictType = {
        "SlurmCustomSettings": ([SlurmCustomSetting], False),
    }


class Cluster(AWSObject):
    """
    `Cluster <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-pcs-cluster.html>`__
    """

    resource_type = "AWS::PCS::Cluster"

    props: PropsDictType = {
        "Name": (str, False),
        "Networking": (Networking, True),
        "Scheduler": (Scheduler, True),
        "Size": (str, True),
        "SlurmConfiguration": (SlurmConfiguration, False),
        "Tags": (dict, False),
    }


class CustomLaunchTemplate(AWSProperty):
    """
    `CustomLaunchTemplate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-pcs-computenodegroup-customlaunchtemplate.html>`__
    """

    props: PropsDictType = {
        "TemplateId": (str, False),
        "Version": (str, True),
    }


class InstanceConfig(AWSProperty):
    """
    `InstanceConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-pcs-computenodegroup-instanceconfig.html>`__
    """

    props: PropsDictType = {
        "InstanceType": (str, False),
    }


class ScalingConfiguration(AWSProperty):
    """
    `ScalingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-pcs-computenodegroup-scalingconfiguration.html>`__
    """

    props: PropsDictType = {
        "MaxInstanceCount": (integer, True),
        "MinInstanceCount": (integer, True),
    }


class SpotOptions(AWSProperty):
    """
    `SpotOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-pcs-computenodegroup-spotoptions.html>`__
    """

    props: PropsDictType = {
        "AllocationStrategy": (str, False),
    }


class ComputeNodeGroup(AWSObject):
    """
    `ComputeNodeGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-pcs-computenodegroup.html>`__
    """

    resource_type = "AWS::PCS::ComputeNodeGroup"

    props: PropsDictType = {
        "AmiId": (str, False),
        "ClusterId": (str, True),
        "CustomLaunchTemplate": (CustomLaunchTemplate, True),
        "IamInstanceProfileArn": (str, True),
        "InstanceConfigs": ([InstanceConfig], True),
        "Name": (str, False),
        "PurchaseOption": (str, False),
        "ScalingConfiguration": (ScalingConfiguration, True),
        "SlurmConfiguration": (SlurmConfiguration, False),
        "SpotOptions": (SpotOptions, False),
        "SubnetIds": ([str], True),
        "Tags": (dict, False),
    }


class ComputeNodeGroupConfiguration(AWSProperty):
    """
    `ComputeNodeGroupConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-pcs-queue-computenodegroupconfiguration.html>`__
    """

    props: PropsDictType = {
        "ComputeNodeGroupId": (str, False),
    }


class Queue(AWSObject):
    """
    `Queue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-pcs-queue.html>`__
    """

    resource_type = "AWS::PCS::Queue"

    props: PropsDictType = {
        "ClusterId": (str, True),
        "ComputeNodeGroupConfigurations": ([ComputeNodeGroupConfiguration], False),
        "Name": (str, False),
        "Tags": (dict, False),
    }


class AuthKey(AWSProperty):
    """
    `AuthKey <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-pcs-cluster-authkey.html>`__
    """

    props: PropsDictType = {
        "SecretArn": (str, True),
        "SecretVersion": (str, True),
    }


class Endpoint(AWSProperty):
    """
    `Endpoint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-pcs-cluster-endpoint.html>`__
    """

    props: PropsDictType = {
        "Port": (str, True),
        "PrivateIpAddress": (str, True),
        "PublicIpAddress": (str, False),
        "Type": (str, True),
    }


class ErrorInfo(AWSProperty):
    """
    `ErrorInfo <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-pcs-queue-errorinfo.html>`__
    """

    props: PropsDictType = {
        "Code": (str, False),
        "Message": (str, False),
    }
