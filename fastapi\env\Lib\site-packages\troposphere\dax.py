# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType
from .validators import boolean, integer


class SSESpecification(AWSProperty):
    """
    `SSESpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dax-cluster-ssespecification.html>`__
    """

    props: PropsDictType = {
        "SSEEnabled": (boolean, False),
    }


class Cluster(AWSObject):
    """
    `Cluster <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-dax-cluster.html>`__
    """

    resource_type = "AWS::DAX::Cluster"

    props: PropsDictType = {
        "AvailabilityZones": ([str], False),
        "ClusterEndpointEncryptionType": (str, False),
        "ClusterName": (str, False),
        "Description": (str, False),
        "IAMRoleARN": (str, True),
        "NodeType": (str, True),
        "NotificationTopicARN": (str, False),
        "ParameterGroupName": (str, False),
        "PreferredMaintenanceWindow": (str, False),
        "ReplicationFactor": (integer, True),
        "SSESpecification": (SSESpecification, False),
        "SecurityGroupIds": ([str], False),
        "SubnetGroupName": (str, False),
        "Tags": (dict, False),
    }


class ParameterGroup(AWSObject):
    """
    `ParameterGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-dax-parametergroup.html>`__
    """

    resource_type = "AWS::DAX::ParameterGroup"

    props: PropsDictType = {
        "Description": (str, False),
        "ParameterGroupName": (str, False),
        "ParameterNameValues": (dict, False),
    }


class SubnetGroup(AWSObject):
    """
    `SubnetGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-dax-subnetgroup.html>`__
    """

    resource_type = "AWS::DAX::SubnetGroup"

    props: PropsDictType = {
        "Description": (str, False),
        "SubnetGroupName": (str, False),
        "SubnetIds": ([str], True),
    }
