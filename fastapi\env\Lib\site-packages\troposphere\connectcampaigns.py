# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double


class AgentlessDialerConfig(AWSProperty):
    """
    `AgentlessDialerConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaigns-campaign-agentlessdialerconfig.html>`__
    """

    props: PropsDictType = {
        "DialingCapacity": (double, False),
    }


class PredictiveDialerConfig(AWSProperty):
    """
    `PredictiveDialerConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaigns-campaign-predictivedialerconfig.html>`__
    """

    props: PropsDictType = {
        "BandwidthAllocation": (double, True),
        "DialingCapacity": (double, False),
    }


class ProgressiveDialerConfig(AWSProperty):
    """
    `ProgressiveDialerConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaigns-campaign-progressivedialerconfig.html>`__
    """

    props: PropsDictType = {
        "BandwidthAllocation": (double, True),
        "DialingCapacity": (double, False),
    }


class DialerConfig(AWSProperty):
    """
    `DialerConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaigns-campaign-dialerconfig.html>`__
    """

    props: PropsDictType = {
        "AgentlessDialerConfig": (AgentlessDialerConfig, False),
        "PredictiveDialerConfig": (PredictiveDialerConfig, False),
        "ProgressiveDialerConfig": (ProgressiveDialerConfig, False),
    }


class AnswerMachineDetectionConfig(AWSProperty):
    """
    `AnswerMachineDetectionConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaigns-campaign-answermachinedetectionconfig.html>`__
    """

    props: PropsDictType = {
        "AwaitAnswerMachinePrompt": (boolean, False),
        "EnableAnswerMachineDetection": (boolean, True),
    }


class OutboundCallConfig(AWSProperty):
    """
    `OutboundCallConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-connectcampaigns-campaign-outboundcallconfig.html>`__
    """

    props: PropsDictType = {
        "AnswerMachineDetectionConfig": (AnswerMachineDetectionConfig, False),
        "ConnectContactFlowArn": (str, True),
        "ConnectQueueArn": (str, False),
        "ConnectSourcePhoneNumber": (str, False),
    }


class Campaign(AWSObject):
    """
    `Campaign <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-connectcampaigns-campaign.html>`__
    """

    resource_type = "AWS::ConnectCampaigns::Campaign"

    props: PropsDictType = {
        "ConnectInstanceArn": (str, True),
        "DialerConfig": (DialerConfig, True),
        "Name": (str, True),
        "OutboundCallConfig": (OutboundCallConfig, True),
        "Tags": (Tags, False),
    }
