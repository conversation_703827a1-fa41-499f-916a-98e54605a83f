# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType


class DataQuery(AWSProperty):
    """
    `DataQuery <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bcmdataexports-export-dataquery.html>`__
    """

    props: PropsDictType = {
        "QueryStatement": (str, True),
        "TableConfigurations": (dict, False),
    }


class S3OutputConfigurations(AWSProperty):
    """
    `S3OutputConfigurations <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bcmdataexports-export-s3outputconfigurations.html>`__
    """

    props: PropsDictType = {
        "Compression": (str, True),
        "Format": (str, True),
        "OutputType": (str, True),
        "Overwrite": (str, True),
    }


class S3Destination(AWSProperty):
    """
    `S3Destination <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bcmdataexports-export-s3destination.html>`__
    """

    props: PropsDictType = {
        "S3Bucket": (str, True),
        "S3OutputConfigurations": (S3OutputConfigurations, True),
        "S3Prefix": (str, True),
        "S3Region": (str, True),
    }


class DestinationConfigurations(AWSProperty):
    """
    `DestinationConfigurations <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bcmdataexports-export-destinationconfigurations.html>`__
    """

    props: PropsDictType = {
        "S3Destination": (S3Destination, True),
    }


class RefreshCadence(AWSProperty):
    """
    `RefreshCadence <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bcmdataexports-export-refreshcadence.html>`__
    """

    props: PropsDictType = {
        "Frequency": (str, True),
    }


class ExportProperty(AWSProperty):
    """
    `ExportProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bcmdataexports-export-export.html>`__
    """

    props: PropsDictType = {
        "DataQuery": (DataQuery, True),
        "Description": (str, False),
        "DestinationConfigurations": (DestinationConfigurations, True),
        "ExportArn": (str, False),
        "Name": (str, True),
        "RefreshCadence": (RefreshCadence, True),
    }


class ResourceTag(AWSProperty):
    """
    `ResourceTag <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bcmdataexports-export-resourcetag.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (str, True),
    }


class Export(AWSObject):
    """
    `Export <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-bcmdataexports-export.html>`__
    """

    resource_type = "AWS::BCMDataExports::Export"

    props: PropsDictType = {
        "Export": (ExportProperty, True),
        "Tags": ([ResourceTag], False),
    }
