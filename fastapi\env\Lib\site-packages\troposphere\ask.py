# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType


class AuthenticationConfiguration(AWSProperty):
    """
    `AuthenticationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ask-skill-authenticationconfiguration.html>`__
    """

    props: PropsDictType = {
        "ClientId": (str, True),
        "ClientSecret": (str, True),
        "RefreshToken": (str, True),
    }


class Overrides(AWSProperty):
    """
    `Overrides <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ask-skill-overrides.html>`__
    """

    props: PropsDictType = {
        "Manifest": (dict, False),
    }


class SkillPackage(AWSProperty):
    """
    `SkillPackage <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ask-skill-skillpackage.html>`__
    """

    props: PropsDictType = {
        "Overrides": (Overrides, False),
        "S3Bucket": (str, True),
        "S3BucketRole": (str, False),
        "S3Key": (str, True),
        "S3ObjectVersion": (str, False),
    }


class Skill(AWSObject):
    """
    `Skill <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ask-skill.html>`__
    """

    resource_type = "Alexa::ASK::Skill"

    props: PropsDictType = {
        "AuthenticationConfiguration": (AuthenticationConfiguration, True),
        "SkillPackage": (SkillPackage, True),
        "VendorId": (str, True),
    }
