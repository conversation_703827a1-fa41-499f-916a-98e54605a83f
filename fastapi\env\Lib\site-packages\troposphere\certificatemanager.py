# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType
from .validators import integer
from .validators.certificatemanager import validate_tags_or_list


class ExpiryEventsConfiguration(AWSProperty):
    """
    `ExpiryEventsConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-certificatemanager-account-expiryeventsconfiguration.html>`__
    """

    props: PropsDictType = {
        "DaysBeforeExpiry": (integer, False),
    }


class Account(AWSObject):
    """
    `Account <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-certificatemanager-account.html>`__
    """

    resource_type = "AWS::CertificateManager::Account"

    props: PropsDictType = {
        "ExpiryEventsConfiguration": (ExpiryEventsConfiguration, True),
    }


class DomainValidationOption(AWSProperty):
    """
    `DomainValidationOption <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-certificatemanager-certificate-domainvalidationoption.html>`__
    """

    props: PropsDictType = {
        "DomainName": (str, True),
        "HostedZoneId": (str, False),
        "ValidationDomain": (str, False),
    }


class Certificate(AWSObject):
    """
    `Certificate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-certificatemanager-certificate.html>`__
    """

    resource_type = "AWS::CertificateManager::Certificate"

    props: PropsDictType = {
        "CertificateAuthorityArn": (str, False),
        "CertificateTransparencyLoggingPreference": (str, False),
        "DomainName": (str, True),
        "DomainValidationOptions": ([DomainValidationOption], False),
        "KeyAlgorithm": (str, False),
        "SubjectAlternativeNames": ([str], False),
        "Tags": (validate_tags_or_list, False),
        "ValidationMethod": (str, False),
    }
