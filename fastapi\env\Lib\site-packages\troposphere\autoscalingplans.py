# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType
from .validators import boolean, double, integer
from .validators.autoscalingplans import (
    scalable_dimension_type,
    service_namespace_type,
    statistic_type,
    validate_predictivescalingmaxcapacitybehavior,
    validate_predictivescalingmode,
    validate_scalingpolicyupdatebehavior,
)


class TagFilter(AWSProperty):
    """
    `TagFilter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-autoscalingplans-scalingplan-tagfilter.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Values": ([str], False),
    }


class ApplicationSource(AWSProperty):
    """
    `ApplicationSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-autoscalingplans-scalingplan-applicationsource.html>`__
    """

    props: PropsDictType = {
        "CloudFormationStackARN": (str, False),
        "TagFilters": ([TagFilter], False),
    }


class MetricDimension(AWSProperty):
    """
    `MetricDimension <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-autoscalingplans-scalingplan-metricdimension.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "Value": (str, True),
    }


class CustomizedLoadMetricSpecification(AWSProperty):
    """
    `CustomizedLoadMetricSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-autoscalingplans-scalingplan-customizedloadmetricspecification.html>`__
    """

    props: PropsDictType = {
        "Dimensions": ([MetricDimension], False),
        "MetricName": (str, True),
        "Namespace": (str, True),
        "Statistic": (str, True),
        "Unit": (str, False),
    }


class PredefinedLoadMetricSpecification(AWSProperty):
    """
    `PredefinedLoadMetricSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-autoscalingplans-scalingplan-predefinedloadmetricspecification.html>`__
    """

    props: PropsDictType = {
        "PredefinedLoadMetricType": (str, True),
        "ResourceLabel": (str, False),
    }


class CustomizedScalingMetricSpecification(AWSProperty):
    """
    `CustomizedScalingMetricSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-autoscalingplans-scalingplan-customizedscalingmetricspecification.html>`__
    """

    props: PropsDictType = {
        "Dimensions": ([MetricDimension], False),
        "MetricName": (str, True),
        "Namespace": (str, True),
        "Statistic": (statistic_type, True),
        "Unit": (str, False),
    }


class PredefinedScalingMetricSpecification(AWSProperty):
    """
    `PredefinedScalingMetricSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-autoscalingplans-scalingplan-predefinedscalingmetricspecification.html>`__
    """

    props: PropsDictType = {
        "PredefinedScalingMetricType": (str, True),
        "ResourceLabel": (str, False),
    }


class TargetTrackingConfiguration(AWSProperty):
    """
    `TargetTrackingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-autoscalingplans-scalingplan-targettrackingconfiguration.html>`__
    """

    props: PropsDictType = {
        "CustomizedScalingMetricSpecification": (
            CustomizedScalingMetricSpecification,
            False,
        ),
        "DisableScaleIn": (boolean, False),
        "EstimatedInstanceWarmup": (integer, False),
        "PredefinedScalingMetricSpecification": (
            PredefinedScalingMetricSpecification,
            False,
        ),
        "ScaleInCooldown": (integer, False),
        "ScaleOutCooldown": (integer, False),
        "TargetValue": (double, True),
    }


class ScalingInstruction(AWSProperty):
    """
    `ScalingInstruction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-autoscalingplans-scalingplan-scalinginstruction.html>`__
    """

    props: PropsDictType = {
        "CustomizedLoadMetricSpecification": (CustomizedLoadMetricSpecification, False),
        "DisableDynamicScaling": (boolean, False),
        "MaxCapacity": (integer, True),
        "MinCapacity": (integer, True),
        "PredefinedLoadMetricSpecification": (PredefinedLoadMetricSpecification, False),
        "PredictiveScalingMaxCapacityBehavior": (
            validate_predictivescalingmaxcapacitybehavior,
            False,
        ),
        "PredictiveScalingMaxCapacityBuffer": (integer, False),
        "PredictiveScalingMode": (validate_predictivescalingmode, False),
        "ResourceId": (str, True),
        "ScalableDimension": (scalable_dimension_type, True),
        "ScalingPolicyUpdateBehavior": (validate_scalingpolicyupdatebehavior, False),
        "ScheduledActionBufferTime": (integer, False),
        "ServiceNamespace": (service_namespace_type, True),
        "TargetTrackingConfigurations": ([TargetTrackingConfiguration], True),
    }


class ScalingPlan(AWSObject):
    """
    `ScalingPlan <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-autoscalingplans-scalingplan.html>`__
    """

    resource_type = "AWS::AutoScalingPlans::ScalingPlan"

    props: PropsDictType = {
        "ApplicationSource": (ApplicationSource, True),
        "ScalingInstructions": ([ScalingInstruction], True),
    }
