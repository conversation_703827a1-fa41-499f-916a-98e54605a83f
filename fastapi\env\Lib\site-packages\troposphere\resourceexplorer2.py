# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType


class DefaultViewAssociation(AWSObject):
    """
    `DefaultViewAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-resourceexplorer2-defaultviewassociation.html>`__
    """

    resource_type = "AWS::ResourceExplorer2::DefaultViewAssociation"

    props: PropsDictType = {
        "ViewArn": (str, True),
    }


class Index(AWSObject):
    """
    `Index <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-resourceexplorer2-index.html>`__
    """

    resource_type = "AWS::ResourceExplorer2::Index"

    props: PropsDictType = {
        "Tags": (dict, False),
        "Type": (str, True),
    }


class IncludedProperty(AWSProperty):
    """
    `IncludedProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-resourceexplorer2-view-includedproperty.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
    }


class SearchFilter(AWSProperty):
    """
    `SearchFilter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-resourceexplorer2-view-searchfilter.html>`__
    """

    props: PropsDictType = {
        "FilterString": (str, True),
    }


class View(AWSObject):
    """
    `View <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-resourceexplorer2-view.html>`__
    """

    resource_type = "AWS::ResourceExplorer2::View"

    props: PropsDictType = {
        "Filters": (SearchFilter, False),
        "IncludedProperties": ([IncludedProperty], False),
        "Scope": (str, False),
        "Tags": (dict, False),
        "ViewName": (str, True),
    }
