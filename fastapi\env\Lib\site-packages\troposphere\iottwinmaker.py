# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType
from .validators import boolean, double, integer
from .validators.iottwinmaker import validate_listvalue, validate_nestedtypel


class CompositeComponentType(AWSProperty):
    """
    `CompositeComponentType <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iottwinmaker-componenttype-compositecomponenttype.html>`__
    """

    props: PropsDictType = {
        "ComponentTypeId": (str, False),
    }


class LambdaFunction(AWSProperty):
    """
    `LambdaFunction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iottwinmaker-componenttype-lambdafunction.html>`__
    """

    props: PropsDictType = {
        "Arn": (str, True),
    }


class DataConnector(AWSProperty):
    """
    `DataConnector <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iottwinmaker-componenttype-dataconnector.html>`__
    """

    props: PropsDictType = {
        "IsNative": (boolean, False),
        "Lambda": (LambdaFunction, False),
    }


class Function(AWSProperty):
    """
    `Function <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iottwinmaker-componenttype-function.html>`__
    """

    props: PropsDictType = {
        "ImplementedBy": (DataConnector, False),
        "RequiredProperties": ([str], False),
        "Scope": (str, False),
    }


class RelationshipValue(AWSProperty):
    """
    `RelationshipValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iottwinmaker-entity-relationshipvalue.html>`__
    """

    props: PropsDictType = {
        "TargetComponentName": (str, False),
        "TargetEntityId": (str, False),
    }


class DataValue(AWSProperty):
    """
    `DataValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iottwinmaker-entity-datavalue.html>`__
    """

    props: PropsDictType = {
        "BooleanValue": (boolean, False),
        "DoubleValue": (double, False),
        "Expression": (str, False),
        "IntegerValue": (integer, False),
        "ListValue": (validate_listvalue, False),
        "LongValue": (double, False),
        "MapValue": (dict, False),
        "RelationshipValue": (RelationshipValue, False),
        "StringValue": (str, False),
    }


class Relationship(AWSProperty):
    """
    `Relationship <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iottwinmaker-entity-relationship.html>`__
    """

    props: PropsDictType = {
        "RelationshipType": (str, False),
        "TargetComponentTypeId": (str, False),
    }


class DataType(AWSProperty):
    """
    `DataType <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iottwinmaker-entity-datatype.html>`__
    """

    props: PropsDictType = {
        "AllowedValues": ([DataValue], False),
        "NestedType": (validate_nestedtypel, False),
        "Relationship": (Relationship, False),
        "Type": (str, False),
        "UnitOfMeasure": (str, False),
    }


class PropertyDefinition(AWSProperty):
    """
    `PropertyDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iottwinmaker-componenttype-propertydefinition.html>`__
    """

    props: PropsDictType = {
        "Configurations": (dict, False),
        "DataType": (DataType, False),
        "DefaultValue": (DataValue, False),
        "IsExternalId": (boolean, False),
        "IsRequiredInEntity": (boolean, False),
        "IsStoredExternally": (boolean, False),
        "IsTimeSeries": (boolean, False),
    }


class PropertyGroup(AWSProperty):
    """
    `PropertyGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iottwinmaker-entity-propertygroup.html>`__
    """

    props: PropsDictType = {
        "GroupType": (str, False),
        "PropertyNames": ([str], False),
    }


class ComponentType(AWSObject):
    """
    `ComponentType <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-iottwinmaker-componenttype.html>`__
    """

    resource_type = "AWS::IoTTwinMaker::ComponentType"

    props: PropsDictType = {
        "ComponentTypeId": (str, True),
        "CompositeComponentTypes": (dict, False),
        "Description": (str, False),
        "ExtendsFrom": ([str], False),
        "Functions": (dict, False),
        "IsSingleton": (boolean, False),
        "PropertyDefinitions": (dict, False),
        "PropertyGroups": (dict, False),
        "Tags": (dict, False),
        "WorkspaceId": (str, True),
    }


class Definition(AWSProperty):
    """
    `Definition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iottwinmaker-entity-definition.html>`__
    """

    props: PropsDictType = {
        "Configuration": (dict, False),
        "DataType": (DataType, False),
        "DefaultValue": (DataValue, False),
        "IsExternalId": (boolean, False),
        "IsFinal": (boolean, False),
        "IsImported": (boolean, False),
        "IsInherited": (boolean, False),
        "IsRequiredInEntity": (boolean, False),
        "IsStoredExternally": (boolean, False),
        "IsTimeSeries": (boolean, False),
    }


class Property(AWSProperty):
    """
    `Property <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iottwinmaker-entity-property.html>`__
    """

    props: PropsDictType = {
        "Definition": (Definition, False),
        "Value": (DataValue, False),
    }


class Error(AWSProperty):
    """
    `Error <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iottwinmaker-entity-error.html>`__
    """

    props: PropsDictType = {
        "Code": (str, False),
        "Message": (str, False),
    }


class Status(AWSProperty):
    """
    `Status <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iottwinmaker-entity-status.html>`__
    """

    props: PropsDictType = {
        "Error": (Error, False),
        "State": (str, False),
    }


class Component(AWSProperty):
    """
    `Component <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iottwinmaker-entity-component.html>`__
    """

    props: PropsDictType = {
        "ComponentName": (str, False),
        "ComponentTypeId": (str, False),
        "DefinedIn": (str, False),
        "Description": (str, False),
        "Properties": (dict, False),
        "PropertyGroups": (dict, False),
        "Status": (Status, False),
    }


class CompositeComponent(AWSProperty):
    """
    `CompositeComponent <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iottwinmaker-entity-compositecomponent.html>`__
    """

    props: PropsDictType = {
        "ComponentName": (str, False),
        "ComponentPath": (str, False),
        "ComponentTypeId": (str, False),
        "Description": (str, False),
        "Properties": (dict, False),
        "PropertyGroups": (dict, False),
        "Status": (Status, False),
    }


class Entity(AWSObject):
    """
    `Entity <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-iottwinmaker-entity.html>`__
    """

    resource_type = "AWS::IoTTwinMaker::Entity"

    props: PropsDictType = {
        "Components": (dict, False),
        "CompositeComponents": (dict, False),
        "Description": (str, False),
        "EntityId": (str, False),
        "EntityName": (str, True),
        "ParentEntityId": (str, False),
        "Tags": (dict, False),
        "WorkspaceId": (str, True),
    }


class Scene(AWSObject):
    """
    `Scene <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-iottwinmaker-scene.html>`__
    """

    resource_type = "AWS::IoTTwinMaker::Scene"

    props: PropsDictType = {
        "Capabilities": ([str], False),
        "ContentLocation": (str, True),
        "Description": (str, False),
        "SceneId": (str, True),
        "SceneMetadata": (dict, False),
        "Tags": (dict, False),
        "WorkspaceId": (str, True),
    }


class SyncJob(AWSObject):
    """
    `SyncJob <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-iottwinmaker-syncjob.html>`__
    """

    resource_type = "AWS::IoTTwinMaker::SyncJob"

    props: PropsDictType = {
        "SyncRole": (str, True),
        "SyncSource": (str, True),
        "Tags": (dict, False),
        "WorkspaceId": (str, True),
    }


class Workspace(AWSObject):
    """
    `Workspace <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-iottwinmaker-workspace.html>`__
    """

    resource_type = "AWS::IoTTwinMaker::Workspace"

    props: PropsDictType = {
        "Description": (str, False),
        "Role": (str, True),
        "S3Location": (str, True),
        "Tags": (dict, False),
        "WorkspaceId": (str, True),
    }
