# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType
from .validators import boolean, integer


class Connector(AWSProperty):
    """
    `Connector <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-connectordefinitionversion-connector.html>`__
    """

    props: PropsDictType = {
        "ConnectorArn": (str, True),
        "Id": (str, True),
        "Parameters": (dict, False),
    }


class ConnectorDefinitionVersionProperty(AWSProperty):
    """
    `ConnectorDefinitionVersionProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-connectordefinition-connectordefinitionversion.html>`__
    """

    props: PropsDictType = {
        "Connectors": ([Connector], True),
    }


class ConnectorDefinition(AWSObject):
    """
    `ConnectorDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-greengrass-connectordefinition.html>`__
    """

    resource_type = "AWS::Greengrass::ConnectorDefinition"

    props: PropsDictType = {
        "InitialVersion": (ConnectorDefinitionVersionProperty, False),
        "Name": (str, True),
        "Tags": (dict, False),
    }


class ConnectorDefinitionVersion(AWSObject):
    """
    `ConnectorDefinitionVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-greengrass-connectordefinitionversion.html>`__
    """

    resource_type = "AWS::Greengrass::ConnectorDefinitionVersion"

    props: PropsDictType = {
        "ConnectorDefinitionId": (str, True),
        "Connectors": ([Connector], True),
    }


class Core(AWSProperty):
    """
    `Core <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-coredefinitionversion-core.html>`__
    """

    props: PropsDictType = {
        "CertificateArn": (str, True),
        "Id": (str, True),
        "SyncShadow": (boolean, False),
        "ThingArn": (str, True),
    }


class CoreDefinitionVersionProperty(AWSProperty):
    """
    `CoreDefinitionVersionProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-coredefinition-coredefinitionversion.html>`__
    """

    props: PropsDictType = {
        "Cores": ([Core], True),
    }


class CoreDefinition(AWSObject):
    """
    `CoreDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-greengrass-coredefinition.html>`__
    """

    resource_type = "AWS::Greengrass::CoreDefinition"

    props: PropsDictType = {
        "InitialVersion": (CoreDefinitionVersionProperty, False),
        "Name": (str, True),
        "Tags": (dict, False),
    }


class CoreDefinitionVersion(AWSObject):
    """
    `CoreDefinitionVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-greengrass-coredefinitionversion.html>`__
    """

    resource_type = "AWS::Greengrass::CoreDefinitionVersion"

    props: PropsDictType = {
        "CoreDefinitionId": (str, True),
        "Cores": ([Core], True),
    }


class Device(AWSProperty):
    """
    `Device <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-devicedefinitionversion-device.html>`__
    """

    props: PropsDictType = {
        "CertificateArn": (str, True),
        "Id": (str, True),
        "SyncShadow": (boolean, False),
        "ThingArn": (str, True),
    }


class DeviceDefinitionVersionProperty(AWSProperty):
    """
    `DeviceDefinitionVersionProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-devicedefinition-devicedefinitionversion.html>`__
    """

    props: PropsDictType = {
        "Devices": ([Device], True),
    }


class DeviceDefinition(AWSObject):
    """
    `DeviceDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-greengrass-devicedefinition.html>`__
    """

    resource_type = "AWS::Greengrass::DeviceDefinition"

    props: PropsDictType = {
        "InitialVersion": (DeviceDefinitionVersionProperty, False),
        "Name": (str, True),
        "Tags": (dict, False),
    }


class DeviceDefinitionVersion(AWSObject):
    """
    `DeviceDefinitionVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-greengrass-devicedefinitionversion.html>`__
    """

    resource_type = "AWS::Greengrass::DeviceDefinitionVersion"

    props: PropsDictType = {
        "DeviceDefinitionId": (str, True),
        "Devices": ([Device], True),
    }


class RunAs(AWSProperty):
    """
    `RunAs <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-functiondefinitionversion-runas.html>`__
    """

    props: PropsDictType = {
        "Gid": (integer, False),
        "Uid": (integer, False),
    }


class Execution(AWSProperty):
    """
    `Execution <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-functiondefinitionversion-execution.html>`__
    """

    props: PropsDictType = {
        "IsolationMode": (str, False),
        "RunAs": (RunAs, False),
    }


class DefaultConfig(AWSProperty):
    """
    `DefaultConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-functiondefinitionversion-defaultconfig.html>`__
    """

    props: PropsDictType = {
        "Execution": (Execution, True),
    }


class ResourceAccessPolicy(AWSProperty):
    """
    `ResourceAccessPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-functiondefinitionversion-resourceaccesspolicy.html>`__
    """

    props: PropsDictType = {
        "Permission": (str, False),
        "ResourceId": (str, True),
    }


class Environment(AWSProperty):
    """
    `Environment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-functiondefinitionversion-environment.html>`__
    """

    props: PropsDictType = {
        "AccessSysfs": (boolean, False),
        "Execution": (Execution, False),
        "ResourceAccessPolicies": ([ResourceAccessPolicy], False),
        "Variables": (dict, False),
    }


class FunctionConfiguration(AWSProperty):
    """
    `FunctionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-functiondefinitionversion-functionconfiguration.html>`__
    """

    props: PropsDictType = {
        "EncodingType": (str, False),
        "Environment": (Environment, False),
        "ExecArgs": (str, False),
        "Executable": (str, False),
        "MemorySize": (integer, False),
        "Pinned": (boolean, False),
        "Timeout": (integer, False),
    }


class Function(AWSProperty):
    """
    `Function <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-functiondefinitionversion-function.html>`__
    """

    props: PropsDictType = {
        "FunctionArn": (str, True),
        "FunctionConfiguration": (FunctionConfiguration, True),
        "Id": (str, True),
    }


class FunctionDefinitionVersionProperty(AWSProperty):
    """
    `FunctionDefinitionVersionProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-functiondefinition-functiondefinitionversion.html>`__
    """

    props: PropsDictType = {
        "DefaultConfig": (DefaultConfig, False),
        "Functions": ([Function], True),
    }


class FunctionDefinition(AWSObject):
    """
    `FunctionDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-greengrass-functiondefinition.html>`__
    """

    resource_type = "AWS::Greengrass::FunctionDefinition"

    props: PropsDictType = {
        "InitialVersion": (FunctionDefinitionVersionProperty, False),
        "Name": (str, True),
        "Tags": (dict, False),
    }


class FunctionDefinitionVersion(AWSObject):
    """
    `FunctionDefinitionVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-greengrass-functiondefinitionversion.html>`__
    """

    resource_type = "AWS::Greengrass::FunctionDefinitionVersion"

    props: PropsDictType = {
        "DefaultConfig": (DefaultConfig, False),
        "FunctionDefinitionId": (str, True),
        "Functions": ([Function], True),
    }


class GroupVersionProperty(AWSProperty):
    """
    `GroupVersionProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-group-groupversion.html>`__
    """

    props: PropsDictType = {
        "ConnectorDefinitionVersionArn": (str, False),
        "CoreDefinitionVersionArn": (str, False),
        "DeviceDefinitionVersionArn": (str, False),
        "FunctionDefinitionVersionArn": (str, False),
        "LoggerDefinitionVersionArn": (str, False),
        "ResourceDefinitionVersionArn": (str, False),
        "SubscriptionDefinitionVersionArn": (str, False),
    }


class Group(AWSObject):
    """
    `Group <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-greengrass-group.html>`__
    """

    resource_type = "AWS::Greengrass::Group"

    props: PropsDictType = {
        "InitialVersion": (GroupVersionProperty, False),
        "Name": (str, True),
        "RoleArn": (str, False),
        "Tags": (dict, False),
    }


class GroupVersion(AWSObject):
    """
    `GroupVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-greengrass-groupversion.html>`__
    """

    resource_type = "AWS::Greengrass::GroupVersion"

    props: PropsDictType = {
        "ConnectorDefinitionVersionArn": (str, False),
        "CoreDefinitionVersionArn": (str, False),
        "DeviceDefinitionVersionArn": (str, False),
        "FunctionDefinitionVersionArn": (str, False),
        "GroupId": (str, True),
        "LoggerDefinitionVersionArn": (str, False),
        "ResourceDefinitionVersionArn": (str, False),
        "SubscriptionDefinitionVersionArn": (str, False),
    }


class Logger(AWSProperty):
    """
    `Logger <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-loggerdefinitionversion-logger.html>`__
    """

    props: PropsDictType = {
        "Component": (str, True),
        "Id": (str, True),
        "Level": (str, True),
        "Space": (integer, False),
        "Type": (str, True),
    }


class LoggerDefinitionVersionProperty(AWSProperty):
    """
    `LoggerDefinitionVersionProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-loggerdefinition-loggerdefinitionversion.html>`__
    """

    props: PropsDictType = {
        "Loggers": ([Logger], True),
    }


class LoggerDefinition(AWSObject):
    """
    `LoggerDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-greengrass-loggerdefinition.html>`__
    """

    resource_type = "AWS::Greengrass::LoggerDefinition"

    props: PropsDictType = {
        "InitialVersion": (LoggerDefinitionVersionProperty, False),
        "Name": (str, True),
        "Tags": (dict, False),
    }


class LoggerDefinitionVersion(AWSObject):
    """
    `LoggerDefinitionVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-greengrass-loggerdefinitionversion.html>`__
    """

    resource_type = "AWS::Greengrass::LoggerDefinitionVersion"

    props: PropsDictType = {
        "LoggerDefinitionId": (str, True),
        "Loggers": ([Logger], True),
    }


class GroupOwnerSetting(AWSProperty):
    """
    `GroupOwnerSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-resourcedefinitionversion-groupownersetting.html>`__
    """

    props: PropsDictType = {
        "AutoAddGroupOwner": (boolean, True),
        "GroupOwner": (str, False),
    }


class LocalDeviceResourceData(AWSProperty):
    """
    `LocalDeviceResourceData <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-resourcedefinitionversion-localdeviceresourcedata.html>`__
    """

    props: PropsDictType = {
        "GroupOwnerSetting": (GroupOwnerSetting, False),
        "SourcePath": (str, True),
    }


class LocalVolumeResourceData(AWSProperty):
    """
    `LocalVolumeResourceData <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-resourcedefinitionversion-localvolumeresourcedata.html>`__
    """

    props: PropsDictType = {
        "DestinationPath": (str, True),
        "GroupOwnerSetting": (GroupOwnerSetting, False),
        "SourcePath": (str, True),
    }


class ResourceDownloadOwnerSetting(AWSProperty):
    """
    `ResourceDownloadOwnerSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-resourcedefinitionversion-resourcedownloadownersetting.html>`__
    """

    props: PropsDictType = {
        "GroupOwner": (str, True),
        "GroupPermission": (str, True),
    }


class S3MachineLearningModelResourceData(AWSProperty):
    """
    `S3MachineLearningModelResourceData <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-resourcedefinitionversion-s3machinelearningmodelresourcedata.html>`__
    """

    props: PropsDictType = {
        "DestinationPath": (str, True),
        "OwnerSetting": (ResourceDownloadOwnerSetting, False),
        "S3Uri": (str, True),
    }


class SageMakerMachineLearningModelResourceData(AWSProperty):
    """
    `SageMakerMachineLearningModelResourceData <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-resourcedefinitionversion-sagemakermachinelearningmodelresourcedata.html>`__
    """

    props: PropsDictType = {
        "DestinationPath": (str, True),
        "OwnerSetting": (ResourceDownloadOwnerSetting, False),
        "SageMakerJobArn": (str, True),
    }


class SecretsManagerSecretResourceData(AWSProperty):
    """
    `SecretsManagerSecretResourceData <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-resourcedefinitionversion-secretsmanagersecretresourcedata.html>`__
    """

    props: PropsDictType = {
        "ARN": (str, True),
        "AdditionalStagingLabelsToDownload": ([str], False),
    }


class ResourceDataContainer(AWSProperty):
    """
    `ResourceDataContainer <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-resourcedefinitionversion-resourcedatacontainer.html>`__
    """

    props: PropsDictType = {
        "LocalDeviceResourceData": (LocalDeviceResourceData, False),
        "LocalVolumeResourceData": (LocalVolumeResourceData, False),
        "S3MachineLearningModelResourceData": (
            S3MachineLearningModelResourceData,
            False,
        ),
        "SageMakerMachineLearningModelResourceData": (
            SageMakerMachineLearningModelResourceData,
            False,
        ),
        "SecretsManagerSecretResourceData": (SecretsManagerSecretResourceData, False),
    }


class ResourceInstance(AWSProperty):
    """
    `ResourceInstance <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-resourcedefinitionversion-resourceinstance.html>`__
    """

    props: PropsDictType = {
        "Id": (str, True),
        "Name": (str, True),
        "ResourceDataContainer": (ResourceDataContainer, True),
    }


class ResourceDefinitionVersionProperty(AWSProperty):
    """
    `ResourceDefinitionVersionProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-resourcedefinition-resourcedefinitionversion.html>`__
    """

    props: PropsDictType = {
        "Resources": ([ResourceInstance], True),
    }


class ResourceDefinition(AWSObject):
    """
    `ResourceDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-greengrass-resourcedefinition.html>`__
    """

    resource_type = "AWS::Greengrass::ResourceDefinition"

    props: PropsDictType = {
        "InitialVersion": (ResourceDefinitionVersionProperty, False),
        "Name": (str, True),
        "Tags": (dict, False),
    }


class ResourceDefinitionVersion(AWSObject):
    """
    `ResourceDefinitionVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-greengrass-resourcedefinitionversion.html>`__
    """

    resource_type = "AWS::Greengrass::ResourceDefinitionVersion"

    props: PropsDictType = {
        "ResourceDefinitionId": (str, True),
        "Resources": ([ResourceInstance], True),
    }


class Subscription(AWSProperty):
    """
    `Subscription <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-subscriptiondefinitionversion-subscription.html>`__
    """

    props: PropsDictType = {
        "Id": (str, True),
        "Source": (str, True),
        "Subject": (str, True),
        "Target": (str, True),
    }


class SubscriptionDefinitionVersionProperty(AWSProperty):
    """
    `SubscriptionDefinitionVersionProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-greengrass-subscriptiondefinition-subscriptiondefinitionversion.html>`__
    """

    props: PropsDictType = {
        "Subscriptions": ([Subscription], True),
    }


class SubscriptionDefinition(AWSObject):
    """
    `SubscriptionDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-greengrass-subscriptiondefinition.html>`__
    """

    resource_type = "AWS::Greengrass::SubscriptionDefinition"

    props: PropsDictType = {
        "InitialVersion": (SubscriptionDefinitionVersionProperty, False),
        "Name": (str, True),
        "Tags": (dict, False),
    }


class SubscriptionDefinitionVersion(AWSObject):
    """
    `SubscriptionDefinitionVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-greengrass-subscriptiondefinitionversion.html>`__
    """

    resource_type = "AWS::Greengrass::SubscriptionDefinitionVersion"

    props: PropsDictType = {
        "SubscriptionDefinitionId": (str, True),
        "Subscriptions": ([Subscription], True),
    }
