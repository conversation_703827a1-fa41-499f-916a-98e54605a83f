# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import double


class BrowserSettings(AWSObject):
    """
    `BrowserSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-workspacesweb-browsersettings.html>`__
    """

    resource_type = "AWS::WorkSpacesWeb::BrowserSettings"

    props: PropsDictType = {
        "AdditionalEncryptionContext": (dict, False),
        "BrowserPolicy": (str, False),
        "CustomerManagedKey": (str, False),
        "Tags": (Tags, False),
    }


class CustomPattern(AWSProperty):
    """
    `CustomPattern <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-workspacesweb-dataprotectionsettings-custompattern.html>`__
    """

    props: PropsDictType = {
        "KeywordRegex": (str, False),
        "PatternDescription": (str, False),
        "PatternName": (str, True),
        "PatternRegex": (str, True),
    }


class RedactionPlaceHolder(AWSProperty):
    """
    `RedactionPlaceHolder <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-workspacesweb-dataprotectionsettings-redactionplaceholder.html>`__
    """

    props: PropsDictType = {
        "RedactionPlaceHolderText": (str, False),
        "RedactionPlaceHolderType": (str, True),
    }


class InlineRedactionPattern(AWSProperty):
    """
    `InlineRedactionPattern <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-workspacesweb-dataprotectionsettings-inlineredactionpattern.html>`__
    """

    props: PropsDictType = {
        "BuiltInPatternId": (str, False),
        "ConfidenceLevel": (double, False),
        "CustomPattern": (CustomPattern, False),
        "EnforcedUrls": ([str], False),
        "ExemptUrls": ([str], False),
        "RedactionPlaceHolder": (RedactionPlaceHolder, True),
    }


class InlineRedactionConfiguration(AWSProperty):
    """
    `InlineRedactionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-workspacesweb-dataprotectionsettings-inlineredactionconfiguration.html>`__
    """

    props: PropsDictType = {
        "GlobalConfidenceLevel": (double, False),
        "GlobalEnforcedUrls": ([str], False),
        "GlobalExemptUrls": ([str], False),
        "InlineRedactionPatterns": ([InlineRedactionPattern], True),
    }


class DataProtectionSettings(AWSObject):
    """
    `DataProtectionSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-workspacesweb-dataprotectionsettings.html>`__
    """

    resource_type = "AWS::WorkSpacesWeb::DataProtectionSettings"

    props: PropsDictType = {
        "AdditionalEncryptionContext": (dict, False),
        "CustomerManagedKey": (str, False),
        "Description": (str, False),
        "DisplayName": (str, False),
        "InlineRedactionConfiguration": (InlineRedactionConfiguration, False),
        "Tags": (Tags, False),
    }


class IdentityProvider(AWSObject):
    """
    `IdentityProvider <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-workspacesweb-identityprovider.html>`__
    """

    resource_type = "AWS::WorkSpacesWeb::IdentityProvider"

    props: PropsDictType = {
        "IdentityProviderDetails": (dict, True),
        "IdentityProviderName": (str, True),
        "IdentityProviderType": (str, True),
        "PortalArn": (str, False),
        "Tags": (Tags, False),
    }


class IpRule(AWSProperty):
    """
    `IpRule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-workspacesweb-ipaccesssettings-iprule.html>`__
    """

    props: PropsDictType = {
        "Description": (str, False),
        "IpRange": (str, True),
    }


class IpAccessSettings(AWSObject):
    """
    `IpAccessSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-workspacesweb-ipaccesssettings.html>`__
    """

    resource_type = "AWS::WorkSpacesWeb::IpAccessSettings"

    props: PropsDictType = {
        "AdditionalEncryptionContext": (dict, False),
        "CustomerManagedKey": (str, False),
        "Description": (str, False),
        "DisplayName": (str, False),
        "IpRules": ([IpRule], True),
        "Tags": (Tags, False),
    }


class NetworkSettings(AWSObject):
    """
    `NetworkSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-workspacesweb-networksettings.html>`__
    """

    resource_type = "AWS::WorkSpacesWeb::NetworkSettings"

    props: PropsDictType = {
        "SecurityGroupIds": ([str], True),
        "SubnetIds": ([str], True),
        "Tags": (Tags, False),
        "VpcId": (str, True),
    }


class Portal(AWSObject):
    """
    `Portal <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-workspacesweb-portal.html>`__
    """

    resource_type = "AWS::WorkSpacesWeb::Portal"

    props: PropsDictType = {
        "AdditionalEncryptionContext": (dict, False),
        "AuthenticationType": (str, False),
        "BrowserSettingsArn": (str, False),
        "CustomerManagedKey": (str, False),
        "DataProtectionSettingsArn": (str, False),
        "DisplayName": (str, False),
        "InstanceType": (str, False),
        "IpAccessSettingsArn": (str, False),
        "MaxConcurrentSessions": (double, False),
        "NetworkSettingsArn": (str, False),
        "Tags": (Tags, False),
        "TrustStoreArn": (str, False),
        "UserAccessLoggingSettingsArn": (str, False),
        "UserSettingsArn": (str, False),
    }


class TrustStore(AWSObject):
    """
    `TrustStore <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-workspacesweb-truststore.html>`__
    """

    resource_type = "AWS::WorkSpacesWeb::TrustStore"

    props: PropsDictType = {
        "CertificateList": ([str], True),
        "Tags": (Tags, False),
    }


class UserAccessLoggingSettings(AWSObject):
    """
    `UserAccessLoggingSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-workspacesweb-useraccessloggingsettings.html>`__
    """

    resource_type = "AWS::WorkSpacesWeb::UserAccessLoggingSettings"

    props: PropsDictType = {
        "KinesisStreamArn": (str, True),
        "Tags": (Tags, False),
    }


class CookieSpecification(AWSProperty):
    """
    `CookieSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-workspacesweb-usersettings-cookiespecification.html>`__
    """

    props: PropsDictType = {
        "Domain": (str, True),
        "Name": (str, False),
        "Path": (str, False),
    }


class CookieSynchronizationConfiguration(AWSProperty):
    """
    `CookieSynchronizationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-workspacesweb-usersettings-cookiesynchronizationconfiguration.html>`__
    """

    props: PropsDictType = {
        "Allowlist": ([CookieSpecification], True),
        "Blocklist": ([CookieSpecification], False),
    }


class ToolbarConfiguration(AWSProperty):
    """
    `ToolbarConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-workspacesweb-usersettings-toolbarconfiguration.html>`__
    """

    props: PropsDictType = {
        "HiddenToolbarItems": ([str], False),
        "MaxDisplayResolution": (str, False),
        "ToolbarType": (str, False),
        "VisualMode": (str, False),
    }


class UserSettings(AWSObject):
    """
    `UserSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-workspacesweb-usersettings.html>`__
    """

    resource_type = "AWS::WorkSpacesWeb::UserSettings"

    props: PropsDictType = {
        "AdditionalEncryptionContext": (dict, False),
        "CookieSynchronizationConfiguration": (
            CookieSynchronizationConfiguration,
            False,
        ),
        "CopyAllowed": (str, True),
        "CustomerManagedKey": (str, False),
        "DeepLinkAllowed": (str, False),
        "DisconnectTimeoutInMinutes": (double, False),
        "DownloadAllowed": (str, True),
        "IdleDisconnectTimeoutInMinutes": (double, False),
        "PasteAllowed": (str, True),
        "PrintAllowed": (str, True),
        "Tags": (Tags, False),
        "ToolbarConfiguration": (ToolbarConfiguration, False),
        "UploadAllowed": (str, True),
    }
