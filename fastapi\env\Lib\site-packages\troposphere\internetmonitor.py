# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer


class LocalHealthEventsConfig(AWSProperty):
    """
    `LocalHealthEventsConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-internetmonitor-monitor-localhealtheventsconfig.html>`__
    """

    props: PropsDictType = {
        "HealthScoreThreshold": (double, False),
        "MinTrafficImpact": (double, False),
        "Status": (str, False),
    }


class HealthEventsConfig(AWSProperty):
    """
    `HealthEventsConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-internetmonitor-monitor-healtheventsconfig.html>`__
    """

    props: PropsDictType = {
        "AvailabilityLocalHealthEventsConfig": (LocalHealthEventsConfig, False),
        "AvailabilityScoreThreshold": (double, False),
        "PerformanceLocalHealthEventsConfig": (LocalHealthEventsConfig, False),
        "PerformanceScoreThreshold": (double, False),
    }


class S3Config(AWSProperty):
    """
    `S3Config <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-internetmonitor-monitor-s3config.html>`__
    """

    props: PropsDictType = {
        "BucketName": (str, False),
        "BucketPrefix": (str, False),
        "LogDeliveryStatus": (str, False),
    }


class InternetMeasurementsLogDelivery(AWSProperty):
    """
    `InternetMeasurementsLogDelivery <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-internetmonitor-monitor-internetmeasurementslogdelivery.html>`__
    """

    props: PropsDictType = {
        "S3Config": (S3Config, False),
    }


class Monitor(AWSObject):
    """
    `Monitor <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-internetmonitor-monitor.html>`__
    """

    resource_type = "AWS::InternetMonitor::Monitor"

    props: PropsDictType = {
        "HealthEventsConfig": (HealthEventsConfig, False),
        "IncludeLinkedAccounts": (boolean, False),
        "InternetMeasurementsLogDelivery": (InternetMeasurementsLogDelivery, False),
        "LinkedAccountId": (str, False),
        "MaxCityNetworksToMonitor": (integer, False),
        "MonitorName": (str, True),
        "Resources": ([str], False),
        "ResourcesToAdd": ([str], False),
        "ResourcesToRemove": ([str], False),
        "Status": (str, False),
        "Tags": (Tags, False),
        "TrafficPercentageToMonitor": (integer, False),
    }
