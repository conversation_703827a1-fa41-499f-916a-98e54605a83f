# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import integer


class AwsLogSource(AWSObject):
    """
    `AwsLogSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-securitylake-awslogsource.html>`__
    """

    resource_type = "AWS::SecurityLake::AwsLogSource"

    props: PropsDictType = {
        "Accounts": ([str], False),
        "DataLakeArn": (str, True),
        "SourceName": (str, True),
        "SourceVersion": (str, True),
    }


class EncryptionConfiguration(AWSProperty):
    """
    `EncryptionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-securitylake-datalake-encryptionconfiguration.html>`__
    """

    props: PropsDictType = {
        "KmsKeyId": (str, False),
    }


class Expiration(AWSProperty):
    """
    `Expiration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-securitylake-datalake-expiration.html>`__
    """

    props: PropsDictType = {
        "Days": (integer, False),
    }


class Transitions(AWSProperty):
    """
    `Transitions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-securitylake-datalake-transitions.html>`__
    """

    props: PropsDictType = {
        "Days": (integer, False),
        "StorageClass": (str, False),
    }


class LifecycleConfiguration(AWSProperty):
    """
    `LifecycleConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-securitylake-datalake-lifecycleconfiguration.html>`__
    """

    props: PropsDictType = {
        "Expiration": (Expiration, False),
        "Transitions": ([Transitions], False),
    }


class ReplicationConfiguration(AWSProperty):
    """
    `ReplicationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-securitylake-datalake-replicationconfiguration.html>`__
    """

    props: PropsDictType = {
        "Regions": ([str], False),
        "RoleArn": (str, False),
    }


class DataLake(AWSObject):
    """
    `DataLake <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-securitylake-datalake.html>`__
    """

    resource_type = "AWS::SecurityLake::DataLake"

    props: PropsDictType = {
        "EncryptionConfiguration": (EncryptionConfiguration, False),
        "LifecycleConfiguration": (LifecycleConfiguration, False),
        "MetaStoreManagerRoleArn": (str, False),
        "ReplicationConfiguration": (ReplicationConfiguration, False),
        "Tags": (Tags, False),
    }


class AwsLogSourceProperty(AWSProperty):
    """
    `AwsLogSourceProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-securitylake-subscriber-awslogsource.html>`__
    """

    props: PropsDictType = {
        "SourceName": (str, False),
        "SourceVersion": (str, False),
    }


class CustomLogSource(AWSProperty):
    """
    `CustomLogSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-securitylake-subscriber-customlogsource.html>`__
    """

    props: PropsDictType = {
        "SourceName": (str, False),
        "SourceVersion": (str, False),
    }


class Source(AWSProperty):
    """
    `Source <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-securitylake-subscriber-source.html>`__
    """

    props: PropsDictType = {
        "AwsLogSource": (AwsLogSourceProperty, False),
        "CustomLogSource": (CustomLogSource, False),
    }


class SubscriberIdentity(AWSProperty):
    """
    `SubscriberIdentity <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-securitylake-subscriber-subscriberidentity.html>`__
    """

    props: PropsDictType = {
        "ExternalId": (str, True),
        "Principal": (str, True),
    }


class Subscriber(AWSObject):
    """
    `Subscriber <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-securitylake-subscriber.html>`__
    """

    resource_type = "AWS::SecurityLake::Subscriber"

    props: PropsDictType = {
        "AccessTypes": ([str], True),
        "DataLakeArn": (str, True),
        "Sources": ([Source], True),
        "SubscriberDescription": (str, False),
        "SubscriberIdentity": (SubscriberIdentity, True),
        "SubscriberName": (str, True),
        "Tags": (Tags, False),
    }


class HttpsNotificationConfiguration(AWSProperty):
    """
    `HttpsNotificationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-securitylake-subscribernotification-httpsnotificationconfiguration.html>`__
    """

    props: PropsDictType = {
        "AuthorizationApiKeyName": (str, False),
        "AuthorizationApiKeyValue": (str, False),
        "Endpoint": (str, True),
        "HttpMethod": (str, False),
        "TargetRoleArn": (str, True),
    }


class NotificationConfiguration(AWSProperty):
    """
    `NotificationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-securitylake-subscribernotification-notificationconfiguration.html>`__
    """

    props: PropsDictType = {
        "HttpsNotificationConfiguration": (HttpsNotificationConfiguration, False),
        "SqsNotificationConfiguration": (dict, False),
    }


class SubscriberNotification(AWSObject):
    """
    `SubscriberNotification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-securitylake-subscribernotification.html>`__
    """

    resource_type = "AWS::SecurityLake::SubscriberNotification"

    props: PropsDictType = {
        "NotificationConfiguration": (NotificationConfiguration, True),
        "SubscriberArn": (str, True),
    }
