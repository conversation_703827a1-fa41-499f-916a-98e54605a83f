# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType
from .validators import boolean


class AwsLambda(AWSProperty):
    """
    `AwsLambda <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3objectlambda-accesspoint-awslambda.html>`__
    """

    props: PropsDictType = {
        "FunctionArn": (str, True),
        "FunctionPayload": (str, False),
    }


class ContentTransformation(AWSProperty):
    """
    `ContentTransformation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3objectlambda-accesspoint-contenttransformation.html>`__
    """

    props: PropsDictType = {
        "AwsLambda": (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>),
    }


class TransformationConfiguration(AWSProperty):
    """
    `TransformationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3objectlambda-accesspoint-transformationconfiguration.html>`__
    """

    props: PropsDictType = {
        "Actions": ([str], True),
        "ContentTransformation": (ContentTransformation, True),
    }


class ObjectLambdaConfiguration(AWSProperty):
    """
    `ObjectLambdaConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3objectlambda-accesspoint-objectlambdaconfiguration.html>`__
    """

    props: PropsDictType = {
        "AllowedFeatures": ([str], False),
        "CloudWatchMetricsEnabled": (boolean, False),
        "SupportingAccessPoint": (str, True),
        "TransformationConfigurations": ([TransformationConfiguration], True),
    }


class AccessPoint(AWSObject):
    """
    `AccessPoint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3objectlambda-accesspoint.html>`__
    """

    resource_type = "AWS::S3ObjectLambda::AccessPoint"

    props: PropsDictType = {
        "Name": (str, False),
        "ObjectLambdaConfiguration": (ObjectLambdaConfiguration, True),
    }


class AccessPointPolicy(AWSObject):
    """
    `AccessPointPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3objectlambda-accesspointpolicy.html>`__
    """

    resource_type = "AWS::S3ObjectLambda::AccessPointPolicy"

    props: PropsDictType = {
        "ObjectLambdaAccessPoint": (str, True),
        "PolicyDocument": (dict, True),
    }


class Alias(AWSProperty):
    """
    `Alias <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3objectlambda-accesspoint-alias.html>`__
    """

    props: PropsDictType = {
        "Status": (str, False),
        "Value": (str, True),
    }


class PublicAccessBlockConfiguration(AWSProperty):
    """
    `PublicAccessBlockConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3objectlambda-accesspoint-publicaccessblockconfiguration.html>`__
    """

    props: PropsDictType = {
        "BlockPublicAcls": (boolean, False),
        "BlockPublicPolicy": (boolean, False),
        "IgnorePublicAcls": (boolean, False),
        "RestrictPublicBuckets": (boolean, False),
    }
