# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer


class CustomerManagedS3(AWSProperty):
    """
    `CustomerManagedS3 <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-datastore-customermanageds3.html>`__
    """

    props: PropsDictType = {
        "Bucket": (str, True),
        "KeyPrefix": (str, False),
        "RoleArn": (str, True),
    }


class ChannelStorage(AWSProperty):
    """
    `ChannelStorage <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-channel-channelstorage.html>`__
    """

    props: PropsDictType = {
        "CustomerManagedS3": (CustomerManagedS3, False),
        "ServiceManagedS3": (dict, False),
    }


class RetentionPeriod(AWSProperty):
    """
    `RetentionPeriod <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-datastore-retentionperiod.html>`__
    """

    props: PropsDictType = {
        "NumberOfDays": (integer, False),
        "Unlimited": (boolean, False),
    }


class Channel(AWSObject):
    """
    `Channel <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-iotanalytics-channel.html>`__
    """

    resource_type = "AWS::IoTAnalytics::Channel"

    props: PropsDictType = {
        "ChannelName": (str, False),
        "ChannelStorage": (ChannelStorage, False),
        "RetentionPeriod": (RetentionPeriod, False),
        "Tags": (Tags, False),
    }


class ResourceConfiguration(AWSProperty):
    """
    `ResourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-resourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "ComputeType": (str, True),
        "VolumeSizeInGB": (integer, True),
    }


class DatasetContentVersionValue(AWSProperty):
    """
    `DatasetContentVersionValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-datasetcontentversionvalue.html>`__
    """

    props: PropsDictType = {
        "DatasetName": (str, True),
    }


class OutputFileUriValue(AWSProperty):
    """
    `OutputFileUriValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-outputfileurivalue.html>`__
    """

    props: PropsDictType = {
        "FileName": (str, True),
    }


class Variable(AWSProperty):
    """
    `Variable <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-variable.html>`__
    """

    props: PropsDictType = {
        "DatasetContentVersionValue": (DatasetContentVersionValue, False),
        "DoubleValue": (double, False),
        "OutputFileUriValue": (OutputFileUriValue, False),
        "StringValue": (str, False),
        "VariableName": (str, True),
    }


class ContainerAction(AWSProperty):
    """
    `ContainerAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-containeraction.html>`__
    """

    props: PropsDictType = {
        "ExecutionRoleArn": (str, True),
        "Image": (str, True),
        "ResourceConfiguration": (ResourceConfiguration, True),
        "Variables": ([Variable], False),
    }


class DeltaTime(AWSProperty):
    """
    `DeltaTime <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-deltatime.html>`__
    """

    props: PropsDictType = {
        "OffsetSeconds": (integer, True),
        "TimeExpression": (str, True),
    }


class QueryActionFilter(AWSProperty):
    """
    `QueryActionFilter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-filter.html>`__
    """

    props: PropsDictType = {
        "DeltaTime": (DeltaTime, False),
    }


class QueryAction(AWSProperty):
    """
    `QueryAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-queryaction.html>`__
    """

    props: PropsDictType = {
        "Filters": ([QueryActionFilter], False),
        "SqlQuery": (str, True),
    }


class Action(AWSProperty):
    """
    `Action <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-action.html>`__
    """

    props: PropsDictType = {
        "ActionName": (str, True),
        "ContainerAction": (ContainerAction, False),
        "QueryAction": (QueryAction, False),
    }


class IotEventsDestinationConfiguration(AWSProperty):
    """
    `IotEventsDestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-ioteventsdestinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "InputName": (str, True),
        "RoleArn": (str, True),
    }


class GlueConfiguration(AWSProperty):
    """
    `GlueConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-glueconfiguration.html>`__
    """

    props: PropsDictType = {
        "DatabaseName": (str, True),
        "TableName": (str, True),
    }


class S3DestinationConfiguration(AWSProperty):
    """
    `S3DestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-s3destinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "Bucket": (str, True),
        "GlueConfiguration": (GlueConfiguration, False),
        "Key": (str, True),
        "RoleArn": (str, True),
    }


class DatasetContentDeliveryRuleDestination(AWSProperty):
    """
    `DatasetContentDeliveryRuleDestination <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-datasetcontentdeliveryruledestination.html>`__
    """

    props: PropsDictType = {
        "IotEventsDestinationConfiguration": (IotEventsDestinationConfiguration, False),
        "S3DestinationConfiguration": (S3DestinationConfiguration, False),
    }


class DatasetContentDeliveryRule(AWSProperty):
    """
    `DatasetContentDeliveryRule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-datasetcontentdeliveryrule.html>`__
    """

    props: PropsDictType = {
        "Destination": (DatasetContentDeliveryRuleDestination, True),
        "EntryName": (str, False),
    }


class DeltaTimeSessionWindowConfiguration(AWSProperty):
    """
    `DeltaTimeSessionWindowConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-deltatimesessionwindowconfiguration.html>`__
    """

    props: PropsDictType = {
        "TimeoutInMinutes": (integer, True),
    }


class LateDataRuleConfiguration(AWSProperty):
    """
    `LateDataRuleConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-latedataruleconfiguration.html>`__
    """

    props: PropsDictType = {
        "DeltaTimeSessionWindowConfiguration": (
            DeltaTimeSessionWindowConfiguration,
            False,
        ),
    }


class LateDataRule(AWSProperty):
    """
    `LateDataRule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-latedatarule.html>`__
    """

    props: PropsDictType = {
        "RuleConfiguration": (LateDataRuleConfiguration, True),
        "RuleName": (str, False),
    }


class Schedule(AWSProperty):
    """
    `Schedule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-schedule.html>`__
    """

    props: PropsDictType = {
        "ScheduleExpression": (str, True),
    }


class TriggeringDataset(AWSProperty):
    """
    `TriggeringDataset <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-triggeringdataset.html>`__
    """

    props: PropsDictType = {
        "DatasetName": (str, True),
    }


class Trigger(AWSProperty):
    """
    `Trigger <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-trigger.html>`__
    """

    props: PropsDictType = {
        "Schedule": (Schedule, False),
        "TriggeringDataset": (TriggeringDataset, False),
    }


class VersioningConfiguration(AWSProperty):
    """
    `VersioningConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-dataset-versioningconfiguration.html>`__
    """

    props: PropsDictType = {
        "MaxVersions": (integer, False),
        "Unlimited": (boolean, False),
    }


class Dataset(AWSObject):
    """
    `Dataset <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-iotanalytics-dataset.html>`__
    """

    resource_type = "AWS::IoTAnalytics::Dataset"

    props: PropsDictType = {
        "Actions": ([Action], True),
        "ContentDeliveryRules": ([DatasetContentDeliveryRule], False),
        "DatasetName": (str, False),
        "LateDataRules": ([LateDataRule], False),
        "RetentionPeriod": (RetentionPeriod, False),
        "Tags": (Tags, False),
        "Triggers": ([Trigger], False),
        "VersioningConfiguration": (VersioningConfiguration, False),
    }


class Partition(AWSProperty):
    """
    `Partition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-datastore-partition.html>`__
    """

    props: PropsDictType = {
        "AttributeName": (str, True),
    }


class TimestampPartition(AWSProperty):
    """
    `TimestampPartition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-datastore-timestamppartition.html>`__
    """

    props: PropsDictType = {
        "AttributeName": (str, True),
        "TimestampFormat": (str, False),
    }


class DatastorePartition(AWSProperty):
    """
    `DatastorePartition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-datastore-datastorepartition.html>`__
    """

    props: PropsDictType = {
        "Partition": (Partition, False),
        "TimestampPartition": (TimestampPartition, False),
    }


class DatastorePartitions(AWSProperty):
    """
    `DatastorePartitions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-datastore-datastorepartitions.html>`__
    """

    props: PropsDictType = {
        "Partitions": ([DatastorePartition], False),
    }


class CustomerManagedS3Storage(AWSProperty):
    """
    `CustomerManagedS3Storage <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-datastore-customermanageds3storage.html>`__
    """

    props: PropsDictType = {
        "Bucket": (str, True),
        "KeyPrefix": (str, False),
    }


class IotSiteWiseMultiLayerStorage(AWSProperty):
    """
    `IotSiteWiseMultiLayerStorage <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-datastore-iotsitewisemultilayerstorage.html>`__
    """

    props: PropsDictType = {
        "CustomerManagedS3Storage": (CustomerManagedS3Storage, False),
    }


class DatastoreStorage(AWSProperty):
    """
    `DatastoreStorage <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-datastore-datastorestorage.html>`__
    """

    props: PropsDictType = {
        "CustomerManagedS3": (CustomerManagedS3, False),
        "IotSiteWiseMultiLayerStorage": (IotSiteWiseMultiLayerStorage, False),
        "ServiceManagedS3": (dict, False),
    }


class Column(AWSProperty):
    """
    `Column <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-datastore-column.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "Type": (str, True),
    }


class SchemaDefinition(AWSProperty):
    """
    `SchemaDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-datastore-schemadefinition.html>`__
    """

    props: PropsDictType = {
        "Columns": ([Column], False),
    }


class ParquetConfiguration(AWSProperty):
    """
    `ParquetConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-datastore-parquetconfiguration.html>`__
    """

    props: PropsDictType = {
        "SchemaDefinition": (SchemaDefinition, False),
    }


class FileFormatConfiguration(AWSProperty):
    """
    `FileFormatConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-datastore-fileformatconfiguration.html>`__
    """

    props: PropsDictType = {
        "JsonConfiguration": (dict, False),
        "ParquetConfiguration": (ParquetConfiguration, False),
    }


class Datastore(AWSObject):
    """
    `Datastore <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-iotanalytics-datastore.html>`__
    """

    resource_type = "AWS::IoTAnalytics::Datastore"

    props: PropsDictType = {
        "DatastoreName": (str, False),
        "DatastorePartitions": (DatastorePartitions, False),
        "DatastoreStorage": (DatastoreStorage, False),
        "FileFormatConfiguration": (FileFormatConfiguration, False),
        "RetentionPeriod": (RetentionPeriod, False),
        "Tags": (Tags, False),
    }


class ActivityChannel(AWSProperty):
    """
    `ActivityChannel <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-pipeline-channel.html>`__
    """

    props: PropsDictType = {
        "ChannelName": (str, True),
        "Name": (str, True),
        "Next": (str, False),
    }


class ActivityDatastore(AWSProperty):
    """
    `ActivityDatastore <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-pipeline-datastore.html>`__
    """

    props: PropsDictType = {
        "DatastoreName": (str, True),
        "Name": (str, True),
    }


class AddAttributes(AWSProperty):
    """
    `AddAttributes <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-pipeline-addattributes.html>`__
    """

    props: PropsDictType = {
        "Attributes": (dict, True),
        "Name": (str, True),
        "Next": (str, False),
    }


class DeviceRegistryEnrich(AWSProperty):
    """
    `DeviceRegistryEnrich <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-pipeline-deviceregistryenrich.html>`__
    """

    props: PropsDictType = {
        "Attribute": (str, True),
        "Name": (str, True),
        "Next": (str, False),
        "RoleArn": (str, True),
        "ThingName": (str, True),
    }


class DeviceShadowEnrich(AWSProperty):
    """
    `DeviceShadowEnrich <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-pipeline-deviceshadowenrich.html>`__
    """

    props: PropsDictType = {
        "Attribute": (str, True),
        "Name": (str, True),
        "Next": (str, False),
        "RoleArn": (str, True),
        "ThingName": (str, True),
    }


class Filter(AWSProperty):
    """
    `Filter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-pipeline-filter.html>`__
    """

    props: PropsDictType = {
        "Filter": (str, True),
        "Name": (str, True),
        "Next": (str, False),
    }


class Lambda(AWSProperty):
    """
    `Lambda <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-pipeline-lambda.html>`__
    """

    props: PropsDictType = {
        "BatchSize": (integer, True),
        "LambdaName": (str, True),
        "Name": (str, True),
        "Next": (str, False),
    }


class Math(AWSProperty):
    """
    `Math <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-pipeline-math.html>`__
    """

    props: PropsDictType = {
        "Attribute": (str, True),
        "Math": (str, True),
        "Name": (str, True),
        "Next": (str, False),
    }


class RemoveAttributes(AWSProperty):
    """
    `RemoveAttributes <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-pipeline-removeattributes.html>`__
    """

    props: PropsDictType = {
        "Attributes": ([str], True),
        "Name": (str, True),
        "Next": (str, False),
    }


class SelectAttributes(AWSProperty):
    """
    `SelectAttributes <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-pipeline-selectattributes.html>`__
    """

    props: PropsDictType = {
        "Attributes": ([str], True),
        "Name": (str, True),
        "Next": (str, False),
    }


class Activity(AWSProperty):
    """
    `Activity <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-iotanalytics-pipeline-activity.html>`__
    """

    props: PropsDictType = {
        "AddAttributes": (AddAttributes, False),
        "Channel": (ActivityChannel, False),
        "Datastore": (ActivityDatastore, False),
        "DeviceRegistryEnrich": (DeviceRegistryEnrich, False),
        "DeviceShadowEnrich": (DeviceShadowEnrich, False),
        "Filter": (Filter, False),
        "Lambda": (Lambda, False),
        "Math": (Math, False),
        "RemoveAttributes": (RemoveAttributes, False),
        "SelectAttributes": (SelectAttributes, False),
    }


class Pipeline(AWSObject):
    """
    `Pipeline <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-iotanalytics-pipeline.html>`__
    """

    resource_type = "AWS::IoTAnalytics::Pipeline"

    props: PropsDictType = {
        "PipelineActivities": ([Activity], True),
        "PipelineName": (str, False),
        "Tags": (Tags, False),
    }
