{"production": {"app_function": "app.main.handler", "aws_region": "us-east-1", "profile_name": "sumathy_aws", "project_name": "replypal-api", "runtime": "python3.11", "s3_bucket": "replypal-zappa-deployments", "slim_handler": false, "use_precompiled_packages": false, "keep_warm": false, "log_level": "DEBUG", "memory_size": 1024, "timeout_seconds": 60, "include": ["openai"], "packages": ["openai"], "environment_variables": {"APP_NAME": "ReplyPal API", "APP_VERSION": "1.0.0", "ENVIRONMENT": "production", "LOG_LEVEL": "INFO", "CORS_ORIGINS": "*", "REQUEST_TIMEOUT": "30", "MAX_REQUEST_SIZE": "10000", "AI_PROVIDER": "deepseek", "MODEL_TEMPERATURE": "0.7", "MODEL_MAX_TOKENS": "500", "AWS_REGION": "us-east-1", "USE_MOCK_DYNAMODB": "false", "DYNAMODB_CUSTOMERS_TABLE": "replypal-customers", "DYNAMODB_SUBSCRIPTIONS_TABLE": "replypal-subscriptions", "DYNAMODB_USER_SUBSCRIPTIONS_TABLE": "replypal-user-subscriptions", "DYNAMODB_USAGE_RECORDS_TABLE": "replypal-usage-records", "AUTO_RENEWAL_TOKEN_THRESHOLD": "1500", "AUTO_RENEWAL_TIME_THRESHOLD_DAYS": "30", "JWT_SECRET_KEY": "ReplyPal-Production-JWT-Secret-Key-2024-Change-This-In-Production-Environment", "OPENAI_API_KEY": "********************************************************************************************************************************************************************", "OPENAI_API_MODEL": "gpt-3.5-turbo", "DEEPSEEK_API_KEY": "***********************************", "DEEPSEEK_API_BASE": "https://api.deepseek.com/v1", "DEEPSEEK_API_MODEL": "deepseek-chat", "STRIPE_PUBLISHABLE_KEY": "pk_test_51RMtuTQkxr8FpV1innlxTokLhLIn2GufKRedYnktG77BulktUwJf15o7uGjy5j27qrX961iLblmdUDMA5CvsDFOY00jo5H7zIB", "STRIPE_SECRET_KEY": "sk_test_51RMtuTQkxr8FpV1iZM0ak0pIKO0mnZrKFLKbtOvFfMuKOCU2bONSO7SOd0QpZEPp1ufIlBOHoApXtNQiHvwl51YI00n3vN80xM", "STRIPE_WEBHOOK_SECRET": "your-stripe-webhook-secret", "STRIPE_BASIC_PRICE_ID": "price_1RMu1WQkxr8FpV1iMFo6eiQA", "FRONTEND_SUCCESS_URL": "https://api.replypal.com/success", "FRONTEND_CANCEL_URL": "https://api.replypal.com/cancel", "GOOGLE_CLIENT_ID": "************-hntko3euueak8f49h8lrsgq8k4os2cp3.apps.googleusercontent.com", "GOOGLE_CLIENT_SECRET": "GOCSPX-placeholder-client-secret", "GOOGLE_REDIRECT_URI": "https://api.replypal.com/auth/google-callback"}, "exclude": ["*.pyc", "__pycache__", "*.git*", "*.DS_Store", "*.zip", "*.tar.gz", "tests/", "test_*.py", "*_test.py", "docs/", "*.md", "Dockerfile", "docker-compose.yml", "build_lambda_package.py", "setup_api_gateway.*", "LAMBDA_DEPLOYMENT_GUIDE.md", "Package/", "env/", "venv/", ".env*", "out.txt", "replypal_lambda.zip"], "cors": true, "cors_origin": "*", "binary_support": false, "lambda_description": "ReplyPal FastAPI application for Chrome extension", "tags": {"Project": "ReplyPal", "Environment": "Production", "Service": "API"}, "apigateway_description": "ReplyPal API Gateway for Chrome Extension", "certificate_arn": null, "domain": null, "route53_hosted_zone_id": null}, "staging": {"extends": "production", "environment_variables": {"ENVIRONMENT": "staging", "LOG_LEVEL": "DEBUG"}, "lambda_description": "ReplyPal FastAPI application - Staging", "tags": {"Project": "ReplyPal", "Environment": "Staging", "Service": "API"}}, "development": {"extends": "production", "environment_variables": {"ENVIRONMENT": "development", "LOG_LEVEL": "DEBUG", "USE_MOCK_DYNAMODB": "true"}, "lambda_description": "ReplyPal FastAPI application - Development", "tags": {"Project": "ReplyPal", "Environment": "Development", "Service": "API"}}}