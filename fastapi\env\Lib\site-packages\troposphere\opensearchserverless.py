# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, integer


class AccessPolicy(AWSObject):
    """
    `AccessPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-opensearchserverless-accesspolicy.html>`__
    """

    resource_type = "AWS::OpenSearchServerless::AccessPolicy"

    props: PropsDictType = {
        "Description": (str, False),
        "Name": (str, True),
        "Policy": (str, True),
        "Type": (str, True),
    }


class Collection(AWSObject):
    """
    `Collection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-opensearchserverless-collection.html>`__
    """

    resource_type = "AWS::OpenSearchServerless::Collection"

    props: PropsDictType = {
        "Description": (str, False),
        "Name": (str, True),
        "StandbyReplicas": (str, False),
        "Tags": (Tags, False),
        "Type": (str, False),
    }


class IndexProperty(AWSProperty):
    """
    `IndexProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-opensearchserverless-index-index.html>`__
    """

    props: PropsDictType = {
        "Knn": (boolean, False),
        "KnnAlgoParamEfSearch": (integer, False),
        "RefreshInterval": (str, False),
    }


class IndexSettings(AWSProperty):
    """
    `IndexSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-opensearchserverless-index-indexsettings.html>`__
    """

    props: PropsDictType = {
        "Index": (IndexProperty, False),
    }


class Parameters(AWSProperty):
    """
    `Parameters <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-opensearchserverless-index-parameters.html>`__
    """

    props: PropsDictType = {
        "EfConstruction": (integer, False),
        "M": (integer, False),
    }


class Method(AWSProperty):
    """
    `Method <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-opensearchserverless-index-method.html>`__
    """

    props: PropsDictType = {
        "Engine": (str, True),
        "Name": (str, True),
        "Parameters": (Parameters, False),
        "SpaceType": (str, False),
    }


class PropertyMapping(AWSProperty):
    """
    `PropertyMapping <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-opensearchserverless-index-propertymapping.html>`__
    """

    props: PropsDictType = {
        "Dimension": (integer, False),
        "Index": (boolean, False),
        "Method": (Method, False),
        "Properties": (dict, False),
        "Type": (str, True),
        "Value": (str, False),
    }


class Mappings(AWSProperty):
    """
    `Mappings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-opensearchserverless-index-mappings.html>`__
    """

    props: PropsDictType = {
        "Properties": (dict, False),
    }


class Index(AWSObject):
    """
    `Index <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-opensearchserverless-index.html>`__
    """

    resource_type = "AWS::OpenSearchServerless::Index"

    props: PropsDictType = {
        "CollectionEndpoint": (str, True),
        "IndexName": (str, True),
        "Mappings": (Mappings, False),
        "Settings": (IndexSettings, False),
    }


class LifecyclePolicy(AWSObject):
    """
    `LifecyclePolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-opensearchserverless-lifecyclepolicy.html>`__
    """

    resource_type = "AWS::OpenSearchServerless::LifecyclePolicy"

    props: PropsDictType = {
        "Description": (str, False),
        "Name": (str, True),
        "Policy": (str, True),
        "Type": (str, True),
    }


class IamIdentityCenterConfigOptions(AWSProperty):
    """
    `IamIdentityCenterConfigOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-opensearchserverless-securityconfig-iamidentitycenterconfigoptions.html>`__
    """

    props: PropsDictType = {
        "ApplicationArn": (str, False),
        "ApplicationDescription": (str, False),
        "ApplicationName": (str, False),
        "GroupAttribute": (str, False),
        "InstanceArn": (str, True),
        "UserAttribute": (str, False),
    }


class SamlConfigOptions(AWSProperty):
    """
    `SamlConfigOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-opensearchserverless-securityconfig-samlconfigoptions.html>`__
    """

    props: PropsDictType = {
        "GroupAttribute": (str, False),
        "Metadata": (str, True),
        "OpenSearchServerlessEntityId": (str, False),
        "SessionTimeout": (integer, False),
        "UserAttribute": (str, False),
    }


class SecurityConfig(AWSObject):
    """
    `SecurityConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-opensearchserverless-securityconfig.html>`__
    """

    resource_type = "AWS::OpenSearchServerless::SecurityConfig"

    props: PropsDictType = {
        "Description": (str, False),
        "IamIdentityCenterOptions": (IamIdentityCenterConfigOptions, False),
        "Name": (str, False),
        "SamlOptions": (SamlConfigOptions, False),
        "Type": (str, False),
    }


class SecurityPolicy(AWSObject):
    """
    `SecurityPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-opensearchserverless-securitypolicy.html>`__
    """

    resource_type = "AWS::OpenSearchServerless::SecurityPolicy"

    props: PropsDictType = {
        "Description": (str, False),
        "Name": (str, True),
        "Policy": (str, True),
        "Type": (str, True),
    }


class VpcEndpoint(AWSObject):
    """
    `VpcEndpoint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-opensearchserverless-vpcendpoint.html>`__
    """

    resource_type = "AWS::OpenSearchServerless::VpcEndpoint"

    props: PropsDictType = {
        "Name": (str, True),
        "SecurityGroupIds": ([str], False),
        "SubnetIds": ([str], True),
        "VpcId": (str, True),
    }
