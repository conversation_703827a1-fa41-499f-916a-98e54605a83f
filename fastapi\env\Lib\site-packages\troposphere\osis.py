# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, integer


class BufferOptions(AWSProperty):
    """
    `BufferOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-osis-pipeline-bufferoptions.html>`__
    """

    props: PropsDictType = {
        "PersistentBufferEnabled": (boolean, True),
    }


class EncryptionAtRestOptions(AWSProperty):
    """
    `EncryptionAtRestOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-osis-pipeline-encryptionatrestoptions.html>`__
    """

    props: PropsDictType = {
        "KmsKeyArn": (str, True),
    }


class CloudWatchLogDestination(AWSProperty):
    """
    `CloudWatchLogDestination <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-osis-pipeline-cloudwatchlogdestination.html>`__
    """

    props: PropsDictType = {
        "LogGroup": (str, True),
    }


class LogPublishingOptions(AWSProperty):
    """
    `LogPublishingOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-osis-pipeline-logpublishingoptions.html>`__
    """

    props: PropsDictType = {
        "CloudWatchLogDestination": (CloudWatchLogDestination, False),
        "IsLoggingEnabled": (boolean, False),
    }


class VpcAttachmentOptions(AWSProperty):
    """
    `VpcAttachmentOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-osis-pipeline-vpcattachmentoptions.html>`__
    """

    props: PropsDictType = {
        "AttachToVpc": (boolean, True),
        "CidrBlock": (str, True),
    }


class VpcOptions(AWSProperty):
    """
    `VpcOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-osis-pipeline-vpcoptions.html>`__
    """

    props: PropsDictType = {
        "SecurityGroupIds": ([str], False),
        "SubnetIds": ([str], True),
        "VpcAttachmentOptions": (VpcAttachmentOptions, False),
        "VpcEndpointManagement": (str, False),
    }


class Pipeline(AWSObject):
    """
    `Pipeline <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-osis-pipeline.html>`__
    """

    resource_type = "AWS::OSIS::Pipeline"

    props: PropsDictType = {
        "BufferOptions": (BufferOptions, False),
        "EncryptionAtRestOptions": (EncryptionAtRestOptions, False),
        "LogPublishingOptions": (LogPublishingOptions, False),
        "MaxUnits": (integer, True),
        "MinUnits": (integer, True),
        "PipelineConfigurationBody": (str, True),
        "PipelineName": (str, True),
        "Tags": (Tags, False),
        "VpcOptions": (VpcOptions, False),
    }


class VpcEndpoint(AWSProperty):
    """
    `VpcEndpoint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-osis-pipeline-vpcendpoint.html>`__
    """

    props: PropsDictType = {
        "VpcEndpointId": (str, False),
        "VpcId": (str, False),
        "VpcOptions": (VpcOptions, False),
    }
