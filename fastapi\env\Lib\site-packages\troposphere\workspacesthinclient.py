# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import integer


class MaintenanceWindow(AWSProperty):
    """
    `MaintenanceWindow <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-workspacesthinclient-environment-maintenancewindow.html>`__
    """

    props: PropsDictType = {
        "ApplyTimeOf": (str, False),
        "DaysOfTheWeek": ([str], False),
        "EndTimeHour": (integer, False),
        "EndTimeMinute": (integer, False),
        "StartTimeHour": (integer, False),
        "StartTimeMinute": (integer, False),
        "Type": (str, True),
    }


class Environment(AWSObject):
    """
    `Environment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-workspacesthinclient-environment.html>`__
    """

    resource_type = "AWS::WorkSpacesThinClient::Environment"

    props: PropsDictType = {
        "DesiredSoftwareSetId": (str, False),
        "DesktopArn": (str, True),
        "DesktopEndpoint": (str, False),
        "DeviceCreationTags": (Tags, False),
        "KmsKeyArn": (str, False),
        "MaintenanceWindow": (MaintenanceWindow, False),
        "Name": (str, False),
        "SoftwareSetUpdateMode": (str, False),
        "SoftwareSetUpdateSchedule": (str, False),
        "Tags": (Tags, False),
    }
