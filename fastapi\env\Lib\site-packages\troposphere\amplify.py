# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean


class BasicAuthConfig(AWSProperty):
    """
    `BasicAuthConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-amplify-branch-basicauthconfig.html>`__
    """

    props: PropsDictType = {
        "EnableBasicAuth": (boolean, False),
        "Password": (str, True),
        "Username": (str, True),
    }


class EnvironmentVariable(AWSProperty):
    """
    `EnvironmentVariable <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-amplify-branch-environmentvariable.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "Value": (str, True),
    }


class AutoBranchCreationConfig(AWSProperty):
    """
    `AutoBranchCreationConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-amplify-app-autobranchcreationconfig.html>`__
    """

    props: PropsDictType = {
        "AutoBranchCreationPatterns": ([str], False),
        "BasicAuthConfig": (BasicAuthConfig, False),
        "BuildSpec": (str, False),
        "EnableAutoBranchCreation": (boolean, False),
        "EnableAutoBuild": (boolean, False),
        "EnablePerformanceMode": (boolean, False),
        "EnablePullRequestPreview": (boolean, False),
        "EnvironmentVariables": ([EnvironmentVariable], False),
        "Framework": (str, False),
        "PullRequestEnvironmentName": (str, False),
        "Stage": (str, False),
    }


class CacheConfig(AWSProperty):
    """
    `CacheConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-amplify-app-cacheconfig.html>`__
    """

    props: PropsDictType = {
        "Type": (str, False),
    }


class CustomRule(AWSProperty):
    """
    `CustomRule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-amplify-app-customrule.html>`__
    """

    props: PropsDictType = {
        "Condition": (str, False),
        "Source": (str, True),
        "Status": (str, False),
        "Target": (str, True),
    }


class App(AWSObject):
    """
    `App <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-amplify-app.html>`__
    """

    resource_type = "AWS::Amplify::App"

    props: PropsDictType = {
        "AccessToken": (str, False),
        "AutoBranchCreationConfig": (AutoBranchCreationConfig, False),
        "BasicAuthConfig": (BasicAuthConfig, False),
        "BuildSpec": (str, False),
        "CacheConfig": (CacheConfig, False),
        "ComputeRoleArn": (str, False),
        "CustomHeaders": (str, False),
        "CustomRules": ([CustomRule], False),
        "Description": (str, False),
        "EnableBranchAutoDeletion": (boolean, False),
        "EnvironmentVariables": ([EnvironmentVariable], False),
        "IAMServiceRole": (str, False),
        "Name": (str, True),
        "OauthToken": (str, False),
        "Platform": (str, False),
        "Repository": (str, False),
        "Tags": (Tags, False),
    }


class Backend(AWSProperty):
    """
    `Backend <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-amplify-branch-backend.html>`__
    """

    props: PropsDictType = {
        "StackArn": (str, False),
    }


class Branch(AWSObject):
    """
    `Branch <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-amplify-branch.html>`__
    """

    resource_type = "AWS::Amplify::Branch"

    props: PropsDictType = {
        "AppId": (str, True),
        "Backend": (Backend, False),
        "BasicAuthConfig": (BasicAuthConfig, False),
        "BranchName": (str, True),
        "BuildSpec": (str, False),
        "ComputeRoleArn": (str, False),
        "Description": (str, False),
        "EnableAutoBuild": (boolean, False),
        "EnablePerformanceMode": (boolean, False),
        "EnablePullRequestPreview": (boolean, False),
        "EnableSkewProtection": (boolean, False),
        "EnvironmentVariables": ([EnvironmentVariable], False),
        "Framework": (str, False),
        "PullRequestEnvironmentName": (str, False),
        "Stage": (str, False),
        "Tags": (Tags, False),
    }


class CertificateSettings(AWSProperty):
    """
    `CertificateSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-amplify-domain-certificatesettings.html>`__
    """

    props: PropsDictType = {
        "CertificateType": (str, False),
        "CustomCertificateArn": (str, False),
    }


class SubDomainSetting(AWSProperty):
    """
    `SubDomainSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-amplify-domain-subdomainsetting.html>`__
    """

    props: PropsDictType = {
        "BranchName": (str, True),
        "Prefix": (str, True),
    }


class Domain(AWSObject):
    """
    `Domain <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-amplify-domain.html>`__
    """

    resource_type = "AWS::Amplify::Domain"

    props: PropsDictType = {
        "AppId": (str, True),
        "AutoSubDomainCreationPatterns": ([str], False),
        "AutoSubDomainIAMRole": (str, False),
        "CertificateSettings": (CertificateSettings, False),
        "DomainName": (str, True),
        "EnableAutoSubDomain": (boolean, False),
        "SubDomainSettings": ([SubDomainSetting], True),
    }


class Certificate(AWSProperty):
    """
    `Certificate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-amplify-domain-certificate.html>`__
    """

    props: PropsDictType = {
        "CertificateArn": (str, False),
        "CertificateType": (str, False),
        "CertificateVerificationDNSRecord": (str, False),
    }
