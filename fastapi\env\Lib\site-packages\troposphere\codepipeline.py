# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, integer


class ArtifactDetails(AWSProperty):
    """
    `ArtifactDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-customactiontype-artifactdetails.html>`__
    """

    props: PropsDictType = {
        "MaximumCount": (integer, True),
        "MinimumCount": (integer, True),
    }


class ConfigurationProperties(AWSProperty):
    """
    `ConfigurationProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-customactiontype-configurationproperties.html>`__
    """

    props: PropsDictType = {
        "Description": (str, False),
        "Key": (boolean, True),
        "Name": (str, True),
        "Queryable": (boolean, False),
        "Required": (boolean, True),
        "Secret": (boolean, True),
        "Type": (str, False),
    }


class Settings(AWSProperty):
    """
    `Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-customactiontype-settings.html>`__
    """

    props: PropsDictType = {
        "EntityUrlTemplate": (str, False),
        "ExecutionUrlTemplate": (str, False),
        "RevisionUrlTemplate": (str, False),
        "ThirdPartyConfigurationUrl": (str, False),
    }


class CustomActionType(AWSObject):
    """
    `CustomActionType <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-codepipeline-customactiontype.html>`__
    """

    resource_type = "AWS::CodePipeline::CustomActionType"

    props: PropsDictType = {
        "Category": (str, True),
        "ConfigurationProperties": ([ConfigurationProperties], False),
        "InputArtifactDetails": (ArtifactDetails, True),
        "OutputArtifactDetails": (ArtifactDetails, True),
        "Provider": (str, True),
        "Settings": (Settings, False),
        "Tags": (Tags, False),
        "Version": (str, True),
    }


class EncryptionKey(AWSProperty):
    """
    `EncryptionKey <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-encryptionkey.html>`__
    """

    props: PropsDictType = {
        "Id": (str, True),
        "Type": (str, True),
    }


class ArtifactStore(AWSProperty):
    """
    `ArtifactStore <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-artifactstore.html>`__
    """

    props: PropsDictType = {
        "EncryptionKey": (EncryptionKey, False),
        "Location": (str, True),
        "Type": (str, True),
    }


class ArtifactStoreMap(AWSProperty):
    """
    `ArtifactStoreMap <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-artifactstoremap.html>`__
    """

    props: PropsDictType = {
        "ArtifactStore": (ArtifactStore, True),
        "Region": (str, True),
    }


class DisableInboundStageTransitions(AWSProperty):
    """
    `DisableInboundStageTransitions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-stagetransition.html>`__
    """

    props: PropsDictType = {
        "Reason": (str, True),
        "StageName": (str, True),
    }


class GitBranchFilterCriteria(AWSProperty):
    """
    `GitBranchFilterCriteria <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-gitbranchfiltercriteria.html>`__
    """

    props: PropsDictType = {
        "Excludes": ([str], False),
        "Includes": ([str], False),
    }


class GitFilePathFilterCriteria(AWSProperty):
    """
    `GitFilePathFilterCriteria <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-gitfilepathfiltercriteria.html>`__
    """

    props: PropsDictType = {
        "Excludes": ([str], False),
        "Includes": ([str], False),
    }


class GitPullRequestFilter(AWSProperty):
    """
    `GitPullRequestFilter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-gitpullrequestfilter.html>`__
    """

    props: PropsDictType = {
        "Branches": (GitBranchFilterCriteria, False),
        "Events": ([str], False),
        "FilePaths": (GitFilePathFilterCriteria, False),
    }


class GitTagFilterCriteria(AWSProperty):
    """
    `GitTagFilterCriteria <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-gittagfiltercriteria.html>`__
    """

    props: PropsDictType = {
        "Excludes": ([str], False),
        "Includes": ([str], False),
    }


class GitPushFilter(AWSProperty):
    """
    `GitPushFilter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-gitpushfilter.html>`__
    """

    props: PropsDictType = {
        "Branches": (GitBranchFilterCriteria, False),
        "FilePaths": (GitFilePathFilterCriteria, False),
        "Tags": (GitTagFilterCriteria, False),
    }


class GitConfiguration(AWSProperty):
    """
    `GitConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-gitconfiguration.html>`__
    """

    props: PropsDictType = {
        "PullRequest": ([GitPullRequestFilter], False),
        "Push": ([GitPushFilter], False),
        "SourceActionName": (str, True),
    }


class PipelineTriggerDeclaration(AWSProperty):
    """
    `PipelineTriggerDeclaration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-pipelinetriggerdeclaration.html>`__
    """

    props: PropsDictType = {
        "GitConfiguration": (GitConfiguration, False),
        "ProviderType": (str, True),
    }


class ActionTypeId(AWSProperty):
    """
    `ActionTypeId <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-actiontypeid.html>`__
    """

    props: PropsDictType = {
        "Category": (str, True),
        "Owner": (str, True),
        "Provider": (str, True),
        "Version": (str, True),
    }


class InputArtifacts(AWSProperty):
    """
    `InputArtifacts <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-inputartifact.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
    }


class OutputArtifacts(AWSProperty):
    """
    `OutputArtifacts <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-outputartifact.html>`__
    """

    props: PropsDictType = {
        "Files": ([str], False),
        "Name": (str, True),
    }


class Actions(AWSProperty):
    """
    `Actions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-actiondeclaration.html>`__
    """

    props: PropsDictType = {
        "ActionTypeId": (ActionTypeId, True),
        "Commands": ([str], False),
        "Configuration": (dict, False),
        "InputArtifacts": ([InputArtifacts], False),
        "Name": (str, True),
        "Namespace": (str, False),
        "OutputArtifacts": ([OutputArtifacts], False),
        "OutputVariables": ([str], False),
        "Region": (str, False),
        "RoleArn": (str, False),
        "RunOrder": (integer, False),
        "TimeoutInMinutes": (integer, False),
    }


class RuleTypeId(AWSProperty):
    """
    `RuleTypeId <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-ruletypeid.html>`__
    """

    props: PropsDictType = {
        "Category": (str, False),
        "Owner": (str, False),
        "Provider": (str, False),
        "Version": (str, False),
    }


class RuleDeclaration(AWSProperty):
    """
    `RuleDeclaration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-ruledeclaration.html>`__
    """

    props: PropsDictType = {
        "Commands": ([str], False),
        "Configuration": (dict, False),
        "InputArtifacts": ([InputArtifacts], False),
        "Name": (str, False),
        "Region": (str, False),
        "RoleArn": (str, False),
        "RuleTypeId": (RuleTypeId, False),
    }


class Condition(AWSProperty):
    """
    `Condition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-condition.html>`__
    """

    props: PropsDictType = {
        "Result": (str, False),
        "Rules": ([RuleDeclaration], False),
    }


class BeforeEntryConditions(AWSProperty):
    """
    `BeforeEntryConditions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-beforeentryconditions.html>`__
    """

    props: PropsDictType = {
        "Conditions": ([Condition], False),
    }


class Blockers(AWSProperty):
    """
    `Blockers <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-blockerdeclaration.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "Type": (str, True),
    }


class RetryConfiguration(AWSProperty):
    """
    `RetryConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-retryconfiguration.html>`__
    """

    props: PropsDictType = {
        "RetryMode": (str, False),
    }


class FailureConditions(AWSProperty):
    """
    `FailureConditions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-failureconditions.html>`__
    """

    props: PropsDictType = {
        "Conditions": ([Condition], False),
        "Result": (str, False),
        "RetryConfiguration": (RetryConfiguration, False),
    }


class SuccessConditions(AWSProperty):
    """
    `SuccessConditions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-successconditions.html>`__
    """

    props: PropsDictType = {
        "Conditions": ([Condition], False),
    }


class Stages(AWSProperty):
    """
    `Stages <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-stagedeclaration.html>`__
    """

    props: PropsDictType = {
        "Actions": ([Actions], True),
        "BeforeEntry": (BeforeEntryConditions, False),
        "Blockers": ([Blockers], False),
        "Name": (str, True),
        "OnFailure": (FailureConditions, False),
        "OnSuccess": (SuccessConditions, False),
    }


class VariableDeclaration(AWSProperty):
    """
    `VariableDeclaration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-pipeline-variabledeclaration.html>`__
    """

    props: PropsDictType = {
        "DefaultValue": (str, False),
        "Description": (str, False),
        "Name": (str, True),
    }


class Pipeline(AWSObject):
    """
    `Pipeline <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-codepipeline-pipeline.html>`__
    """

    resource_type = "AWS::CodePipeline::Pipeline"

    props: PropsDictType = {
        "ArtifactStore": (ArtifactStore, False),
        "ArtifactStores": ([ArtifactStoreMap], False),
        "DisableInboundStageTransitions": ([DisableInboundStageTransitions], False),
        "ExecutionMode": (str, False),
        "Name": (str, False),
        "PipelineType": (str, False),
        "RestartExecutionOnUpdate": (boolean, False),
        "RoleArn": (str, True),
        "Stages": ([Stages], True),
        "Tags": (Tags, False),
        "Triggers": ([PipelineTriggerDeclaration], False),
        "Variables": ([VariableDeclaration], False),
    }


class WebhookAuthConfiguration(AWSProperty):
    """
    `WebhookAuthConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-webhook-webhookauthconfiguration.html>`__
    """

    props: PropsDictType = {
        "AllowedIPRange": (str, False),
        "SecretToken": (str, False),
    }


class WebhookFilterRule(AWSProperty):
    """
    `WebhookFilterRule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codepipeline-webhook-webhookfilterrule.html>`__
    """

    props: PropsDictType = {
        "JsonPath": (str, True),
        "MatchEquals": (str, False),
    }


class Webhook(AWSObject):
    """
    `Webhook <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-codepipeline-webhook.html>`__
    """

    resource_type = "AWS::CodePipeline::Webhook"

    props: PropsDictType = {
        "Authentication": (str, True),
        "AuthenticationConfiguration": (WebhookAuthConfiguration, True),
        "Filters": ([WebhookFilterRule], True),
        "Name": (str, False),
        "RegisterWithThirdParty": (boolean, False),
        "TargetAction": (str, True),
        "TargetPipeline": (str, True),
        "TargetPipelineVersion": (integer, True),
    }
