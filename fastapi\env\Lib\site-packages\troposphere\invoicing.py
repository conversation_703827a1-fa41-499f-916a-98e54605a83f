# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType
from .validators import boolean


class ResourceTag(AWSProperty):
    """
    `ResourceTag <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-invoicing-invoiceunit-resourcetag.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (str, True),
    }


class Rule(AWSProperty):
    """
    `Rule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-invoicing-invoiceunit-rule.html>`__
    """

    props: PropsDictType = {
        "LinkedAccounts": ([str], True),
    }


class InvoiceUnit(AWSObject):
    """
    `InvoiceUnit <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-invoicing-invoiceunit.html>`__
    """

    resource_type = "AWS::Invoicing::InvoiceUnit"

    props: PropsDictType = {
        "Description": (str, False),
        "InvoiceReceiver": (str, True),
        "Name": (str, True),
        "ResourceTags": ([ResourceTag], False),
        "Rule": (Rule, True),
        "TaxInheritanceDisabled": (boolean, False),
    }
