# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import integer


class ResourceTag(AWSProperty):
    """
    `ResourceTag <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-rbin-rule-resourcetag.html>`__
    """

    props: PropsDictType = {
        "ResourceTagKey": (str, True),
        "ResourceTagValue": (str, True),
    }


class RetentionPeriod(AWSProperty):
    """
    `RetentionPeriod <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-rbin-rule-retentionperiod.html>`__
    """

    props: PropsDictType = {
        "RetentionPeriodUnit": (str, True),
        "RetentionPeriodValue": (integer, True),
    }


class UnlockDelay(AWSProperty):
    """
    `UnlockDelay <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-rbin-rule-unlockdelay.html>`__
    """

    props: PropsDictType = {
        "UnlockDelayUnit": (str, False),
        "UnlockDelayValue": (integer, False),
    }


class Rule(AWSObject):
    """
    `Rule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-rbin-rule.html>`__
    """

    resource_type = "AWS::Rbin::Rule"

    props: PropsDictType = {
        "Description": (str, False),
        "ExcludeResourceTags": ([ResourceTag], False),
        "LockConfiguration": (UnlockDelay, False),
        "ResourceTags": ([ResourceTag], False),
        "ResourceType": (str, True),
        "RetentionPeriod": (RetentionPeriod, True),
        "Status": (str, False),
        "Tags": (Tags, False),
    }
