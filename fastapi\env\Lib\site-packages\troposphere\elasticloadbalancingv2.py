# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, integer
from .validators.elasticloadbalancingv2 import TARGET_TYPE_ALB  # noqa: F401
from .validators.elasticloadbalancingv2 import TARGET_TYPE_INSTANCE  # noqa: F401
from .validators.elasticloadbalancingv2 import TARGET_TYPE_IP  # noqa: F401
from .validators.elasticloadbalancingv2 import TARGET_TYPE_LAMBDA  # noqa: F401
from .validators.elasticloadbalancingv2 import (
    tg_healthcheck_port,
    validate_action,
    validate_elb_name,
    validate_fixed_response_config,
    validate_loadbalancer,
    validate_network_port,
    validate_redirect_config,
    validate_tags_or_list,
    validate_target_group,
    validate_target_type,
)


class AuthenticateCognitoConfig(AWSProperty):
    """
    `AuthenticateCognitoConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listenerrule-authenticatecognitoconfig.html>`__
    """

    props: PropsDictType = {
        "AuthenticationRequestExtraParams": (dict, False),
        "OnUnauthenticatedRequest": (str, False),
        "Scope": (str, False),
        "SessionCookieName": (str, False),
        "SessionTimeout": (integer, False),
        "UserPoolArn": (str, True),
        "UserPoolClientId": (str, True),
        "UserPoolDomain": (str, True),
    }


class AuthenticateOidcConfig(AWSProperty):
    """
    `AuthenticateOidcConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listener-authenticateoidcconfig.html>`__
    """

    props: PropsDictType = {
        "AuthenticationRequestExtraParams": (dict, False),
        "AuthorizationEndpoint": (str, True),
        "ClientId": (str, True),
        "ClientSecret": (str, False),
        "Issuer": (str, True),
        "OnUnauthenticatedRequest": (str, False),
        "Scope": (str, False),
        "SessionCookieName": (str, False),
        "SessionTimeout": (str, False),
        "TokenEndpoint": (str, True),
        "UseExistingClientSecret": (boolean, False),
        "UserInfoEndpoint": (str, True),
    }


class FixedResponseConfig(AWSProperty):
    """
    `FixedResponseConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listenerrule-fixedresponseconfig.html>`__
    """

    props: PropsDictType = {
        "ContentType": (str, False),
        "MessageBody": (str, False),
        "StatusCode": (str, True),
    }

    def validate(self):
        validate_fixed_response_config(self)


class TargetGroupStickinessConfig(AWSProperty):
    """
    `TargetGroupStickinessConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listenerrule-targetgroupstickinessconfig.html>`__
    """

    props: PropsDictType = {
        "DurationSeconds": (integer, False),
        "Enabled": (boolean, False),
    }


class TargetGroupTuple(AWSProperty):
    """
    `TargetGroupTuple <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listenerrule-targetgrouptuple.html>`__
    """

    props: PropsDictType = {
        "TargetGroupArn": (str, False),
        "Weight": (integer, False),
    }


class ForwardConfig(AWSProperty):
    """
    `ForwardConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listenerrule-forwardconfig.html>`__
    """

    props: PropsDictType = {
        "TargetGroupStickinessConfig": (TargetGroupStickinessConfig, False),
        "TargetGroups": ([TargetGroupTuple], False),
    }


class RedirectConfig(AWSProperty):
    """
    `RedirectConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listenerrule-redirectconfig.html>`__
    """

    props: PropsDictType = {
        "Host": (str, False),
        "Path": (str, False),
        "Port": (str, False),
        "Protocol": (str, False),
        "Query": (str, False),
        "StatusCode": (str, True),
    }

    def validate(self):
        validate_redirect_config(self)


class Action(AWSProperty):
    """
    `Action <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listener-action.html>`__
    """

    props: PropsDictType = {
        "AuthenticateCognitoConfig": (AuthenticateCognitoConfig, False),
        "AuthenticateOidcConfig": (AuthenticateOidcConfig, False),
        "FixedResponseConfig": (FixedResponseConfig, False),
        "ForwardConfig": (ForwardConfig, False),
        "Order": (integer, False),
        "RedirectConfig": (RedirectConfig, False),
        "TargetGroupArn": (str, False),
        "Type": (str, True),
    }

    def validate(self):
        validate_action(self)


class Certificate(AWSProperty):
    """
    `Certificate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listener-certificates.html>`__
    """

    props: PropsDictType = {
        "CertificateArn": (str, False),
    }


class ListenerAttribute(AWSProperty):
    """
    `ListenerAttribute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listener-listenerattribute.html>`__
    """

    props: PropsDictType = {
        "Key": (str, False),
        "Value": (str, False),
    }


class MutualAuthentication(AWSProperty):
    """
    `MutualAuthentication <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listener-mutualauthentication.html>`__
    """

    props: PropsDictType = {
        "AdvertiseTrustStoreCaNames": (str, False),
        "IgnoreClientCertificateExpiry": (boolean, False),
        "Mode": (str, False),
        "TrustStoreArn": (str, False),
    }


class Listener(AWSObject):
    """
    `Listener <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-elasticloadbalancingv2-listener.html>`__
    """

    resource_type = "AWS::ElasticLoadBalancingV2::Listener"

    props: PropsDictType = {
        "AlpnPolicy": ([str], False),
        "Certificates": ([Certificate], False),
        "DefaultActions": ([Action], True),
        "ListenerAttributes": ([ListenerAttribute], False),
        "LoadBalancerArn": (str, True),
        "MutualAuthentication": (MutualAuthentication, False),
        "Port": (validate_network_port, False),
        "Protocol": (str, False),
        "SslPolicy": (str, False),
    }


class ListenerCertificate(AWSObject):
    """
    `ListenerCertificate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-elasticloadbalancingv2-listenercertificate.html>`__
    """

    resource_type = "AWS::ElasticLoadBalancingV2::ListenerCertificate"

    props: PropsDictType = {
        "Certificates": ([Certificate], True),
        "ListenerArn": (str, True),
    }


class HostHeaderConfig(AWSProperty):
    """
    `HostHeaderConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listenerrule-hostheaderconfig.html>`__
    """

    props: PropsDictType = {
        "Values": ([str], False),
    }


class HttpHeaderConfig(AWSProperty):
    """
    `HttpHeaderConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listenerrule-httpheaderconfig.html>`__
    """

    props: PropsDictType = {
        "HttpHeaderName": (str, False),
        "Values": ([str], False),
    }


class HttpRequestMethodConfig(AWSProperty):
    """
    `HttpRequestMethodConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listenerrule-httprequestmethodconfig.html>`__
    """

    props: PropsDictType = {
        "Values": ([str], False),
    }


class PathPatternConfig(AWSProperty):
    """
    `PathPatternConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listenerrule-pathpatternconfig.html>`__
    """

    props: PropsDictType = {
        "Values": ([str], False),
    }


class QueryStringKeyValue(AWSProperty):
    """
    `QueryStringKeyValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listenerrule-querystringkeyvalue.html>`__
    """

    props: PropsDictType = {
        "Key": (str, False),
        "Value": (str, False),
    }


class QueryStringConfig(AWSProperty):
    """
    `QueryStringConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listenerrule-querystringconfig.html>`__
    """

    props: PropsDictType = {
        "Values": ([QueryStringKeyValue], False),
    }


class SourceIpConfig(AWSProperty):
    """
    `SourceIpConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listenerrule-sourceipconfig.html>`__
    """

    props: PropsDictType = {
        "Values": ([str], False),
    }


class Condition(AWSProperty):
    """
    `Condition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listenerrule-rulecondition.html>`__
    """

    props: PropsDictType = {
        "Field": (str, False),
        "HostHeaderConfig": (HostHeaderConfig, False),
        "HttpHeaderConfig": (HttpHeaderConfig, False),
        "HttpRequestMethodConfig": (HttpRequestMethodConfig, False),
        "PathPatternConfig": (PathPatternConfig, False),
        "QueryStringConfig": (QueryStringConfig, False),
        "SourceIpConfig": (SourceIpConfig, False),
        "Values": ([str], False),
    }


class ListenerRuleAuthenticateOidcConfig(AWSProperty):
    """
    `ListenerRuleAuthenticateOidcConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listenerrule-authenticateoidcconfig.html>`__
    """

    props: PropsDictType = {
        "AuthenticationRequestExtraParams": (dict, False),
        "AuthorizationEndpoint": (str, True),
        "ClientId": (str, True),
        "ClientSecret": (str, False),
        "Issuer": (str, True),
        "OnUnauthenticatedRequest": (str, False),
        "Scope": (str, False),
        "SessionCookieName": (str, False),
        "SessionTimeout": (integer, False),
        "TokenEndpoint": (str, True),
        "UseExistingClientSecret": (boolean, False),
        "UserInfoEndpoint": (str, True),
    }


class ListenerRuleAction(AWSProperty):
    """
    `ListenerRuleAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-listenerrule-action.html>`__
    """

    props: PropsDictType = {
        "AuthenticateCognitoConfig": (AuthenticateCognitoConfig, False),
        "AuthenticateOidcConfig": (ListenerRuleAuthenticateOidcConfig, False),
        "FixedResponseConfig": (FixedResponseConfig, False),
        "ForwardConfig": (ForwardConfig, False),
        "Order": (integer, False),
        "RedirectConfig": (RedirectConfig, False),
        "TargetGroupArn": (str, False),
        "Type": (str, True),
    }


class ListenerRule(AWSObject):
    """
    `ListenerRule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-elasticloadbalancingv2-listenerrule.html>`__
    """

    resource_type = "AWS::ElasticLoadBalancingV2::ListenerRule"

    props: PropsDictType = {
        "Actions": ([ListenerRuleAction], True),
        "Conditions": ([Condition], True),
        "ListenerArn": (str, False),
        "Priority": (integer, True),
    }


class LoadBalancerAttributes(AWSProperty):
    """
    `LoadBalancerAttributes <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-loadbalancer-loadbalancerattribute.html>`__
    """

    props: PropsDictType = {
        "Key": (str, False),
        "Value": (str, False),
    }


class MinimumLoadBalancerCapacity(AWSProperty):
    """
    `MinimumLoadBalancerCapacity <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-loadbalancer-minimumloadbalancercapacity.html>`__
    """

    props: PropsDictType = {
        "CapacityUnits": (integer, True),
    }


class SubnetMapping(AWSProperty):
    """
    `SubnetMapping <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-loadbalancer-subnetmapping.html>`__
    """

    props: PropsDictType = {
        "AllocationId": (str, False),
        "IPv6Address": (str, False),
        "PrivateIPv4Address": (str, False),
        "SourceNatIpv6Prefix": (str, False),
        "SubnetId": (str, True),
    }


class LoadBalancer(AWSObject):
    """
    `LoadBalancer <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-elasticloadbalancingv2-loadbalancer.html>`__
    """

    resource_type = "AWS::ElasticLoadBalancingV2::LoadBalancer"

    props: PropsDictType = {
        "EnablePrefixForIpv6SourceNat": (str, False),
        "EnforceSecurityGroupInboundRulesOnPrivateLinkTraffic": (str, False),
        "IpAddressType": (str, False),
        "Ipv4IpamPoolId": (str, False),
        "LoadBalancerAttributes": ([LoadBalancerAttributes], False),
        "MinimumLoadBalancerCapacity": (MinimumLoadBalancerCapacity, False),
        "Name": (validate_elb_name, False),
        "Scheme": (str, False),
        "SecurityGroups": ([str], False),
        "SubnetMappings": ([SubnetMapping], False),
        "Subnets": ([str], False),
        "Tags": (validate_tags_or_list, False),
        "Type": (str, False),
    }

    def validate(self):
        validate_loadbalancer(self)


class Matcher(AWSProperty):
    """
    `Matcher <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-targetgroup-matcher.html>`__
    """

    props: PropsDictType = {
        "GrpcCode": (str, False),
        "HttpCode": (str, False),
    }


class TargetDescription(AWSProperty):
    """
    `TargetDescription <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-targetgroup-targetdescription.html>`__
    """

    props: PropsDictType = {
        "AvailabilityZone": (str, False),
        "Id": (str, True),
        "Port": (validate_network_port, False),
    }


class TargetGroupAttribute(AWSProperty):
    """
    `TargetGroupAttribute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-targetgroup-targetgroupattribute.html>`__
    """

    props: PropsDictType = {
        "Key": (str, False),
        "Value": (str, False),
    }


class TargetGroup(AWSObject):
    """
    `TargetGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-elasticloadbalancingv2-targetgroup.html>`__
    """

    resource_type = "AWS::ElasticLoadBalancingV2::TargetGroup"

    props: PropsDictType = {
        "HealthCheckEnabled": (boolean, False),
        "HealthCheckIntervalSeconds": (integer, False),
        "HealthCheckPath": (str, False),
        "HealthCheckPort": (tg_healthcheck_port, False),
        "HealthCheckProtocol": (str, False),
        "HealthCheckTimeoutSeconds": (integer, False),
        "HealthyThresholdCount": (integer, False),
        "IpAddressType": (str, False),
        "Matcher": (Matcher, False),
        "Name": (str, False),
        "Port": (validate_network_port, False),
        "Protocol": (str, False),
        "ProtocolVersion": (str, False),
        "Tags": (validate_tags_or_list, False),
        "TargetGroupAttributes": ([TargetGroupAttribute], False),
        "TargetType": (validate_target_type, False),
        "Targets": ([TargetDescription], False),
        "UnhealthyThresholdCount": (integer, False),
        "VpcId": (str, False),
    }

    def validate(self):
        validate_target_group(self)


class TrustStore(AWSObject):
    """
    `TrustStore <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-elasticloadbalancingv2-truststore.html>`__
    """

    resource_type = "AWS::ElasticLoadBalancingV2::TrustStore"

    props: PropsDictType = {
        "CaCertificatesBundleS3Bucket": (str, False),
        "CaCertificatesBundleS3Key": (str, False),
        "CaCertificatesBundleS3ObjectVersion": (str, False),
        "Name": (str, False),
        "Tags": (Tags, False),
    }


class RevocationContent(AWSProperty):
    """
    `RevocationContent <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-truststorerevocation-revocationcontent.html>`__
    """

    props: PropsDictType = {
        "RevocationType": (str, False),
        "S3Bucket": (str, False),
        "S3Key": (str, False),
        "S3ObjectVersion": (str, False),
    }


class TrustStoreRevocation(AWSObject):
    """
    `TrustStoreRevocation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-elasticloadbalancingv2-truststorerevocation.html>`__
    """

    resource_type = "AWS::ElasticLoadBalancingV2::TrustStoreRevocation"

    props: PropsDictType = {
        "RevocationContents": ([RevocationContent], False),
        "TrustStoreArn": (str, False),
    }


class TrustStoreRevocationProperty(AWSProperty):
    """
    `TrustStoreRevocationProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-elasticloadbalancingv2-truststorerevocation-truststorerevocation.html>`__
    """

    props: PropsDictType = {
        "NumberOfRevokedEntries": (integer, False),
        "RevocationId": (str, False),
        "RevocationType": (str, False),
        "TrustStoreArn": (str, False),
    }
