# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType
from .validators import boolean, double, integer
from .validators.apigatewayv2 import (
    dict_or_string,
    validate_authorizer_ttl,
    validate_authorizer_type,
    validate_content_handling_strategy,
    validate_integration_type,
    validate_logging_level,
    validate_model,
    validate_passthrough_behavior,
    validate_timeout_in_millis,
)


class BodyS3Location(AWSProperty):
    """
    `BodyS3Location <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-apigatewayv2-api-bodys3location.html>`__
    """

    props: PropsDictType = {
        "Bucket": (str, False),
        "Etag": (str, False),
        "Key": (str, False),
        "Version": (str, False),
    }


class Cors(AWSProperty):
    """
    `Cors <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-apigatewayv2-api-cors.html>`__
    """

    props: PropsDictType = {
        "AllowCredentials": (boolean, False),
        "AllowHeaders": ([str], False),
        "AllowMethods": ([str], False),
        "AllowOrigins": ([str], False),
        "ExposeHeaders": ([str], False),
        "MaxAge": (integer, False),
    }


class Api(AWSObject):
    """
    `Api <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-apigatewayv2-api.html>`__
    """

    resource_type = "AWS::ApiGatewayV2::Api"

    props: PropsDictType = {
        "ApiKeySelectionExpression": (str, False),
        "BasePath": (str, False),
        "Body": (dict, False),
        "BodyS3Location": (BodyS3Location, False),
        "CorsConfiguration": (Cors, False),
        "CredentialsArn": (str, False),
        "Description": (str, False),
        "DisableExecuteApiEndpoint": (boolean, False),
        "DisableSchemaValidation": (boolean, False),
        "FailOnWarnings": (boolean, False),
        "IpAddressType": (str, False),
        "Name": (str, False),
        "ProtocolType": (str, False),
        "RouteKey": (str, False),
        "RouteSelectionExpression": (str, False),
        "Tags": (dict, False),
        "Target": (str, False),
        "Version": (str, False),
    }


class IntegrationOverrides(AWSProperty):
    """
    `IntegrationOverrides <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-apigatewayv2-apigatewaymanagedoverrides-integrationoverrides.html>`__
    """

    props: PropsDictType = {
        "Description": (str, False),
        "IntegrationMethod": (str, False),
        "PayloadFormatVersion": (str, False),
        "TimeoutInMillis": (integer, False),
    }


class RouteOverrides(AWSProperty):
    """
    `RouteOverrides <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-apigatewayv2-apigatewaymanagedoverrides-routeoverrides.html>`__
    """

    props: PropsDictType = {
        "AuthorizationScopes": ([str], False),
        "AuthorizationType": (str, False),
        "AuthorizerId": (str, False),
        "OperationName": (str, False),
        "Target": (str, False),
    }


class AccessLogSettings(AWSProperty):
    """
    `AccessLogSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-apigatewayv2-stage-accesslogsettings.html>`__
    """

    props: PropsDictType = {
        "DestinationArn": (str, False),
        "Format": (str, False),
    }


class RouteSettings(AWSProperty):
    """
    `RouteSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-apigatewayv2-stage-routesettings.html>`__
    """

    props: PropsDictType = {
        "DataTraceEnabled": (boolean, False),
        "DetailedMetricsEnabled": (boolean, False),
        "LoggingLevel": (validate_logging_level, False),
        "ThrottlingBurstLimit": (integer, False),
        "ThrottlingRateLimit": (double, False),
    }


class StageOverrides(AWSProperty):
    """
    `StageOverrides <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-apigatewayv2-apigatewaymanagedoverrides-stageoverrides.html>`__
    """

    props: PropsDictType = {
        "AccessLogSettings": (AccessLogSettings, False),
        "AutoDeploy": (boolean, False),
        "DefaultRouteSettings": (RouteSettings, False),
        "Description": (str, False),
        "RouteSettings": (dict, False),
        "StageVariables": (dict, False),
    }


class ApiGatewayManagedOverrides(AWSObject):
    """
    `ApiGatewayManagedOverrides <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-apigatewayv2-apigatewaymanagedoverrides.html>`__
    """

    resource_type = "AWS::ApiGatewayV2::ApiGatewayManagedOverrides"

    props: PropsDictType = {
        "ApiId": (str, True),
        "Integration": (IntegrationOverrides, False),
        "Route": (RouteOverrides, False),
        "Stage": (StageOverrides, False),
    }


class ApiMapping(AWSObject):
    """
    `ApiMapping <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-apigatewayv2-apimapping.html>`__
    """

    resource_type = "AWS::ApiGatewayV2::ApiMapping"

    props: PropsDictType = {
        "ApiId": (str, True),
        "ApiMappingKey": (str, False),
        "DomainName": (str, True),
        "Stage": (str, True),
    }


class JWTConfiguration(AWSProperty):
    """
    `JWTConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-apigatewayv2-authorizer-jwtconfiguration.html>`__
    """

    props: PropsDictType = {
        "Audience": ([str], False),
        "Issuer": (str, False),
    }


class Authorizer(AWSObject):
    """
    `Authorizer <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-apigatewayv2-authorizer.html>`__
    """

    resource_type = "AWS::ApiGatewayV2::Authorizer"

    props: PropsDictType = {
        "ApiId": (str, True),
        "AuthorizerCredentialsArn": (str, False),
        "AuthorizerPayloadFormatVersion": (str, False),
        "AuthorizerResultTtlInSeconds": (validate_authorizer_ttl, False),
        "AuthorizerType": (validate_authorizer_type, True),
        "AuthorizerUri": (str, False),
        "EnableSimpleResponses": (boolean, False),
        "IdentitySource": ([str], False),
        "IdentityValidationExpression": (str, False),
        "JwtConfiguration": (JWTConfiguration, False),
        "Name": (str, True),
    }


class Deployment(AWSObject):
    """
    `Deployment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-apigatewayv2-deployment.html>`__
    """

    resource_type = "AWS::ApiGatewayV2::Deployment"

    props: PropsDictType = {
        "ApiId": (str, True),
        "Description": (str, False),
        "StageName": (str, False),
    }


class DomainNameConfiguration(AWSProperty):
    """
    `DomainNameConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-apigatewayv2-domainname-domainnameconfiguration.html>`__
    """

    props: PropsDictType = {
        "CertificateArn": (str, False),
        "CertificateName": (str, False),
        "EndpointType": (str, False),
        "IpAddressType": (str, False),
        "OwnershipVerificationCertificateArn": (str, False),
        "SecurityPolicy": (str, False),
    }


class MutualTlsAuthentication(AWSProperty):
    """
    `MutualTlsAuthentication <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-apigatewayv2-domainname-mutualtlsauthentication.html>`__
    """

    props: PropsDictType = {
        "TruststoreUri": (str, False),
        "TruststoreVersion": (str, False),
    }


class DomainName(AWSObject):
    """
    `DomainName <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-apigatewayv2-domainname.html>`__
    """

    resource_type = "AWS::ApiGatewayV2::DomainName"

    props: PropsDictType = {
        "DomainName": (str, True),
        "DomainNameConfigurations": ([DomainNameConfiguration], False),
        "MutualTlsAuthentication": (MutualTlsAuthentication, False),
        "Tags": (dict, False),
    }


class ResponseParameter(AWSProperty):
    """
    `ResponseParameter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-apigatewayv2-integration-responseparameter.html>`__
    """

    props: PropsDictType = {
        "Destination": (str, False),
        "Source": (str, False),
    }


class ResponseParameterMap(AWSProperty):
    """
    `ResponseParameterMap <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-apigatewayv2-integration-responseparametermap.html>`__
    """

    props: PropsDictType = {
        "ResponseParameters": ([ResponseParameter], False),
    }


class TlsConfig(AWSProperty):
    """
    `TlsConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-apigatewayv2-integration-tlsconfig.html>`__
    """

    props: PropsDictType = {
        "ServerNameToVerify": (str, False),
    }


class Integration(AWSObject):
    """
    `Integration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-apigatewayv2-integration.html>`__
    """

    resource_type = "AWS::ApiGatewayV2::Integration"

    props: PropsDictType = {
        "ApiId": (str, True),
        "ConnectionId": (str, False),
        "ConnectionType": (str, False),
        "ContentHandlingStrategy": (validate_content_handling_strategy, False),
        "CredentialsArn": (str, False),
        "Description": (str, False),
        "IntegrationMethod": (str, False),
        "IntegrationSubtype": (str, False),
        "IntegrationType": (validate_integration_type, True),
        "IntegrationUri": (str, False),
        "PassthroughBehavior": (validate_passthrough_behavior, False),
        "PayloadFormatVersion": (str, False),
        "RequestParameters": (dict, False),
        "RequestTemplates": (dict, False),
        "ResponseParameters": (dict, False),
        "TemplateSelectionExpression": (str, False),
        "TimeoutInMillis": (validate_timeout_in_millis, False),
        "TlsConfig": (TlsConfig, False),
    }


class IntegrationResponse(AWSObject):
    """
    `IntegrationResponse <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-apigatewayv2-integrationresponse.html>`__
    """

    resource_type = "AWS::ApiGatewayV2::IntegrationResponse"

    props: PropsDictType = {
        "ApiId": (str, True),
        "ContentHandlingStrategy": (validate_content_handling_strategy, False),
        "IntegrationId": (str, True),
        "IntegrationResponseKey": (str, True),
        "ResponseParameters": (dict, False),
        "ResponseTemplates": (dict, False),
        "TemplateSelectionExpression": (str, False),
    }


class Model(AWSObject):
    """
    `Model <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-apigatewayv2-model.html>`__
    """

    resource_type = "AWS::ApiGatewayV2::Model"

    props: PropsDictType = {
        "ApiId": (str, True),
        "ContentType": (str, False),
        "Description": (str, False),
        "Name": (str, True),
        "Schema": (dict_or_string, True),
    }

    def validate(self):
        validate_model(self)


class Route(AWSObject):
    """
    `Route <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-apigatewayv2-route.html>`__
    """

    resource_type = "AWS::ApiGatewayV2::Route"

    props: PropsDictType = {
        "ApiId": (str, True),
        "ApiKeyRequired": (boolean, False),
        "AuthorizationScopes": ([str], False),
        "AuthorizationType": (str, False),
        "AuthorizerId": (str, False),
        "ModelSelectionExpression": (str, False),
        "OperationName": (str, False),
        "RequestModels": (dict, False),
        "RequestParameters": (dict, False),
        "RouteKey": (str, True),
        "RouteResponseSelectionExpression": (str, False),
        "Target": (str, False),
    }


class ParameterConstraints(AWSProperty):
    """
    `ParameterConstraints <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-apigatewayv2-routeresponse-parameterconstraints.html>`__
    """

    props: PropsDictType = {
        "Required": (boolean, True),
    }


class RouteResponse(AWSObject):
    """
    `RouteResponse <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-apigatewayv2-routeresponse.html>`__
    """

    resource_type = "AWS::ApiGatewayV2::RouteResponse"

    props: PropsDictType = {
        "ApiId": (str, True),
        "ModelSelectionExpression": (str, False),
        "ResponseModels": (dict, False),
        "ResponseParameters": (dict, False),
        "RouteId": (str, True),
        "RouteResponseKey": (str, True),
    }


class Stage(AWSObject):
    """
    `Stage <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-apigatewayv2-stage.html>`__
    """

    resource_type = "AWS::ApiGatewayV2::Stage"

    props: PropsDictType = {
        "AccessLogSettings": (AccessLogSettings, False),
        "AccessPolicyId": (str, False),
        "ApiId": (str, True),
        "AutoDeploy": (boolean, False),
        "ClientCertificateId": (str, False),
        "DefaultRouteSettings": (RouteSettings, False),
        "DeploymentId": (str, False),
        "Description": (str, False),
        "RouteSettings": (dict, False),
        "StageName": (str, True),
        "StageVariables": (dict, False),
        "Tags": (dict, False),
    }


class VpcLink(AWSObject):
    """
    `VpcLink <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-apigatewayv2-vpclink.html>`__
    """

    resource_type = "AWS::ApiGatewayV2::VpcLink"

    props: PropsDictType = {
        "Name": (str, True),
        "SecurityGroupIds": ([str], False),
        "SubnetIds": ([str], True),
        "Tags": (dict, False),
    }
