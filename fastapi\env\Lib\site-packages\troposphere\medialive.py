# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer


class AnywhereSettings(AWSProperty):
    """
    `AnywhereSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-anywheresettings.html>`__
    """

    props: PropsDictType = {
        "ChannelPlacementGroupId": (str, False),
        "ClusterId": (str, False),
    }


class CdiInputSpecification(AWSProperty):
    """
    `CdiInputSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-cdiinputspecification.html>`__
    """

    props: PropsDictType = {
        "Resolution": (str, False),
    }


class ChannelEngineVersionRequest(AWSProperty):
    """
    `ChannelEngineVersionRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-channelengineversionrequest.html>`__
    """

    props: PropsDictType = {
        "Version": (str, False),
    }


class AacSettings(AWSProperty):
    """
    `AacSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-aacsettings.html>`__
    """

    props: PropsDictType = {
        "Bitrate": (double, False),
        "CodingMode": (str, False),
        "InputType": (str, False),
        "Profile": (str, False),
        "RateControlMode": (str, False),
        "RawFormat": (str, False),
        "SampleRate": (double, False),
        "Spec": (str, False),
        "VbrQuality": (str, False),
    }


class Ac3Settings(AWSProperty):
    """
    `Ac3Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-ac3settings.html>`__
    """

    props: PropsDictType = {
        "AttenuationControl": (str, False),
        "Bitrate": (double, False),
        "BitstreamMode": (str, False),
        "CodingMode": (str, False),
        "Dialnorm": (integer, False),
        "DrcProfile": (str, False),
        "LfeFilter": (str, False),
        "MetadataControl": (str, False),
    }


class Eac3AtmosSettings(AWSProperty):
    """
    `Eac3AtmosSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-eac3atmossettings.html>`__
    """

    props: PropsDictType = {
        "Bitrate": (double, False),
        "CodingMode": (str, False),
        "Dialnorm": (integer, False),
        "DrcLine": (str, False),
        "DrcRf": (str, False),
        "HeightTrim": (double, False),
        "SurroundTrim": (double, False),
    }


class Eac3Settings(AWSProperty):
    """
    `Eac3Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-eac3settings.html>`__
    """

    props: PropsDictType = {
        "AttenuationControl": (str, False),
        "Bitrate": (double, False),
        "BitstreamMode": (str, False),
        "CodingMode": (str, False),
        "DcFilter": (str, False),
        "Dialnorm": (integer, False),
        "DrcLine": (str, False),
        "DrcRf": (str, False),
        "LfeControl": (str, False),
        "LfeFilter": (str, False),
        "LoRoCenterMixLevel": (double, False),
        "LoRoSurroundMixLevel": (double, False),
        "LtRtCenterMixLevel": (double, False),
        "LtRtSurroundMixLevel": (double, False),
        "MetadataControl": (str, False),
        "PassthroughControl": (str, False),
        "PhaseControl": (str, False),
        "StereoDownmix": (str, False),
        "SurroundExMode": (str, False),
        "SurroundMode": (str, False),
    }


class Mp2Settings(AWSProperty):
    """
    `Mp2Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-mp2settings.html>`__
    """

    props: PropsDictType = {
        "Bitrate": (double, False),
        "CodingMode": (str, False),
        "SampleRate": (double, False),
    }


class PassThroughSettings(AWSProperty):
    """
    `PassThroughSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-passthroughsettings.html>`__
    """

    props: PropsDictType = {}


class WavSettings(AWSProperty):
    """
    `WavSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-wavsettings.html>`__
    """

    props: PropsDictType = {
        "BitDepth": (double, False),
        "CodingMode": (str, False),
        "SampleRate": (double, False),
    }


class AudioCodecSettings(AWSProperty):
    """
    `AudioCodecSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-audiocodecsettings.html>`__
    """

    props: PropsDictType = {
        "AacSettings": (AacSettings, False),
        "Ac3Settings": (Ac3Settings, False),
        "Eac3AtmosSettings": (Eac3AtmosSettings, False),
        "Eac3Settings": (Eac3Settings, False),
        "Mp2Settings": (Mp2Settings, False),
        "PassThroughSettings": (PassThroughSettings, False),
        "WavSettings": (WavSettings, False),
    }


class AudioNormalizationSettings(AWSProperty):
    """
    `AudioNormalizationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-audionormalizationsettings.html>`__
    """

    props: PropsDictType = {
        "Algorithm": (str, False),
        "AlgorithmControl": (str, False),
        "TargetLkfs": (double, False),
    }


class NielsenCBET(AWSProperty):
    """
    `NielsenCBET <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-nielsencbet.html>`__
    """

    props: PropsDictType = {
        "CbetCheckDigitString": (str, False),
        "CbetStepaside": (str, False),
        "Csid": (str, False),
    }


class NielsenNaesIiNw(AWSProperty):
    """
    `NielsenNaesIiNw <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-nielsennaesiinw.html>`__
    """

    props: PropsDictType = {
        "CheckDigitString": (str, False),
        "Sid": (double, False),
        "Timezone": (str, False),
    }


class NielsenWatermarksSettings(AWSProperty):
    """
    `NielsenWatermarksSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-nielsenwatermarkssettings.html>`__
    """

    props: PropsDictType = {
        "NielsenCbetSettings": (NielsenCBET, False),
        "NielsenDistributionType": (str, False),
        "NielsenNaesIiNwSettings": (NielsenNaesIiNw, False),
    }


class AudioWatermarkSettings(AWSProperty):
    """
    `AudioWatermarkSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-audiowatermarksettings.html>`__
    """

    props: PropsDictType = {
        "NielsenWatermarksSettings": (NielsenWatermarksSettings, False),
    }


class InputChannelLevel(AWSProperty):
    """
    `InputChannelLevel <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-inputchannellevel.html>`__
    """

    props: PropsDictType = {
        "Gain": (integer, False),
        "InputChannel": (integer, False),
    }


class AudioChannelMapping(AWSProperty):
    """
    `AudioChannelMapping <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-audiochannelmapping.html>`__
    """

    props: PropsDictType = {
        "InputChannelLevels": ([InputChannelLevel], False),
        "OutputChannel": (integer, False),
    }


class RemixSettings(AWSProperty):
    """
    `RemixSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-remixsettings.html>`__
    """

    props: PropsDictType = {
        "ChannelMappings": ([AudioChannelMapping], False),
        "ChannelsIn": (integer, False),
        "ChannelsOut": (integer, False),
    }


class AudioDescription(AWSProperty):
    """
    `AudioDescription <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-audiodescription.html>`__
    """

    props: PropsDictType = {
        "AudioDashRoles": ([str], False),
        "AudioNormalizationSettings": (AudioNormalizationSettings, False),
        "AudioSelectorName": (str, False),
        "AudioType": (str, False),
        "AudioTypeControl": (str, False),
        "AudioWatermarkingSettings": (AudioWatermarkSettings, False),
        "CodecSettings": (AudioCodecSettings, False),
        "DvbDashAccessibility": (str, False),
        "LanguageCode": (str, False),
        "LanguageCodeControl": (str, False),
        "Name": (str, False),
        "RemixSettings": (RemixSettings, False),
        "StreamName": (str, False),
    }


class InputLocation(AWSProperty):
    """
    `InputLocation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-inputlocation.html>`__
    """

    props: PropsDictType = {
        "PasswordParam": (str, False),
        "Uri": (str, False),
        "Username": (str, False),
    }


class AvailBlanking(AWSProperty):
    """
    `AvailBlanking <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-availblanking.html>`__
    """

    props: PropsDictType = {
        "AvailBlankingImage": (InputLocation, False),
        "State": (str, False),
    }


class Esam(AWSProperty):
    """
    `Esam <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-esam.html>`__
    """

    props: PropsDictType = {
        "AcquisitionPointId": (str, False),
        "AdAvailOffset": (integer, False),
        "PasswordParam": (str, False),
        "PoisEndpoint": (str, False),
        "Username": (str, False),
        "ZoneIdentity": (str, False),
    }


class Scte35SpliceInsert(AWSProperty):
    """
    `Scte35SpliceInsert <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-scte35spliceinsert.html>`__
    """

    props: PropsDictType = {
        "AdAvailOffset": (integer, False),
        "NoRegionalBlackoutFlag": (str, False),
        "WebDeliveryAllowedFlag": (str, False),
    }


class Scte35TimeSignalApos(AWSProperty):
    """
    `Scte35TimeSignalApos <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-scte35timesignalapos.html>`__
    """

    props: PropsDictType = {
        "AdAvailOffset": (integer, False),
        "NoRegionalBlackoutFlag": (str, False),
        "WebDeliveryAllowedFlag": (str, False),
    }


class AvailSettings(AWSProperty):
    """
    `AvailSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-availsettings.html>`__
    """

    props: PropsDictType = {
        "Esam": (Esam, False),
        "Scte35SpliceInsert": (Scte35SpliceInsert, False),
        "Scte35TimeSignalApos": (Scte35TimeSignalApos, False),
    }


class AvailConfiguration(AWSProperty):
    """
    `AvailConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-availconfiguration.html>`__
    """

    props: PropsDictType = {
        "AvailSettings": (AvailSettings, False),
        "Scte35SegmentationScope": (str, False),
    }


class BlackoutSlate(AWSProperty):
    """
    `BlackoutSlate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-blackoutslate.html>`__
    """

    props: PropsDictType = {
        "BlackoutSlateImage": (InputLocation, False),
        "NetworkEndBlackout": (str, False),
        "NetworkEndBlackoutImage": (InputLocation, False),
        "NetworkId": (str, False),
        "State": (str, False),
    }


class AribDestinationSettings(AWSProperty):
    """
    `AribDestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-aribdestinationsettings.html>`__
    """

    props: PropsDictType = {}


class BurnInDestinationSettings(AWSProperty):
    """
    `BurnInDestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-burnindestinationsettings.html>`__
    """

    props: PropsDictType = {
        "Alignment": (str, False),
        "BackgroundColor": (str, False),
        "BackgroundOpacity": (integer, False),
        "Font": (InputLocation, False),
        "FontColor": (str, False),
        "FontOpacity": (integer, False),
        "FontResolution": (integer, False),
        "FontSize": (str, False),
        "OutlineColor": (str, False),
        "OutlineSize": (integer, False),
        "ShadowColor": (str, False),
        "ShadowOpacity": (integer, False),
        "ShadowXOffset": (integer, False),
        "ShadowYOffset": (integer, False),
        "TeletextGridControl": (str, False),
        "XPosition": (integer, False),
        "YPosition": (integer, False),
    }


class DvbSubDestinationSettings(AWSProperty):
    """
    `DvbSubDestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-dvbsubdestinationsettings.html>`__
    """

    props: PropsDictType = {
        "Alignment": (str, False),
        "BackgroundColor": (str, False),
        "BackgroundOpacity": (integer, False),
        "Font": (InputLocation, False),
        "FontColor": (str, False),
        "FontOpacity": (integer, False),
        "FontResolution": (integer, False),
        "FontSize": (str, False),
        "OutlineColor": (str, False),
        "OutlineSize": (integer, False),
        "ShadowColor": (str, False),
        "ShadowOpacity": (integer, False),
        "ShadowXOffset": (integer, False),
        "ShadowYOffset": (integer, False),
        "TeletextGridControl": (str, False),
        "XPosition": (integer, False),
        "YPosition": (integer, False),
    }


class EbuTtDDestinationSettings(AWSProperty):
    """
    `EbuTtDDestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-ebuttddestinationsettings.html>`__
    """

    props: PropsDictType = {
        "CopyrightHolder": (str, False),
        "FillLineGap": (str, False),
        "FontFamily": (str, False),
        "StyleControl": (str, False),
    }


class EmbeddedDestinationSettings(AWSProperty):
    """
    `EmbeddedDestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-embeddeddestinationsettings.html>`__
    """

    props: PropsDictType = {}


class EmbeddedPlusScte20DestinationSettings(AWSProperty):
    """
    `EmbeddedPlusScte20DestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-embeddedplusscte20destinationsettings.html>`__
    """

    props: PropsDictType = {}


class RtmpCaptionInfoDestinationSettings(AWSProperty):
    """
    `RtmpCaptionInfoDestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-rtmpcaptioninfodestinationsettings.html>`__
    """

    props: PropsDictType = {}


class Scte20PlusEmbeddedDestinationSettings(AWSProperty):
    """
    `Scte20PlusEmbeddedDestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-scte20plusembeddeddestinationsettings.html>`__
    """

    props: PropsDictType = {}


class Scte27DestinationSettings(AWSProperty):
    """
    `Scte27DestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-scte27destinationsettings.html>`__
    """

    props: PropsDictType = {}


class SmpteTtDestinationSettings(AWSProperty):
    """
    `SmpteTtDestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-smptettdestinationsettings.html>`__
    """

    props: PropsDictType = {}


class TeletextDestinationSettings(AWSProperty):
    """
    `TeletextDestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-teletextdestinationsettings.html>`__
    """

    props: PropsDictType = {}


class TtmlDestinationSettings(AWSProperty):
    """
    `TtmlDestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-ttmldestinationsettings.html>`__
    """

    props: PropsDictType = {
        "StyleControl": (str, False),
    }


class WebvttDestinationSettings(AWSProperty):
    """
    `WebvttDestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-webvttdestinationsettings.html>`__
    """

    props: PropsDictType = {
        "StyleControl": (str, False),
    }


class CaptionDestinationSettings(AWSProperty):
    """
    `CaptionDestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-captiondestinationsettings.html>`__
    """

    props: PropsDictType = {
        "AribDestinationSettings": (AribDestinationSettings, False),
        "BurnInDestinationSettings": (BurnInDestinationSettings, False),
        "DvbSubDestinationSettings": (DvbSubDestinationSettings, False),
        "EbuTtDDestinationSettings": (EbuTtDDestinationSettings, False),
        "EmbeddedDestinationSettings": (EmbeddedDestinationSettings, False),
        "EmbeddedPlusScte20DestinationSettings": (
            EmbeddedPlusScte20DestinationSettings,
            False,
        ),
        "RtmpCaptionInfoDestinationSettings": (
            RtmpCaptionInfoDestinationSettings,
            False,
        ),
        "Scte20PlusEmbeddedDestinationSettings": (
            Scte20PlusEmbeddedDestinationSettings,
            False,
        ),
        "Scte27DestinationSettings": (Scte27DestinationSettings, False),
        "SmpteTtDestinationSettings": (SmpteTtDestinationSettings, False),
        "TeletextDestinationSettings": (TeletextDestinationSettings, False),
        "TtmlDestinationSettings": (TtmlDestinationSettings, False),
        "WebvttDestinationSettings": (WebvttDestinationSettings, False),
    }


class CaptionDescription(AWSProperty):
    """
    `CaptionDescription <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-captiondescription.html>`__
    """

    props: PropsDictType = {
        "Accessibility": (str, False),
        "CaptionDashRoles": ([str], False),
        "CaptionSelectorName": (str, False),
        "DestinationSettings": (CaptionDestinationSettings, False),
        "DvbDashAccessibility": (str, False),
        "LanguageCode": (str, False),
        "LanguageDescription": (str, False),
        "Name": (str, False),
    }


class ColorCorrection(AWSProperty):
    """
    `ColorCorrection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-colorcorrection.html>`__
    """

    props: PropsDictType = {
        "InputColorSpace": (str, False),
        "OutputColorSpace": (str, False),
        "Uri": (str, False),
    }


class ColorCorrectionSettings(AWSProperty):
    """
    `ColorCorrectionSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-colorcorrectionsettings.html>`__
    """

    props: PropsDictType = {
        "GlobalColorCorrections": ([ColorCorrection], False),
    }


class FeatureActivations(AWSProperty):
    """
    `FeatureActivations <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-featureactivations.html>`__
    """

    props: PropsDictType = {
        "InputPrepareScheduleActions": (str, False),
        "OutputStaticImageOverlayScheduleActions": (str, False),
    }


class InputLossBehavior(AWSProperty):
    """
    `InputLossBehavior <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-inputlossbehavior.html>`__
    """

    props: PropsDictType = {
        "BlackFrameMsec": (integer, False),
        "InputLossImageColor": (str, False),
        "InputLossImageSlate": (InputLocation, False),
        "InputLossImageType": (str, False),
        "RepeatFrameMsec": (integer, False),
    }


class EpochLockingSettings(AWSProperty):
    """
    `EpochLockingSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-epochlockingsettings.html>`__
    """

    props: PropsDictType = {
        "CustomEpoch": (str, False),
        "JamSyncTime": (str, False),
    }


class PipelineLockingSettings(AWSProperty):
    """
    `PipelineLockingSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-pipelinelockingsettings.html>`__
    """

    props: PropsDictType = {}


class OutputLockingSettings(AWSProperty):
    """
    `OutputLockingSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-outputlockingsettings.html>`__
    """

    props: PropsDictType = {
        "EpochLockingSettings": (EpochLockingSettings, False),
        "PipelineLockingSettings": (PipelineLockingSettings, False),
    }


class GlobalConfiguration(AWSProperty):
    """
    `GlobalConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-globalconfiguration.html>`__
    """

    props: PropsDictType = {
        "InitialAudioGain": (integer, False),
        "InputEndAction": (str, False),
        "InputLossBehavior": (InputLossBehavior, False),
        "OutputLockingMode": (str, False),
        "OutputLockingSettings": (OutputLockingSettings, False),
        "OutputTimingSource": (str, False),
        "SupportLowFramerateInputs": (str, False),
    }


class HtmlMotionGraphicsSettings(AWSProperty):
    """
    `HtmlMotionGraphicsSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-htmlmotiongraphicssettings.html>`__
    """

    props: PropsDictType = {}


class MotionGraphicsSettings(AWSProperty):
    """
    `MotionGraphicsSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-motiongraphicssettings.html>`__
    """

    props: PropsDictType = {
        "HtmlMotionGraphicsSettings": (HtmlMotionGraphicsSettings, False),
    }


class MotionGraphicsConfiguration(AWSProperty):
    """
    `MotionGraphicsConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-motiongraphicsconfiguration.html>`__
    """

    props: PropsDictType = {
        "MotionGraphicsInsertion": (str, False),
        "MotionGraphicsSettings": (MotionGraphicsSettings, False),
    }


class NielsenConfiguration(AWSProperty):
    """
    `NielsenConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-nielsenconfiguration.html>`__
    """

    props: PropsDictType = {
        "DistributorId": (str, False),
        "NielsenPcmToId3Tagging": (str, False),
    }


class DvbNitSettings(AWSProperty):
    """
    `DvbNitSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-dvbnitsettings.html>`__
    """

    props: PropsDictType = {
        "NetworkId": (integer, False),
        "NetworkName": (str, False),
        "RepInterval": (integer, False),
    }


class DvbSdtSettings(AWSProperty):
    """
    `DvbSdtSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-dvbsdtsettings.html>`__
    """

    props: PropsDictType = {
        "OutputSdt": (str, False),
        "RepInterval": (integer, False),
        "ServiceName": (str, False),
        "ServiceProviderName": (str, False),
    }


class DvbTdtSettings(AWSProperty):
    """
    `DvbTdtSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-dvbtdtsettings.html>`__
    """

    props: PropsDictType = {
        "RepInterval": (integer, False),
    }


class M2tsSettings(AWSProperty):
    """
    `M2tsSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-m2tssettings.html>`__
    """

    props: PropsDictType = {
        "AbsentInputAudioBehavior": (str, False),
        "Arib": (str, False),
        "AribCaptionsPid": (str, False),
        "AribCaptionsPidControl": (str, False),
        "AudioBufferModel": (str, False),
        "AudioFramesPerPes": (integer, False),
        "AudioPids": (str, False),
        "AudioStreamType": (str, False),
        "Bitrate": (integer, False),
        "BufferModel": (str, False),
        "CcDescriptor": (str, False),
        "DvbNitSettings": (DvbNitSettings, False),
        "DvbSdtSettings": (DvbSdtSettings, False),
        "DvbSubPids": (str, False),
        "DvbTdtSettings": (DvbTdtSettings, False),
        "DvbTeletextPid": (str, False),
        "Ebif": (str, False),
        "EbpAudioInterval": (str, False),
        "EbpLookaheadMs": (integer, False),
        "EbpPlacement": (str, False),
        "EcmPid": (str, False),
        "EsRateInPes": (str, False),
        "EtvPlatformPid": (str, False),
        "EtvSignalPid": (str, False),
        "FragmentTime": (double, False),
        "Klv": (str, False),
        "KlvDataPids": (str, False),
        "NielsenId3Behavior": (str, False),
        "NullPacketBitrate": (double, False),
        "PatInterval": (integer, False),
        "PcrControl": (str, False),
        "PcrPeriod": (integer, False),
        "PcrPid": (str, False),
        "PmtInterval": (integer, False),
        "PmtPid": (str, False),
        "ProgramNum": (integer, False),
        "RateMode": (str, False),
        "Scte27Pids": (str, False),
        "Scte35Control": (str, False),
        "Scte35Pid": (str, False),
        "Scte35PrerollPullupMilliseconds": (double, False),
        "SegmentationMarkers": (str, False),
        "SegmentationStyle": (str, False),
        "SegmentationTime": (double, False),
        "TimedMetadataBehavior": (str, False),
        "TimedMetadataPid": (str, False),
        "TransportStreamId": (integer, False),
        "VideoPid": (str, False),
    }


class RawSettings(AWSProperty):
    """
    `RawSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-rawsettings.html>`__
    """

    props: PropsDictType = {}


class ArchiveContainerSettings(AWSProperty):
    """
    `ArchiveContainerSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-archivecontainersettings.html>`__
    """

    props: PropsDictType = {
        "M2tsSettings": (M2tsSettings, False),
        "RawSettings": (RawSettings, False),
    }


class ArchiveOutputSettings(AWSProperty):
    """
    `ArchiveOutputSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-archiveoutputsettings.html>`__
    """

    props: PropsDictType = {
        "ContainerSettings": (ArchiveContainerSettings, False),
        "Extension": (str, False),
        "NameModifier": (str, False),
    }


class CmafIngestOutputSettings(AWSProperty):
    """
    `CmafIngestOutputSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-cmafingestoutputsettings.html>`__
    """

    props: PropsDictType = {
        "NameModifier": (str, False),
    }


class FrameCaptureOutputSettings(AWSProperty):
    """
    `FrameCaptureOutputSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-framecaptureoutputsettings.html>`__
    """

    props: PropsDictType = {
        "NameModifier": (str, False),
    }


class AudioOnlyHlsSettings(AWSProperty):
    """
    `AudioOnlyHlsSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-audioonlyhlssettings.html>`__
    """

    props: PropsDictType = {
        "AudioGroupId": (str, False),
        "AudioOnlyImage": (InputLocation, False),
        "AudioTrackType": (str, False),
        "SegmentType": (str, False),
    }


class Fmp4HlsSettings(AWSProperty):
    """
    `Fmp4HlsSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-fmp4hlssettings.html>`__
    """

    props: PropsDictType = {
        "AudioRenditionSets": (str, False),
        "NielsenId3Behavior": (str, False),
        "TimedMetadataBehavior": (str, False),
    }


class FrameCaptureHlsSettings(AWSProperty):
    """
    `FrameCaptureHlsSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-framecapturehlssettings.html>`__
    """

    props: PropsDictType = {}


class M3u8Settings(AWSProperty):
    """
    `M3u8Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-m3u8settings.html>`__
    """

    props: PropsDictType = {
        "AudioFramesPerPes": (integer, False),
        "AudioPids": (str, False),
        "EcmPid": (str, False),
        "KlvBehavior": (str, False),
        "KlvDataPids": (str, False),
        "NielsenId3Behavior": (str, False),
        "PatInterval": (integer, False),
        "PcrControl": (str, False),
        "PcrPeriod": (integer, False),
        "PcrPid": (str, False),
        "PmtInterval": (integer, False),
        "PmtPid": (str, False),
        "ProgramNum": (integer, False),
        "Scte35Behavior": (str, False),
        "Scte35Pid": (str, False),
        "TimedMetadataBehavior": (str, False),
        "TimedMetadataPid": (str, False),
        "TransportStreamId": (integer, False),
        "VideoPid": (str, False),
    }


class StandardHlsSettings(AWSProperty):
    """
    `StandardHlsSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-standardhlssettings.html>`__
    """

    props: PropsDictType = {
        "AudioRenditionSets": (str, False),
        "M3u8Settings": (M3u8Settings, False),
    }


class HlsSettings(AWSProperty):
    """
    `HlsSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-hlssettings.html>`__
    """

    props: PropsDictType = {
        "AudioOnlyHlsSettings": (AudioOnlyHlsSettings, False),
        "Fmp4HlsSettings": (Fmp4HlsSettings, False),
        "FrameCaptureHlsSettings": (FrameCaptureHlsSettings, False),
        "StandardHlsSettings": (StandardHlsSettings, False),
    }


class HlsOutputSettings(AWSProperty):
    """
    `HlsOutputSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-hlsoutputsettings.html>`__
    """

    props: PropsDictType = {
        "H265PackagingType": (str, False),
        "HlsSettings": (HlsSettings, False),
        "NameModifier": (str, False),
        "SegmentModifier": (str, False),
    }


class MediaPackageOutputSettings(AWSProperty):
    """
    `MediaPackageOutputSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-mediapackageoutputsettings.html>`__
    """

    props: PropsDictType = {}


class MsSmoothOutputSettings(AWSProperty):
    """
    `MsSmoothOutputSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-mssmoothoutputsettings.html>`__
    """

    props: PropsDictType = {
        "H265PackagingType": (str, False),
        "NameModifier": (str, False),
    }


class MultiplexM2tsSettings(AWSProperty):
    """
    `MultiplexM2tsSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-multiplexm2tssettings.html>`__
    """

    props: PropsDictType = {
        "AbsentInputAudioBehavior": (str, False),
        "Arib": (str, False),
        "AudioBufferModel": (str, False),
        "AudioFramesPerPes": (integer, False),
        "AudioStreamType": (str, False),
        "CcDescriptor": (str, False),
        "Ebif": (str, False),
        "EsRateInPes": (str, False),
        "Klv": (str, False),
        "NielsenId3Behavior": (str, False),
        "PcrControl": (str, False),
        "PcrPeriod": (integer, False),
        "Scte35Control": (str, False),
        "Scte35PrerollPullupMilliseconds": (double, False),
    }


class MultiplexContainerSettings(AWSProperty):
    """
    `MultiplexContainerSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-multiplexcontainersettings.html>`__
    """

    props: PropsDictType = {
        "MultiplexM2tsSettings": (MultiplexM2tsSettings, False),
    }


class OutputLocationRef(AWSProperty):
    """
    `OutputLocationRef <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-outputlocationref.html>`__
    """

    props: PropsDictType = {
        "DestinationRefId": (str, False),
    }


class MultiplexOutputSettings(AWSProperty):
    """
    `MultiplexOutputSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-multiplexoutputsettings.html>`__
    """

    props: PropsDictType = {
        "ContainerSettings": (MultiplexContainerSettings, False),
        "Destination": (OutputLocationRef, False),
    }


class RtmpOutputSettings(AWSProperty):
    """
    `RtmpOutputSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-rtmpoutputsettings.html>`__
    """

    props: PropsDictType = {
        "CertificateMode": (str, False),
        "ConnectionRetryInterval": (integer, False),
        "Destination": (OutputLocationRef, False),
        "NumRetries": (integer, False),
    }


class UdpContainerSettings(AWSProperty):
    """
    `UdpContainerSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-udpcontainersettings.html>`__
    """

    props: PropsDictType = {
        "M2tsSettings": (M2tsSettings, False),
    }


class SrtOutputSettings(AWSProperty):
    """
    `SrtOutputSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-srtoutputsettings.html>`__
    """

    props: PropsDictType = {
        "BufferMsec": (integer, False),
        "ContainerSettings": (UdpContainerSettings, False),
        "Destination": (OutputLocationRef, False),
        "EncryptionType": (str, False),
        "Latency": (integer, False),
    }


class FecOutputSettings(AWSProperty):
    """
    `FecOutputSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-fecoutputsettings.html>`__
    """

    props: PropsDictType = {
        "ColumnDepth": (integer, False),
        "IncludeFec": (str, False),
        "RowLength": (integer, False),
    }


class UdpOutputSettings(AWSProperty):
    """
    `UdpOutputSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-udpoutputsettings.html>`__
    """

    props: PropsDictType = {
        "BufferMsec": (integer, False),
        "ContainerSettings": (UdpContainerSettings, False),
        "Destination": (OutputLocationRef, False),
        "FecOutputSettings": (FecOutputSettings, False),
    }


class OutputSettings(AWSProperty):
    """
    `OutputSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-outputsettings.html>`__
    """

    props: PropsDictType = {
        "ArchiveOutputSettings": (ArchiveOutputSettings, False),
        "CmafIngestOutputSettings": (CmafIngestOutputSettings, False),
        "FrameCaptureOutputSettings": (FrameCaptureOutputSettings, False),
        "HlsOutputSettings": (HlsOutputSettings, False),
        "MediaPackageOutputSettings": (MediaPackageOutputSettings, False),
        "MsSmoothOutputSettings": (MsSmoothOutputSettings, False),
        "MultiplexOutputSettings": (MultiplexOutputSettings, False),
        "RtmpOutputSettings": (RtmpOutputSettings, False),
        "SrtOutputSettings": (SrtOutputSettings, False),
        "UdpOutputSettings": (UdpOutputSettings, False),
    }


class Output(AWSProperty):
    """
    `Output <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-output.html>`__
    """

    props: PropsDictType = {
        "AudioDescriptionNames": ([str], False),
        "CaptionDescriptionNames": ([str], False),
        "OutputName": (str, False),
        "OutputSettings": (OutputSettings, False),
        "VideoDescriptionName": (str, False),
    }


class ArchiveS3Settings(AWSProperty):
    """
    `ArchiveS3Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-archives3settings.html>`__
    """

    props: PropsDictType = {
        "CannedAcl": (str, False),
    }


class ArchiveCdnSettings(AWSProperty):
    """
    `ArchiveCdnSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-archivecdnsettings.html>`__
    """

    props: PropsDictType = {
        "ArchiveS3Settings": (ArchiveS3Settings, False),
    }


class ArchiveGroupSettings(AWSProperty):
    """
    `ArchiveGroupSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-archivegroupsettings.html>`__
    """

    props: PropsDictType = {
        "ArchiveCdnSettings": (ArchiveCdnSettings, False),
        "Destination": (OutputLocationRef, False),
        "RolloverInterval": (integer, False),
    }


class CmafIngestGroupSettings(AWSProperty):
    """
    `CmafIngestGroupSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-cmafingestgroupsettings.html>`__
    """

    props: PropsDictType = {
        "Destination": (OutputLocationRef, False),
        "Id3Behavior": (str, False),
        "Id3NameModifier": (str, False),
        "KlvBehavior": (str, False),
        "KlvNameModifier": (str, False),
        "NielsenId3Behavior": (str, False),
        "NielsenId3NameModifier": (str, False),
        "Scte35NameModifier": (str, False),
        "Scte35Type": (str, False),
        "SegmentLength": (integer, False),
        "SegmentLengthUnits": (str, False),
        "SendDelayMs": (integer, False),
    }


class FrameCaptureS3Settings(AWSProperty):
    """
    `FrameCaptureS3Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-framecaptures3settings.html>`__
    """

    props: PropsDictType = {
        "CannedAcl": (str, False),
    }


class FrameCaptureCdnSettings(AWSProperty):
    """
    `FrameCaptureCdnSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-framecapturecdnsettings.html>`__
    """

    props: PropsDictType = {
        "FrameCaptureS3Settings": (FrameCaptureS3Settings, False),
    }


class FrameCaptureGroupSettings(AWSProperty):
    """
    `FrameCaptureGroupSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-framecapturegroupsettings.html>`__
    """

    props: PropsDictType = {
        "Destination": (OutputLocationRef, False),
        "FrameCaptureCdnSettings": (FrameCaptureCdnSettings, False),
    }


class CaptionLanguageMapping(AWSProperty):
    """
    `CaptionLanguageMapping <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-captionlanguagemapping.html>`__
    """

    props: PropsDictType = {
        "CaptionChannel": (integer, False),
        "LanguageCode": (str, False),
        "LanguageDescription": (str, False),
    }


class HlsAkamaiSettings(AWSProperty):
    """
    `HlsAkamaiSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-hlsakamaisettings.html>`__
    """

    props: PropsDictType = {
        "ConnectionRetryInterval": (integer, False),
        "FilecacheDuration": (integer, False),
        "HttpTransferMode": (str, False),
        "NumRetries": (integer, False),
        "RestartDelay": (integer, False),
        "Salt": (str, False),
        "Token": (str, False),
    }


class HlsBasicPutSettings(AWSProperty):
    """
    `HlsBasicPutSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-hlsbasicputsettings.html>`__
    """

    props: PropsDictType = {
        "ConnectionRetryInterval": (integer, False),
        "FilecacheDuration": (integer, False),
        "NumRetries": (integer, False),
        "RestartDelay": (integer, False),
    }


class HlsMediaStoreSettings(AWSProperty):
    """
    `HlsMediaStoreSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-hlsmediastoresettings.html>`__
    """

    props: PropsDictType = {
        "ConnectionRetryInterval": (integer, False),
        "FilecacheDuration": (integer, False),
        "MediaStoreStorageClass": (str, False),
        "NumRetries": (integer, False),
        "RestartDelay": (integer, False),
    }


class HlsS3Settings(AWSProperty):
    """
    `HlsS3Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-hlss3settings.html>`__
    """

    props: PropsDictType = {
        "CannedAcl": (str, False),
    }


class HlsWebdavSettings(AWSProperty):
    """
    `HlsWebdavSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-hlswebdavsettings.html>`__
    """

    props: PropsDictType = {
        "ConnectionRetryInterval": (integer, False),
        "FilecacheDuration": (integer, False),
        "HttpTransferMode": (str, False),
        "NumRetries": (integer, False),
        "RestartDelay": (integer, False),
    }


class HlsCdnSettings(AWSProperty):
    """
    `HlsCdnSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-hlscdnsettings.html>`__
    """

    props: PropsDictType = {
        "HlsAkamaiSettings": (HlsAkamaiSettings, False),
        "HlsBasicPutSettings": (HlsBasicPutSettings, False),
        "HlsMediaStoreSettings": (HlsMediaStoreSettings, False),
        "HlsS3Settings": (HlsS3Settings, False),
        "HlsWebdavSettings": (HlsWebdavSettings, False),
    }


class StaticKeySettings(AWSProperty):
    """
    `StaticKeySettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-statickeysettings.html>`__
    """

    props: PropsDictType = {
        "KeyProviderServer": (InputLocation, False),
        "StaticKeyValue": (str, False),
    }


class KeyProviderSettings(AWSProperty):
    """
    `KeyProviderSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-keyprovidersettings.html>`__
    """

    props: PropsDictType = {
        "StaticKeySettings": (StaticKeySettings, False),
    }


class HlsGroupSettings(AWSProperty):
    """
    `HlsGroupSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-hlsgroupsettings.html>`__
    """

    props: PropsDictType = {
        "AdMarkers": ([str], False),
        "BaseUrlContent": (str, False),
        "BaseUrlContent1": (str, False),
        "BaseUrlManifest": (str, False),
        "BaseUrlManifest1": (str, False),
        "CaptionLanguageMappings": ([CaptionLanguageMapping], False),
        "CaptionLanguageSetting": (str, False),
        "ClientCache": (str, False),
        "CodecSpecification": (str, False),
        "ConstantIv": (str, False),
        "Destination": (OutputLocationRef, False),
        "DirectoryStructure": (str, False),
        "DiscontinuityTags": (str, False),
        "EncryptionType": (str, False),
        "HlsCdnSettings": (HlsCdnSettings, False),
        "HlsId3SegmentTagging": (str, False),
        "IFrameOnlyPlaylists": (str, False),
        "IncompleteSegmentBehavior": (str, False),
        "IndexNSegments": (integer, False),
        "InputLossAction": (str, False),
        "IvInManifest": (str, False),
        "IvSource": (str, False),
        "KeepSegments": (integer, False),
        "KeyFormat": (str, False),
        "KeyFormatVersions": (str, False),
        "KeyProviderSettings": (KeyProviderSettings, False),
        "ManifestCompression": (str, False),
        "ManifestDurationFormat": (str, False),
        "MinSegmentLength": (integer, False),
        "Mode": (str, False),
        "OutputSelection": (str, False),
        "ProgramDateTime": (str, False),
        "ProgramDateTimeClock": (str, False),
        "ProgramDateTimePeriod": (integer, False),
        "RedundantManifest": (str, False),
        "SegmentLength": (integer, False),
        "SegmentationMode": (str, False),
        "SegmentsPerSubdirectory": (integer, False),
        "StreamInfResolution": (str, False),
        "TimedMetadataId3Frame": (str, False),
        "TimedMetadataId3Period": (integer, False),
        "TimestampDeltaMilliseconds": (integer, False),
        "TsFileMode": (str, False),
    }


class MediaPackageGroupSettings(AWSProperty):
    """
    `MediaPackageGroupSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-mediapackagegroupsettings.html>`__
    """

    props: PropsDictType = {
        "Destination": (OutputLocationRef, False),
    }


class MsSmoothGroupSettings(AWSProperty):
    """
    `MsSmoothGroupSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-mssmoothgroupsettings.html>`__
    """

    props: PropsDictType = {
        "AcquisitionPointId": (str, False),
        "AudioOnlyTimecodeControl": (str, False),
        "CertificateMode": (str, False),
        "ConnectionRetryInterval": (integer, False),
        "Destination": (OutputLocationRef, False),
        "EventId": (str, False),
        "EventIdMode": (str, False),
        "EventStopBehavior": (str, False),
        "FilecacheDuration": (integer, False),
        "FragmentLength": (integer, False),
        "InputLossAction": (str, False),
        "NumRetries": (integer, False),
        "RestartDelay": (integer, False),
        "SegmentationMode": (str, False),
        "SendDelayMs": (integer, False),
        "SparseTrackType": (str, False),
        "StreamManifestBehavior": (str, False),
        "TimestampOffset": (str, False),
        "TimestampOffsetMode": (str, False),
    }


class MultiplexGroupSettings(AWSProperty):
    """
    `MultiplexGroupSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-multiplexgroupsettings.html>`__
    """

    props: PropsDictType = {}


class RtmpGroupSettings(AWSProperty):
    """
    `RtmpGroupSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-rtmpgroupsettings.html>`__
    """

    props: PropsDictType = {
        "AdMarkers": ([str], False),
        "AuthenticationScheme": (str, False),
        "CacheFullBehavior": (str, False),
        "CacheLength": (integer, False),
        "CaptionData": (str, False),
        "IncludeFillerNalUnits": (str, False),
        "InputLossAction": (str, False),
        "RestartDelay": (integer, False),
    }


class SrtGroupSettings(AWSProperty):
    """
    `SrtGroupSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-srtgroupsettings.html>`__
    """

    props: PropsDictType = {
        "InputLossAction": (str, False),
    }


class UdpGroupSettings(AWSProperty):
    """
    `UdpGroupSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-udpgroupsettings.html>`__
    """

    props: PropsDictType = {
        "InputLossAction": (str, False),
        "TimedMetadataId3Frame": (str, False),
        "TimedMetadataId3Period": (integer, False),
    }


class OutputGroupSettings(AWSProperty):
    """
    `OutputGroupSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-outputgroupsettings.html>`__
    """

    props: PropsDictType = {
        "ArchiveGroupSettings": (ArchiveGroupSettings, False),
        "CmafIngestGroupSettings": (CmafIngestGroupSettings, False),
        "FrameCaptureGroupSettings": (FrameCaptureGroupSettings, False),
        "HlsGroupSettings": (HlsGroupSettings, False),
        "MediaPackageGroupSettings": (MediaPackageGroupSettings, False),
        "MsSmoothGroupSettings": (MsSmoothGroupSettings, False),
        "MultiplexGroupSettings": (MultiplexGroupSettings, False),
        "RtmpGroupSettings": (RtmpGroupSettings, False),
        "SrtGroupSettings": (SrtGroupSettings, False),
        "UdpGroupSettings": (UdpGroupSettings, False),
    }


class OutputGroup(AWSProperty):
    """
    `OutputGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-outputgroup.html>`__
    """

    props: PropsDictType = {
        "Name": (str, False),
        "OutputGroupSettings": (OutputGroupSettings, False),
        "Outputs": ([Output], False),
    }


class ThumbnailConfiguration(AWSProperty):
    """
    `ThumbnailConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-thumbnailconfiguration.html>`__
    """

    props: PropsDictType = {
        "State": (str, False),
    }


class TimecodeConfig(AWSProperty):
    """
    `TimecodeConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-timecodeconfig.html>`__
    """

    props: PropsDictType = {
        "Source": (str, False),
        "SyncThreshold": (integer, False),
    }


class ColorSpacePassthroughSettings(AWSProperty):
    """
    `ColorSpacePassthroughSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-colorspacepassthroughsettings.html>`__
    """

    props: PropsDictType = {}


class Hdr10Settings(AWSProperty):
    """
    `Hdr10Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-hdr10settings.html>`__
    """

    props: PropsDictType = {
        "MaxCll": (integer, False),
        "MaxFall": (integer, False),
    }


class Rec601Settings(AWSProperty):
    """
    `Rec601Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-rec601settings.html>`__
    """

    props: PropsDictType = {}


class Rec709Settings(AWSProperty):
    """
    `Rec709Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-rec709settings.html>`__
    """

    props: PropsDictType = {}


class Av1ColorSpaceSettings(AWSProperty):
    """
    `Av1ColorSpaceSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-av1colorspacesettings.html>`__
    """

    props: PropsDictType = {
        "ColorSpacePassthroughSettings": (ColorSpacePassthroughSettings, False),
        "Hdr10Settings": (Hdr10Settings, False),
        "Rec601Settings": (Rec601Settings, False),
        "Rec709Settings": (Rec709Settings, False),
    }


class TimecodeBurninSettings(AWSProperty):
    """
    `TimecodeBurninSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-timecodeburninsettings.html>`__
    """

    props: PropsDictType = {
        "FontSize": (str, False),
        "Position": (str, False),
        "Prefix": (str, False),
    }


class Av1Settings(AWSProperty):
    """
    `Av1Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-av1settings.html>`__
    """

    props: PropsDictType = {
        "AfdSignaling": (str, False),
        "BufSize": (integer, False),
        "ColorSpaceSettings": (Av1ColorSpaceSettings, False),
        "FixedAfd": (str, False),
        "FramerateDenominator": (integer, False),
        "FramerateNumerator": (integer, False),
        "GopSize": (double, False),
        "GopSizeUnits": (str, False),
        "Level": (str, False),
        "LookAheadRateControl": (str, False),
        "MaxBitrate": (integer, False),
        "MinIInterval": (integer, False),
        "ParDenominator": (integer, False),
        "ParNumerator": (integer, False),
        "QvbrQualityLevel": (integer, False),
        "SceneChangeDetect": (str, False),
        "TimecodeBurninSettings": (TimecodeBurninSettings, False),
    }


class FrameCaptureSettings(AWSProperty):
    """
    `FrameCaptureSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-framecapturesettings.html>`__
    """

    props: PropsDictType = {
        "CaptureInterval": (integer, False),
        "CaptureIntervalUnits": (str, False),
        "TimecodeBurninSettings": (TimecodeBurninSettings, False),
    }


class H264ColorSpaceSettings(AWSProperty):
    """
    `H264ColorSpaceSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-h264colorspacesettings.html>`__
    """

    props: PropsDictType = {
        "ColorSpacePassthroughSettings": (ColorSpacePassthroughSettings, False),
        "Rec601Settings": (Rec601Settings, False),
        "Rec709Settings": (Rec709Settings, False),
    }


class BandwidthReductionFilterSettings(AWSProperty):
    """
    `BandwidthReductionFilterSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-bandwidthreductionfiltersettings.html>`__
    """

    props: PropsDictType = {
        "PostFilterSharpening": (str, False),
        "Strength": (str, False),
    }


class TemporalFilterSettings(AWSProperty):
    """
    `TemporalFilterSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-temporalfiltersettings.html>`__
    """

    props: PropsDictType = {
        "PostFilterSharpening": (str, False),
        "Strength": (str, False),
    }


class H264FilterSettings(AWSProperty):
    """
    `H264FilterSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-h264filtersettings.html>`__
    """

    props: PropsDictType = {
        "BandwidthReductionFilterSettings": (BandwidthReductionFilterSettings, False),
        "TemporalFilterSettings": (TemporalFilterSettings, False),
    }


class H264Settings(AWSProperty):
    """
    `H264Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-h264settings.html>`__
    """

    props: PropsDictType = {
        "AdaptiveQuantization": (str, False),
        "AfdSignaling": (str, False),
        "Bitrate": (integer, False),
        "BufFillPct": (integer, False),
        "BufSize": (integer, False),
        "ColorMetadata": (str, False),
        "ColorSpaceSettings": (H264ColorSpaceSettings, False),
        "EntropyEncoding": (str, False),
        "FilterSettings": (H264FilterSettings, False),
        "FixedAfd": (str, False),
        "FlickerAq": (str, False),
        "ForceFieldPictures": (str, False),
        "FramerateControl": (str, False),
        "FramerateDenominator": (integer, False),
        "FramerateNumerator": (integer, False),
        "GopBReference": (str, False),
        "GopClosedCadence": (integer, False),
        "GopNumBFrames": (integer, False),
        "GopSize": (double, False),
        "GopSizeUnits": (str, False),
        "Level": (str, False),
        "LookAheadRateControl": (str, False),
        "MaxBitrate": (integer, False),
        "MinIInterval": (integer, False),
        "MinQp": (integer, False),
        "NumRefFrames": (integer, False),
        "ParControl": (str, False),
        "ParDenominator": (integer, False),
        "ParNumerator": (integer, False),
        "Profile": (str, False),
        "QualityLevel": (str, False),
        "QvbrQualityLevel": (integer, False),
        "RateControlMode": (str, False),
        "ScanType": (str, False),
        "SceneChangeDetect": (str, False),
        "Slices": (integer, False),
        "Softness": (integer, False),
        "SpatialAq": (str, False),
        "SubgopLength": (str, False),
        "Syntax": (str, False),
        "TemporalAq": (str, False),
        "TimecodeBurninSettings": (TimecodeBurninSettings, False),
        "TimecodeInsertion": (str, False),
    }


class DolbyVision81Settings(AWSProperty):
    """
    `DolbyVision81Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-dolbyvision81settings.html>`__
    """

    props: PropsDictType = {}


class H265ColorSpaceSettings(AWSProperty):
    """
    `H265ColorSpaceSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-h265colorspacesettings.html>`__
    """

    props: PropsDictType = {
        "ColorSpacePassthroughSettings": (ColorSpacePassthroughSettings, False),
        "DolbyVision81Settings": (DolbyVision81Settings, False),
        "Hdr10Settings": (Hdr10Settings, False),
        "Rec601Settings": (Rec601Settings, False),
        "Rec709Settings": (Rec709Settings, False),
    }


class H265FilterSettings(AWSProperty):
    """
    `H265FilterSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-h265filtersettings.html>`__
    """

    props: PropsDictType = {
        "BandwidthReductionFilterSettings": (BandwidthReductionFilterSettings, False),
        "TemporalFilterSettings": (TemporalFilterSettings, False),
    }


class H265Settings(AWSProperty):
    """
    `H265Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-h265settings.html>`__
    """

    props: PropsDictType = {
        "AdaptiveQuantization": (str, False),
        "AfdSignaling": (str, False),
        "AlternativeTransferFunction": (str, False),
        "Bitrate": (integer, False),
        "BufSize": (integer, False),
        "ColorMetadata": (str, False),
        "ColorSpaceSettings": (H265ColorSpaceSettings, False),
        "Deblocking": (str, False),
        "FilterSettings": (H265FilterSettings, False),
        "FixedAfd": (str, False),
        "FlickerAq": (str, False),
        "FramerateDenominator": (integer, False),
        "FramerateNumerator": (integer, False),
        "GopClosedCadence": (integer, False),
        "GopSize": (double, False),
        "GopSizeUnits": (str, False),
        "Level": (str, False),
        "LookAheadRateControl": (str, False),
        "MaxBitrate": (integer, False),
        "MinIInterval": (integer, False),
        "MinQp": (integer, False),
        "MvOverPictureBoundaries": (str, False),
        "MvTemporalPredictor": (str, False),
        "ParDenominator": (integer, False),
        "ParNumerator": (integer, False),
        "Profile": (str, False),
        "QvbrQualityLevel": (integer, False),
        "RateControlMode": (str, False),
        "ScanType": (str, False),
        "SceneChangeDetect": (str, False),
        "Slices": (integer, False),
        "Tier": (str, False),
        "TileHeight": (integer, False),
        "TilePadding": (str, False),
        "TileWidth": (integer, False),
        "TimecodeBurninSettings": (TimecodeBurninSettings, False),
        "TimecodeInsertion": (str, False),
        "TreeblockSize": (str, False),
    }


class Mpeg2FilterSettings(AWSProperty):
    """
    `Mpeg2FilterSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-mpeg2filtersettings.html>`__
    """

    props: PropsDictType = {
        "TemporalFilterSettings": (TemporalFilterSettings, False),
    }


class Mpeg2Settings(AWSProperty):
    """
    `Mpeg2Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-mpeg2settings.html>`__
    """

    props: PropsDictType = {
        "AdaptiveQuantization": (str, False),
        "AfdSignaling": (str, False),
        "ColorMetadata": (str, False),
        "ColorSpace": (str, False),
        "DisplayAspectRatio": (str, False),
        "FilterSettings": (Mpeg2FilterSettings, False),
        "FixedAfd": (str, False),
        "FramerateDenominator": (integer, False),
        "FramerateNumerator": (integer, False),
        "GopClosedCadence": (integer, False),
        "GopNumBFrames": (integer, False),
        "GopSize": (double, False),
        "GopSizeUnits": (str, False),
        "ScanType": (str, False),
        "SubgopLength": (str, False),
        "TimecodeBurninSettings": (TimecodeBurninSettings, False),
        "TimecodeInsertion": (str, False),
    }


class VideoCodecSettings(AWSProperty):
    """
    `VideoCodecSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-videocodecsettings.html>`__
    """

    props: PropsDictType = {
        "Av1Settings": (Av1Settings, False),
        "FrameCaptureSettings": (FrameCaptureSettings, False),
        "H264Settings": (H264Settings, False),
        "H265Settings": (H265Settings, False),
        "Mpeg2Settings": (Mpeg2Settings, False),
    }


class VideoDescription(AWSProperty):
    """
    `VideoDescription <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-videodescription.html>`__
    """

    props: PropsDictType = {
        "CodecSettings": (VideoCodecSettings, False),
        "Height": (integer, False),
        "Name": (str, False),
        "RespondToAfd": (str, False),
        "ScalingBehavior": (str, False),
        "Sharpness": (integer, False),
        "Width": (integer, False),
    }


class EncoderSettings(AWSProperty):
    """
    `EncoderSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-encodersettings.html>`__
    """

    props: PropsDictType = {
        "AudioDescriptions": ([AudioDescription], False),
        "AvailBlanking": (AvailBlanking, False),
        "AvailConfiguration": (AvailConfiguration, False),
        "BlackoutSlate": (BlackoutSlate, False),
        "CaptionDescriptions": ([CaptionDescription], False),
        "ColorCorrectionSettings": (ColorCorrectionSettings, False),
        "FeatureActivations": (FeatureActivations, False),
        "GlobalConfiguration": (GlobalConfiguration, False),
        "MotionGraphicsConfiguration": (MotionGraphicsConfiguration, False),
        "NielsenConfiguration": (NielsenConfiguration, False),
        "OutputGroups": ([OutputGroup], False),
        "ThumbnailConfiguration": (ThumbnailConfiguration, False),
        "TimecodeConfig": (TimecodeConfig, False),
        "VideoDescriptions": ([VideoDescription], False),
    }


class AudioSilenceFailoverSettings(AWSProperty):
    """
    `AudioSilenceFailoverSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-audiosilencefailoversettings.html>`__
    """

    props: PropsDictType = {
        "AudioSelectorName": (str, False),
        "AudioSilenceThresholdMsec": (integer, False),
    }


class InputLossFailoverSettings(AWSProperty):
    """
    `InputLossFailoverSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-inputlossfailoversettings.html>`__
    """

    props: PropsDictType = {
        "InputLossThresholdMsec": (integer, False),
    }


class VideoBlackFailoverSettings(AWSProperty):
    """
    `VideoBlackFailoverSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-videoblackfailoversettings.html>`__
    """

    props: PropsDictType = {
        "BlackDetectThreshold": (double, False),
        "VideoBlackThresholdMsec": (integer, False),
    }


class FailoverConditionSettings(AWSProperty):
    """
    `FailoverConditionSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-failoverconditionsettings.html>`__
    """

    props: PropsDictType = {
        "AudioSilenceSettings": (AudioSilenceFailoverSettings, False),
        "InputLossSettings": (InputLossFailoverSettings, False),
        "VideoBlackSettings": (VideoBlackFailoverSettings, False),
    }


class FailoverCondition(AWSProperty):
    """
    `FailoverCondition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-failovercondition.html>`__
    """

    props: PropsDictType = {
        "FailoverConditionSettings": (FailoverConditionSettings, False),
    }


class AutomaticInputFailoverSettings(AWSProperty):
    """
    `AutomaticInputFailoverSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-automaticinputfailoversettings.html>`__
    """

    props: PropsDictType = {
        "ErrorClearTimeMsec": (integer, False),
        "FailoverConditions": ([FailoverCondition], False),
        "InputPreference": (str, False),
        "SecondaryInputId": (str, False),
    }


class AudioHlsRenditionSelection(AWSProperty):
    """
    `AudioHlsRenditionSelection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-audiohlsrenditionselection.html>`__
    """

    props: PropsDictType = {
        "GroupId": (str, False),
        "Name": (str, False),
    }


class AudioLanguageSelection(AWSProperty):
    """
    `AudioLanguageSelection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-audiolanguageselection.html>`__
    """

    props: PropsDictType = {
        "LanguageCode": (str, False),
        "LanguageSelectionPolicy": (str, False),
    }


class AudioPidSelection(AWSProperty):
    """
    `AudioPidSelection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-audiopidselection.html>`__
    """

    props: PropsDictType = {
        "Pid": (integer, False),
    }


class AudioDolbyEDecode(AWSProperty):
    """
    `AudioDolbyEDecode <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-audiodolbyedecode.html>`__
    """

    props: PropsDictType = {
        "ProgramSelection": (str, False),
    }


class AudioTrack(AWSProperty):
    """
    `AudioTrack <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-audiotrack.html>`__
    """

    props: PropsDictType = {
        "Track": (integer, False),
    }


class AudioTrackSelection(AWSProperty):
    """
    `AudioTrackSelection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-audiotrackselection.html>`__
    """

    props: PropsDictType = {
        "DolbyEDecode": (AudioDolbyEDecode, False),
        "Tracks": ([AudioTrack], False),
    }


class AudioSelectorSettings(AWSProperty):
    """
    `AudioSelectorSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-audioselectorsettings.html>`__
    """

    props: PropsDictType = {
        "AudioHlsRenditionSelection": (AudioHlsRenditionSelection, False),
        "AudioLanguageSelection": (AudioLanguageSelection, False),
        "AudioPidSelection": (AudioPidSelection, False),
        "AudioTrackSelection": (AudioTrackSelection, False),
    }


class AudioSelector(AWSProperty):
    """
    `AudioSelector <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-audioselector.html>`__
    """

    props: PropsDictType = {
        "Name": (str, False),
        "SelectorSettings": (AudioSelectorSettings, False),
    }


class AncillarySourceSettings(AWSProperty):
    """
    `AncillarySourceSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-ancillarysourcesettings.html>`__
    """

    props: PropsDictType = {
        "SourceAncillaryChannelNumber": (integer, False),
    }


class AribSourceSettings(AWSProperty):
    """
    `AribSourceSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-aribsourcesettings.html>`__
    """

    props: PropsDictType = {}


class DvbSubSourceSettings(AWSProperty):
    """
    `DvbSubSourceSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-dvbsubsourcesettings.html>`__
    """

    props: PropsDictType = {
        "OcrLanguage": (str, False),
        "Pid": (integer, False),
    }


class EmbeddedSourceSettings(AWSProperty):
    """
    `EmbeddedSourceSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-embeddedsourcesettings.html>`__
    """

    props: PropsDictType = {
        "Convert608To708": (str, False),
        "Scte20Detection": (str, False),
        "Source608ChannelNumber": (integer, False),
        "Source608TrackNumber": (integer, False),
    }


class Scte20SourceSettings(AWSProperty):
    """
    `Scte20SourceSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-scte20sourcesettings.html>`__
    """

    props: PropsDictType = {
        "Convert608To708": (str, False),
        "Source608ChannelNumber": (integer, False),
    }


class Scte27SourceSettings(AWSProperty):
    """
    `Scte27SourceSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-scte27sourcesettings.html>`__
    """

    props: PropsDictType = {
        "OcrLanguage": (str, False),
        "Pid": (integer, False),
    }


class CaptionRectangle(AWSProperty):
    """
    `CaptionRectangle <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-captionrectangle.html>`__
    """

    props: PropsDictType = {
        "Height": (double, False),
        "LeftOffset": (double, False),
        "TopOffset": (double, False),
        "Width": (double, False),
    }


class TeletextSourceSettings(AWSProperty):
    """
    `TeletextSourceSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-teletextsourcesettings.html>`__
    """

    props: PropsDictType = {
        "OutputRectangle": (CaptionRectangle, False),
        "PageNumber": (str, False),
    }


class CaptionSelectorSettings(AWSProperty):
    """
    `CaptionSelectorSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-captionselectorsettings.html>`__
    """

    props: PropsDictType = {
        "AncillarySourceSettings": (AncillarySourceSettings, False),
        "AribSourceSettings": (AribSourceSettings, False),
        "DvbSubSourceSettings": (DvbSubSourceSettings, False),
        "EmbeddedSourceSettings": (EmbeddedSourceSettings, False),
        "Scte20SourceSettings": (Scte20SourceSettings, False),
        "Scte27SourceSettings": (Scte27SourceSettings, False),
        "TeletextSourceSettings": (TeletextSourceSettings, False),
    }


class CaptionSelector(AWSProperty):
    """
    `CaptionSelector <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-captionselector.html>`__
    """

    props: PropsDictType = {
        "LanguageCode": (str, False),
        "Name": (str, False),
        "SelectorSettings": (CaptionSelectorSettings, False),
    }


class HlsInputSettings(AWSProperty):
    """
    `HlsInputSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-hlsinputsettings.html>`__
    """

    props: PropsDictType = {
        "Bandwidth": (integer, False),
        "BufferSegments": (integer, False),
        "Retries": (integer, False),
        "RetryInterval": (integer, False),
        "Scte35Source": (str, False),
    }


class MulticastInputSettings(AWSProperty):
    """
    `MulticastInputSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-multicastinputsettings.html>`__
    """

    props: PropsDictType = {
        "SourceIpAddress": (str, False),
    }


class NetworkInputSettings(AWSProperty):
    """
    `NetworkInputSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-networkinputsettings.html>`__
    """

    props: PropsDictType = {
        "HlsInputSettings": (HlsInputSettings, False),
        "MulticastInputSettings": (MulticastInputSettings, False),
        "ServerValidation": (str, False),
    }


class VideoSelectorColorSpaceSettings(AWSProperty):
    """
    `VideoSelectorColorSpaceSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-videoselectorcolorspacesettings.html>`__
    """

    props: PropsDictType = {
        "Hdr10Settings": (Hdr10Settings, False),
    }


class VideoSelectorPid(AWSProperty):
    """
    `VideoSelectorPid <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-videoselectorpid.html>`__
    """

    props: PropsDictType = {
        "Pid": (integer, False),
    }


class VideoSelectorProgramId(AWSProperty):
    """
    `VideoSelectorProgramId <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-videoselectorprogramid.html>`__
    """

    props: PropsDictType = {
        "ProgramId": (integer, False),
    }


class VideoSelectorSettings(AWSProperty):
    """
    `VideoSelectorSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-videoselectorsettings.html>`__
    """

    props: PropsDictType = {
        "VideoSelectorPid": (VideoSelectorPid, False),
        "VideoSelectorProgramId": (VideoSelectorProgramId, False),
    }


class VideoSelector(AWSProperty):
    """
    `VideoSelector <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-videoselector.html>`__
    """

    props: PropsDictType = {
        "ColorSpace": (str, False),
        "ColorSpaceSettings": (VideoSelectorColorSpaceSettings, False),
        "ColorSpaceUsage": (str, False),
        "SelectorSettings": (VideoSelectorSettings, False),
    }


class InputSettings(AWSProperty):
    """
    `InputSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-inputsettings.html>`__
    """

    props: PropsDictType = {
        "AudioSelectors": ([AudioSelector], False),
        "CaptionSelectors": ([CaptionSelector], False),
        "DeblockFilter": (str, False),
        "DenoiseFilter": (str, False),
        "FilterStrength": (integer, False),
        "InputFilter": (str, False),
        "NetworkInputSettings": (NetworkInputSettings, False),
        "Scte35Pid": (integer, False),
        "Smpte2038DataPreference": (str, False),
        "SourceEndBehavior": (str, False),
        "VideoSelector": (VideoSelector, False),
    }


class InputAttachment(AWSProperty):
    """
    `InputAttachment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-inputattachment.html>`__
    """

    props: PropsDictType = {
        "AutomaticInputFailoverSettings": (AutomaticInputFailoverSettings, False),
        "InputAttachmentName": (str, False),
        "InputId": (str, False),
        "InputSettings": (InputSettings, False),
        "LogicalInterfaceNames": ([str], False),
    }


class InputSpecification(AWSProperty):
    """
    `InputSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-inputspecification.html>`__
    """

    props: PropsDictType = {
        "Codec": (str, False),
        "MaximumBitrate": (str, False),
        "Resolution": (str, False),
    }


class MaintenanceCreateSettings(AWSProperty):
    """
    `MaintenanceCreateSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-maintenancecreatesettings.html>`__
    """

    props: PropsDictType = {
        "MaintenanceDay": (str, False),
        "MaintenanceStartTime": (str, False),
    }


class MediaPackageOutputDestinationSettings(AWSProperty):
    """
    `MediaPackageOutputDestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-mediapackageoutputdestinationsettings.html>`__
    """

    props: PropsDictType = {
        "ChannelGroup": (str, False),
        "ChannelId": (str, False),
        "ChannelName": (str, False),
    }


class MultiplexProgramChannelDestinationSettings(AWSProperty):
    """
    `MultiplexProgramChannelDestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-multiplexprogramchanneldestinationsettings.html>`__
    """

    props: PropsDictType = {
        "MultiplexId": (str, False),
        "ProgramName": (str, False),
    }


class OutputDestinationSettings(AWSProperty):
    """
    `OutputDestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-outputdestinationsettings.html>`__
    """

    props: PropsDictType = {
        "PasswordParam": (str, False),
        "StreamName": (str, False),
        "Url": (str, False),
        "Username": (str, False),
    }


class SrtOutputDestinationSettings(AWSProperty):
    """
    `SrtOutputDestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-srtoutputdestinationsettings.html>`__
    """

    props: PropsDictType = {
        "EncryptionPassphraseSecretArn": (str, False),
        "StreamId": (str, False),
        "Url": (str, False),
    }


class OutputDestination(AWSProperty):
    """
    `OutputDestination <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-outputdestination.html>`__
    """

    props: PropsDictType = {
        "Id": (str, False),
        "MediaPackageSettings": ([MediaPackageOutputDestinationSettings], False),
        "MultiplexSettings": (MultiplexProgramChannelDestinationSettings, False),
        "Settings": ([OutputDestinationSettings], False),
        "SrtSettings": ([SrtOutputDestinationSettings], False),
    }


class VpcOutputSettings(AWSProperty):
    """
    `VpcOutputSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-vpcoutputsettings.html>`__
    """

    props: PropsDictType = {
        "PublicAddressAllocationIds": ([str], False),
        "SecurityGroupIds": ([str], False),
        "SubnetIds": ([str], False),
    }


class Channel(AWSObject):
    """
    `Channel <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-medialive-channel.html>`__
    """

    resource_type = "AWS::MediaLive::Channel"

    props: PropsDictType = {
        "AnywhereSettings": (AnywhereSettings, False),
        "CdiInputSpecification": (CdiInputSpecification, False),
        "ChannelClass": (str, False),
        "ChannelEngineVersion": (ChannelEngineVersionRequest, False),
        "Destinations": ([OutputDestination], False),
        "DryRun": (boolean, False),
        "EncoderSettings": (EncoderSettings, False),
        "InputAttachments": ([InputAttachment], False),
        "InputSpecification": (InputSpecification, False),
        "LogLevel": (str, False),
        "Maintenance": (MaintenanceCreateSettings, False),
        "Name": (str, False),
        "RoleArn": (str, False),
        "Tags": (dict, False),
        "Vpc": (VpcOutputSettings, False),
    }


class ChannelPlacementGroup(AWSObject):
    """
    `ChannelPlacementGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-medialive-channelplacementgroup.html>`__
    """

    resource_type = "AWS::MediaLive::ChannelPlacementGroup"

    props: PropsDictType = {
        "ClusterId": (str, False),
        "Name": (str, False),
        "Nodes": ([str], False),
        "Tags": (Tags, False),
    }


class CloudWatchAlarmTemplate(AWSObject):
    """
    `CloudWatchAlarmTemplate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-medialive-cloudwatchalarmtemplate.html>`__
    """

    resource_type = "AWS::MediaLive::CloudWatchAlarmTemplate"

    props: PropsDictType = {
        "ComparisonOperator": (str, True),
        "DatapointsToAlarm": (double, False),
        "Description": (str, False),
        "EvaluationPeriods": (double, True),
        "GroupIdentifier": (str, False),
        "MetricName": (str, True),
        "Name": (str, True),
        "Period": (double, True),
        "Statistic": (str, True),
        "Tags": (dict, False),
        "TargetResourceType": (str, True),
        "Threshold": (double, True),
        "TreatMissingData": (str, True),
    }


class CloudWatchAlarmTemplateGroup(AWSObject):
    """
    `CloudWatchAlarmTemplateGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-medialive-cloudwatchalarmtemplategroup.html>`__
    """

    resource_type = "AWS::MediaLive::CloudWatchAlarmTemplateGroup"

    props: PropsDictType = {
        "Description": (str, False),
        "Name": (str, True),
        "Tags": (dict, False),
    }


class InterfaceMapping(AWSProperty):
    """
    `InterfaceMapping <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-cluster-interfacemapping.html>`__
    """

    props: PropsDictType = {
        "LogicalInterfaceName": (str, False),
        "NetworkId": (str, False),
    }


class ClusterNetworkSettings(AWSProperty):
    """
    `ClusterNetworkSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-cluster-clusternetworksettings.html>`__
    """

    props: PropsDictType = {
        "DefaultRoute": (str, False),
        "InterfaceMappings": ([InterfaceMapping], False),
    }


class Cluster(AWSObject):
    """
    `Cluster <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-medialive-cluster.html>`__
    """

    resource_type = "AWS::MediaLive::Cluster"

    props: PropsDictType = {
        "ClusterType": (str, False),
        "InstanceRoleArn": (str, False),
        "Name": (str, False),
        "NetworkSettings": (ClusterNetworkSettings, False),
        "Tags": (Tags, False),
    }


class EventBridgeRuleTemplateTarget(AWSProperty):
    """
    `EventBridgeRuleTemplateTarget <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-eventbridgeruletemplate-eventbridgeruletemplatetarget.html>`__
    """

    props: PropsDictType = {
        "Arn": (str, True),
    }


class EventBridgeRuleTemplate(AWSObject):
    """
    `EventBridgeRuleTemplate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-medialive-eventbridgeruletemplate.html>`__
    """

    resource_type = "AWS::MediaLive::EventBridgeRuleTemplate"

    props: PropsDictType = {
        "Description": (str, False),
        "EventTargets": ([EventBridgeRuleTemplateTarget], False),
        "EventType": (str, True),
        "GroupIdentifier": (str, False),
        "Name": (str, True),
        "Tags": (dict, False),
    }


class EventBridgeRuleTemplateGroup(AWSObject):
    """
    `EventBridgeRuleTemplateGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-medialive-eventbridgeruletemplategroup.html>`__
    """

    resource_type = "AWS::MediaLive::EventBridgeRuleTemplateGroup"

    props: PropsDictType = {
        "Description": (str, False),
        "Name": (str, True),
        "Tags": (dict, False),
    }


class InputRequestDestinationRoute(AWSProperty):
    """
    `InputRequestDestinationRoute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-input-inputrequestdestinationroute.html>`__
    """

    props: PropsDictType = {
        "Cidr": (str, False),
        "Gateway": (str, False),
    }


class InputDestinationRequest(AWSProperty):
    """
    `InputDestinationRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-input-inputdestinationrequest.html>`__
    """

    props: PropsDictType = {
        "Network": (str, False),
        "NetworkRoutes": ([InputRequestDestinationRoute], False),
        "StaticIpAddress": (str, False),
        "StreamName": (str, False),
    }


class InputDeviceSettings(AWSProperty):
    """
    `InputDeviceSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-input-inputdevicesettings.html>`__
    """

    props: PropsDictType = {
        "Id": (str, False),
    }


class InputSourceRequest(AWSProperty):
    """
    `InputSourceRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-input-inputsourcerequest.html>`__
    """

    props: PropsDictType = {
        "PasswordParam": (str, False),
        "Url": (str, False),
        "Username": (str, False),
    }


class InputVpcRequest(AWSProperty):
    """
    `InputVpcRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-input-inputvpcrequest.html>`__
    """

    props: PropsDictType = {
        "SecurityGroupIds": ([str], False),
        "SubnetIds": ([str], False),
    }


class MediaConnectFlowRequest(AWSProperty):
    """
    `MediaConnectFlowRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-input-mediaconnectflowrequest.html>`__
    """

    props: PropsDictType = {
        "FlowArn": (str, False),
    }


class MulticastSourceCreateRequest(AWSProperty):
    """
    `MulticastSourceCreateRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-input-multicastsourcecreaterequest.html>`__
    """

    props: PropsDictType = {
        "SourceIp": (str, False),
        "Url": (str, False),
    }


class MulticastSettingsCreateRequest(AWSProperty):
    """
    `MulticastSettingsCreateRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-input-multicastsettingscreaterequest.html>`__
    """

    props: PropsDictType = {
        "Sources": ([MulticastSourceCreateRequest], False),
    }


class SrtCallerDecryptionRequest(AWSProperty):
    """
    `SrtCallerDecryptionRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-input-srtcallerdecryptionrequest.html>`__
    """

    props: PropsDictType = {
        "Algorithm": (str, False),
        "PassphraseSecretArn": (str, False),
    }


class SrtCallerSourceRequest(AWSProperty):
    """
    `SrtCallerSourceRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-input-srtcallersourcerequest.html>`__
    """

    props: PropsDictType = {
        "Decryption": (SrtCallerDecryptionRequest, False),
        "MinimumLatency": (integer, False),
        "SrtListenerAddress": (str, False),
        "SrtListenerPort": (str, False),
        "StreamId": (str, False),
    }


class SrtSettingsRequest(AWSProperty):
    """
    `SrtSettingsRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-input-srtsettingsrequest.html>`__
    """

    props: PropsDictType = {
        "SrtCallerSources": ([SrtCallerSourceRequest], False),
    }


class Input(AWSObject):
    """
    `Input <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-medialive-input.html>`__
    """

    resource_type = "AWS::MediaLive::Input"

    props: PropsDictType = {
        "Destinations": ([InputDestinationRequest], False),
        "InputDevices": ([InputDeviceSettings], False),
        "InputNetworkLocation": (str, False),
        "InputSecurityGroups": ([str], False),
        "MediaConnectFlows": ([MediaConnectFlowRequest], False),
        "MulticastSettings": (MulticastSettingsCreateRequest, False),
        "Name": (str, False),
        "RoleArn": (str, False),
        "Sources": ([InputSourceRequest], False),
        "SrtSettings": (SrtSettingsRequest, False),
        "Tags": (dict, False),
        "Type": (str, False),
        "Vpc": (InputVpcRequest, False),
    }


class InputWhitelistRuleCidr(AWSProperty):
    """
    `InputWhitelistRuleCidr <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-inputsecuritygroup-inputwhitelistrulecidr.html>`__
    """

    props: PropsDictType = {
        "Cidr": (str, False),
    }


class InputSecurityGroup(AWSObject):
    """
    `InputSecurityGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-medialive-inputsecuritygroup.html>`__
    """

    resource_type = "AWS::MediaLive::InputSecurityGroup"

    props: PropsDictType = {
        "Tags": (dict, False),
        "WhitelistRules": ([InputWhitelistRuleCidr], False),
    }


class MultiplexMediaConnectOutputDestinationSettings(AWSProperty):
    """
    `MultiplexMediaConnectOutputDestinationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-multiplex-multiplexmediaconnectoutputdestinationsettings.html>`__
    """

    props: PropsDictType = {
        "EntitlementArn": (str, False),
    }


class MultiplexOutputDestination(AWSProperty):
    """
    `MultiplexOutputDestination <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-multiplex-multiplexoutputdestination.html>`__
    """

    props: PropsDictType = {
        "MultiplexMediaConnectOutputDestinationSettings": (
            MultiplexMediaConnectOutputDestinationSettings,
            False,
        ),
    }


class MultiplexSettings(AWSProperty):
    """
    `MultiplexSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-multiplex-multiplexsettings.html>`__
    """

    props: PropsDictType = {
        "MaximumVideoBufferDelayMilliseconds": (integer, False),
        "TransportStreamBitrate": (integer, True),
        "TransportStreamId": (integer, True),
        "TransportStreamReservedBitrate": (integer, False),
    }


class Multiplex(AWSObject):
    """
    `Multiplex <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-medialive-multiplex.html>`__
    """

    resource_type = "AWS::MediaLive::Multiplex"

    props: PropsDictType = {
        "AvailabilityZones": ([str], True),
        "Destinations": ([MultiplexOutputDestination], False),
        "MultiplexSettings": (MultiplexSettings, True),
        "Name": (str, True),
        "Tags": (Tags, False),
    }


class MultiplexProgramPacketIdentifiersMap(AWSProperty):
    """
    `MultiplexProgramPacketIdentifiersMap <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-multiplexprogram-multiplexprogrampacketidentifiersmap.html>`__
    """

    props: PropsDictType = {
        "AudioPids": ([integer], False),
        "DvbSubPids": ([integer], False),
        "DvbTeletextPid": (integer, False),
        "EtvPlatformPid": (integer, False),
        "EtvSignalPid": (integer, False),
        "KlvDataPids": ([integer], False),
        "PcrPid": (integer, False),
        "PmtPid": (integer, False),
        "PrivateMetadataPid": (integer, False),
        "Scte27Pids": ([integer], False),
        "Scte35Pid": (integer, False),
        "TimedMetadataPid": (integer, False),
        "VideoPid": (integer, False),
    }


class MultiplexProgramPipelineDetail(AWSProperty):
    """
    `MultiplexProgramPipelineDetail <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-multiplexprogram-multiplexprogrampipelinedetail.html>`__
    """

    props: PropsDictType = {
        "ActiveChannelPipeline": (str, False),
        "PipelineId": (str, False),
    }


class MultiplexProgramServiceDescriptor(AWSProperty):
    """
    `MultiplexProgramServiceDescriptor <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-multiplexprogram-multiplexprogramservicedescriptor.html>`__
    """

    props: PropsDictType = {
        "ProviderName": (str, True),
        "ServiceName": (str, True),
    }


class MultiplexStatmuxVideoSettings(AWSProperty):
    """
    `MultiplexStatmuxVideoSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-multiplexprogram-multiplexstatmuxvideosettings.html>`__
    """

    props: PropsDictType = {
        "MaximumBitrate": (integer, False),
        "MinimumBitrate": (integer, False),
        "Priority": (integer, False),
    }


class MultiplexVideoSettings(AWSProperty):
    """
    `MultiplexVideoSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-multiplexprogram-multiplexvideosettings.html>`__
    """

    props: PropsDictType = {
        "ConstantBitrate": (integer, False),
        "StatmuxSettings": (MultiplexStatmuxVideoSettings, False),
    }


class MultiplexProgramSettings(AWSProperty):
    """
    `MultiplexProgramSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-multiplexprogram-multiplexprogramsettings.html>`__
    """

    props: PropsDictType = {
        "PreferredChannelPipeline": (str, False),
        "ProgramNumber": (integer, True),
        "ServiceDescriptor": (MultiplexProgramServiceDescriptor, False),
        "VideoSettings": (MultiplexVideoSettings, False),
    }


class Multiplexprogram(AWSObject):
    """
    `Multiplexprogram <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-medialive-multiplexprogram.html>`__
    """

    resource_type = "AWS::MediaLive::Multiplexprogram"

    props: PropsDictType = {
        "MultiplexId": (str, False),
        "MultiplexProgramSettings": (MultiplexProgramSettings, False),
        "PacketIdentifiersMap": (MultiplexProgramPacketIdentifiersMap, False),
        "PipelineDetails": ([MultiplexProgramPipelineDetail], False),
        "PreferredChannelPipeline": (str, False),
        "ProgramName": (str, False),
    }


class IpPool(AWSProperty):
    """
    `IpPool <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-network-ippool.html>`__
    """

    props: PropsDictType = {
        "Cidr": (str, False),
    }


class Route(AWSProperty):
    """
    `Route <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-network-route.html>`__
    """

    props: PropsDictType = {
        "Cidr": (str, False),
        "Gateway": (str, False),
    }


class Network(AWSObject):
    """
    `Network <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-medialive-network.html>`__
    """

    resource_type = "AWS::MediaLive::Network"

    props: PropsDictType = {
        "IpPools": ([IpPool], True),
        "Name": (str, True),
        "Routes": ([Route], False),
        "Tags": (Tags, False),
    }


class SdiSource(AWSObject):
    """
    `SdiSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-medialive-sdisource.html>`__
    """

    resource_type = "AWS::MediaLive::SdiSource"

    props: PropsDictType = {
        "Mode": (str, False),
        "Name": (str, True),
        "Tags": (Tags, False),
        "Type": (str, True),
    }


class SignalMap(AWSObject):
    """
    `SignalMap <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-medialive-signalmap.html>`__
    """

    resource_type = "AWS::MediaLive::SignalMap"

    props: PropsDictType = {
        "CloudWatchAlarmTemplateGroupIdentifiers": ([str], False),
        "Description": (str, False),
        "DiscoveryEntryPointArn": (str, True),
        "EventBridgeRuleTemplateGroupIdentifiers": ([str], False),
        "ForceRediscovery": (boolean, False),
        "Name": (str, True),
        "Tags": (dict, False),
    }


class InputDeviceRequest(AWSProperty):
    """
    `InputDeviceRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-input-inputdevicerequest.html>`__
    """

    props: PropsDictType = {
        "Id": (str, False),
    }


class MaintenanceUpdateSettings(AWSProperty):
    """
    `MaintenanceUpdateSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-channel-maintenanceupdatesettings.html>`__
    """

    props: PropsDictType = {
        "MaintenanceDay": (str, False),
        "MaintenanceScheduledDate": (str, False),
        "MaintenanceStartTime": (str, False),
    }


class MediaResourceNeighbor(AWSProperty):
    """
    `MediaResourceNeighbor <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-signalmap-mediaresourceneighbor.html>`__
    """

    props: PropsDictType = {
        "Arn": (str, True),
        "Name": (str, False),
    }


class MediaResource(AWSProperty):
    """
    `MediaResource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-signalmap-mediaresource.html>`__
    """

    props: PropsDictType = {
        "Destinations": ([MediaResourceNeighbor], False),
        "Name": (str, False),
        "Sources": ([MediaResourceNeighbor], False),
    }


class MonitorDeployment(AWSProperty):
    """
    `MonitorDeployment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-signalmap-monitordeployment.html>`__
    """

    props: PropsDictType = {
        "DetailsUri": (str, False),
        "ErrorMessage": (str, False),
        "Status": (str, True),
    }


class MulticastSourceUpdateRequest(AWSProperty):
    """
    `MulticastSourceUpdateRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-input-multicastsourceupdaterequest.html>`__
    """

    props: PropsDictType = {
        "SourceIp": (str, False),
        "Url": (str, False),
    }


class MulticastSettingsUpdateRequest(AWSProperty):
    """
    `MulticastSettingsUpdateRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-input-multicastsettingsupdaterequest.html>`__
    """

    props: PropsDictType = {
        "Sources": ([MulticastSourceUpdateRequest], False),
    }


class SuccessfulMonitorDeployment(AWSProperty):
    """
    `SuccessfulMonitorDeployment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-medialive-signalmap-successfulmonitordeployment.html>`__
    """

    props: PropsDictType = {
        "DetailsUri": (str, True),
        "Status": (str, True),
    }
