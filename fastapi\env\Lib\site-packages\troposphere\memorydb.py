# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, integer


class ACL(AWSObject):
    """
    `ACL <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-memorydb-acl.html>`__
    """

    resource_type = "AWS::MemoryDB::ACL"

    props: PropsDictType = {
        "ACLName": (str, True),
        "Tags": (Tags, False),
        "UserNames": ([str], False),
    }


class Endpoint(AWSProperty):
    """
    `Endpoint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-memorydb-cluster-endpoint.html>`__
    """

    props: PropsDictType = {
        "Address": (str, False),
        "Port": (integer, False),
    }


class Cluster(AWSObject):
    """
    `Cluster <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-memorydb-cluster.html>`__
    """

    resource_type = "AWS::MemoryDB::Cluster"

    props: PropsDictType = {
        "ACLName": (str, True),
        "AutoMinorVersionUpgrade": (boolean, False),
        "ClusterEndpoint": (Endpoint, False),
        "ClusterName": (str, True),
        "DataTiering": (str, False),
        "Description": (str, False),
        "Engine": (str, False),
        "EngineVersion": (str, False),
        "FinalSnapshotName": (str, False),
        "IpDiscovery": (str, False),
        "KmsKeyId": (str, False),
        "MaintenanceWindow": (str, False),
        "MultiRegionClusterName": (str, False),
        "NetworkType": (str, False),
        "NodeType": (str, True),
        "NumReplicasPerShard": (integer, False),
        "NumShards": (integer, False),
        "ParameterGroupName": (str, False),
        "Port": (integer, False),
        "SecurityGroupIds": ([str], False),
        "SnapshotArns": ([str], False),
        "SnapshotName": (str, False),
        "SnapshotRetentionLimit": (integer, False),
        "SnapshotWindow": (str, False),
        "SnsTopicArn": (str, False),
        "SnsTopicStatus": (str, False),
        "SubnetGroupName": (str, False),
        "TLSEnabled": (boolean, False),
        "Tags": (Tags, False),
    }


class MultiRegionCluster(AWSObject):
    """
    `MultiRegionCluster <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-memorydb-multiregioncluster.html>`__
    """

    resource_type = "AWS::MemoryDB::MultiRegionCluster"

    props: PropsDictType = {
        "Description": (str, False),
        "Engine": (str, False),
        "EngineVersion": (str, False),
        "MultiRegionClusterNameSuffix": (str, False),
        "MultiRegionParameterGroupName": (str, False),
        "NodeType": (str, True),
        "NumShards": (integer, False),
        "TLSEnabled": (boolean, False),
        "Tags": (Tags, False),
        "UpdateStrategy": (str, False),
    }


class ParameterGroup(AWSObject):
    """
    `ParameterGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-memorydb-parametergroup.html>`__
    """

    resource_type = "AWS::MemoryDB::ParameterGroup"

    props: PropsDictType = {
        "Description": (str, False),
        "Family": (str, True),
        "ParameterGroupName": (str, True),
        "Parameters": (dict, False),
        "Tags": (Tags, False),
    }


class SubnetGroup(AWSObject):
    """
    `SubnetGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-memorydb-subnetgroup.html>`__
    """

    resource_type = "AWS::MemoryDB::SubnetGroup"

    props: PropsDictType = {
        "Description": (str, False),
        "SubnetGroupName": (str, True),
        "SubnetIds": ([str], True),
        "Tags": (Tags, False),
    }


class AuthenticationMode(AWSProperty):
    """
    `AuthenticationMode <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-memorydb-user-authenticationmode.html>`__
    """

    props: PropsDictType = {
        "Passwords": ([str], False),
        "Type": (str, False),
    }


class User(AWSObject):
    """
    `User <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-memorydb-user.html>`__
    """

    resource_type = "AWS::MemoryDB::User"

    props: PropsDictType = {
        "AccessString": (str, False),
        "AuthenticationMode": (AuthenticationMode, False),
        "Tags": (Tags, False),
        "UserName": (str, True),
    }
