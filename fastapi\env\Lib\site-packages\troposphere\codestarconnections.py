# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, PropsDictType, Tags
from .validators.codestarconnections import validate_connection_providertype


class Connection(AWSObject):
    """
    `Connection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-codestarconnections-connection.html>`__
    """

    resource_type = "AWS::CodeStarConnections::Connection"

    props: PropsDictType = {
        "ConnectionName": (str, True),
        "HostArn": (str, False),
        "ProviderType": (validate_connection_providertype, False),
        "Tags": (Tags, False),
    }


class RepositoryLink(AWSObject):
    """
    `RepositoryLink <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-codestarconnections-repositorylink.html>`__
    """

    resource_type = "AWS::CodeStarConnections::RepositoryLink"

    props: PropsDictType = {
        "ConnectionArn": (str, True),
        "EncryptionKeyArn": (str, False),
        "OwnerId": (str, True),
        "RepositoryName": (str, True),
        "Tags": (Tags, False),
    }


class SyncConfiguration(AWSObject):
    """
    `SyncConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-codestarconnections-syncconfiguration.html>`__
    """

    resource_type = "AWS::CodeStarConnections::SyncConfiguration"

    props: PropsDictType = {
        "Branch": (str, True),
        "ConfigFile": (str, True),
        "PublishDeploymentStatus": (str, False),
        "RepositoryLinkId": (str, True),
        "ResourceName": (str, True),
        "RoleArn": (str, True),
        "SyncType": (str, True),
        "TriggerResourceUpdateOn": (str, False),
    }
