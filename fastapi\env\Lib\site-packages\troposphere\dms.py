# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer
from .validators.dms import CDC  # noqa: F401
from .validators.dms import FULL_LOAD  # noqa: F401
from .validators.dms import FULL_LOAD_AND_CDC  # noqa: F401
from .validators.dms import validate_network_port


class Certificate(AWSObject):
    """
    `Certificate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-dms-certificate.html>`__
    """

    resource_type = "AWS::DMS::Certificate"

    props: PropsDictType = {
        "CertificateIdentifier": (str, False),
        "CertificatePem": (str, False),
        "CertificateWallet": (str, False),
    }


class DataMigrationSettings(AWSProperty):
    """
    `DataMigrationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-datamigration-datamigrationsettings.html>`__
    """

    props: PropsDictType = {
        "CloudwatchLogsEnabled": (boolean, False),
        "NumberOfJobs": (integer, False),
        "SelectionRules": (str, False),
    }


class SourceDataSettings(AWSProperty):
    """
    `SourceDataSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-datamigration-sourcedatasettings.html>`__
    """

    props: PropsDictType = {
        "CDCStartPosition": (str, False),
        "CDCStartTime": (str, False),
        "CDCStopTime": (str, False),
        "SlotName": (str, False),
    }


class DataMigration(AWSObject):
    """
    `DataMigration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-dms-datamigration.html>`__
    """

    resource_type = "AWS::DMS::DataMigration"

    props: PropsDictType = {
        "DataMigrationIdentifier": (str, False),
        "DataMigrationName": (str, False),
        "DataMigrationSettings": (DataMigrationSettings, False),
        "DataMigrationType": (str, True),
        "MigrationProjectIdentifier": (str, True),
        "ServiceAccessRoleArn": (str, True),
        "SourceDataSettings": ([SourceDataSettings], False),
        "Tags": (Tags, False),
    }


class DocDbSettings(AWSProperty):
    """
    `DocDbSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-endpoint-docdbsettings.html>`__
    """

    props: PropsDictType = {
        "DocsToInvestigate": (integer, False),
        "ExtractDocId": (boolean, False),
        "NestingLevel": (str, False),
        "SecretsManagerAccessRoleArn": (str, False),
        "SecretsManagerSecretId": (str, False),
    }


class IbmDb2LuwSettings(AWSProperty):
    """
    `IbmDb2LuwSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-dataprovider-ibmdb2luwsettings.html>`__
    """

    props: PropsDictType = {
        "CertificateArn": (str, False),
        "DatabaseName": (str, True),
        "Port": (integer, True),
        "ServerName": (str, True),
        "SslMode": (str, True),
    }


class IbmDb2zOsSettings(AWSProperty):
    """
    `IbmDb2zOsSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-dataprovider-ibmdb2zossettings.html>`__
    """

    props: PropsDictType = {
        "CertificateArn": (str, False),
        "DatabaseName": (str, True),
        "Port": (integer, True),
        "ServerName": (str, True),
        "SslMode": (str, True),
    }


class MariaDbSettings(AWSProperty):
    """
    `MariaDbSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-dataprovider-mariadbsettings.html>`__
    """

    props: PropsDictType = {
        "CertificateArn": (str, False),
        "Port": (integer, True),
        "ServerName": (str, True),
        "SslMode": (str, True),
    }


class MicrosoftSqlServerSettings(AWSProperty):
    """
    `MicrosoftSqlServerSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-endpoint-microsoftsqlserversettings.html>`__
    """

    props: PropsDictType = {
        "BcpPacketSize": (integer, False),
        "ControlTablesFileGroup": (str, False),
        "DatabaseName": (str, False),
        "ForceLobLookup": (boolean, False),
        "Password": (str, False),
        "Port": (integer, False),
        "QuerySingleAlwaysOnNode": (boolean, False),
        "ReadBackupOnly": (boolean, False),
        "SafeguardPolicy": (str, False),
        "SecretsManagerAccessRoleArn": (str, False),
        "SecretsManagerSecretId": (str, False),
        "ServerName": (str, False),
        "TlogAccessMode": (str, False),
        "TrimSpaceInChar": (boolean, False),
        "UseBcpFullLoad": (boolean, False),
        "UseThirdPartyBackupDevice": (boolean, False),
        "Username": (str, False),
    }


class MongoDbSettings(AWSProperty):
    """
    `MongoDbSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-endpoint-mongodbsettings.html>`__
    """

    props: PropsDictType = {
        "AuthMechanism": (str, False),
        "AuthSource": (str, False),
        "AuthType": (str, False),
        "DatabaseName": (str, False),
        "DocsToInvestigate": (str, False),
        "ExtractDocId": (str, False),
        "NestingLevel": (str, False),
        "Password": (str, False),
        "Port": (validate_network_port, False),
        "SecretsManagerAccessRoleArn": (str, False),
        "SecretsManagerSecretId": (str, False),
        "ServerName": (str, False),
        "Username": (str, False),
    }


class MySqlSettings(AWSProperty):
    """
    `MySqlSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-endpoint-mysqlsettings.html>`__
    """

    props: PropsDictType = {
        "AfterConnectScript": (str, False),
        "CleanSourceMetadataOnMismatch": (boolean, False),
        "EventsPollInterval": (integer, False),
        "MaxFileSize": (integer, False),
        "ParallelLoadThreads": (integer, False),
        "SecretsManagerAccessRoleArn": (str, False),
        "SecretsManagerSecretId": (str, False),
        "ServerTimezone": (str, False),
        "TargetDbType": (str, False),
    }


class OracleSettings(AWSProperty):
    """
    `OracleSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-endpoint-oraclesettings.html>`__
    """

    props: PropsDictType = {
        "AccessAlternateDirectly": (boolean, False),
        "AddSupplementalLogging": (boolean, False),
        "AdditionalArchivedLogDestId": (integer, False),
        "AllowSelectNestedTables": (boolean, False),
        "ArchivedLogDestId": (integer, False),
        "ArchivedLogsOnly": (boolean, False),
        "AsmPassword": (str, False),
        "AsmServer": (str, False),
        "AsmUser": (str, False),
        "CharLengthSemantics": (str, False),
        "DirectPathNoLog": (boolean, False),
        "DirectPathParallelLoad": (boolean, False),
        "EnableHomogenousTablespace": (boolean, False),
        "ExtraArchivedLogDestIds": ([integer], False),
        "FailTasksOnLobTruncation": (boolean, False),
        "NumberDatatypeScale": (integer, False),
        "OraclePathPrefix": (str, False),
        "ParallelAsmReadThreads": (integer, False),
        "ReadAheadBlocks": (integer, False),
        "ReadTableSpaceName": (boolean, False),
        "ReplacePathPrefix": (boolean, False),
        "RetryInterval": (integer, False),
        "SecretsManagerAccessRoleArn": (str, False),
        "SecretsManagerOracleAsmAccessRoleArn": (str, False),
        "SecretsManagerOracleAsmSecretId": (str, False),
        "SecretsManagerSecretId": (str, False),
        "SecurityDbEncryption": (str, False),
        "SecurityDbEncryptionName": (str, False),
        "SpatialDataOptionToGeoJsonFunctionName": (str, False),
        "StandbyDelayTime": (integer, False),
        "UseAlternateFolderForOnline": (boolean, False),
        "UseBFile": (boolean, False),
        "UseDirectPathFullLoad": (boolean, False),
        "UseLogminerReader": (boolean, False),
        "UsePathPrefix": (str, False),
    }


class PostgreSqlSettings(AWSProperty):
    """
    `PostgreSqlSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-endpoint-postgresqlsettings.html>`__
    """

    props: PropsDictType = {
        "AfterConnectScript": (str, False),
        "BabelfishDatabaseName": (str, False),
        "CaptureDdls": (boolean, False),
        "DatabaseMode": (str, False),
        "DdlArtifactsSchema": (str, False),
        "ExecuteTimeout": (integer, False),
        "FailTasksOnLobTruncation": (boolean, False),
        "HeartbeatEnable": (boolean, False),
        "HeartbeatFrequency": (integer, False),
        "HeartbeatSchema": (str, False),
        "MapBooleanAsBoolean": (boolean, False),
        "MaxFileSize": (integer, False),
        "PluginName": (str, False),
        "SecretsManagerAccessRoleArn": (str, False),
        "SecretsManagerSecretId": (str, False),
        "SlotName": (str, False),
    }


class RedshiftSettings(AWSProperty):
    """
    `RedshiftSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-endpoint-redshiftsettings.html>`__
    """

    props: PropsDictType = {
        "AcceptAnyDate": (boolean, False),
        "AfterConnectScript": (str, False),
        "BucketFolder": (str, False),
        "BucketName": (str, False),
        "CaseSensitiveNames": (boolean, False),
        "CompUpdate": (boolean, False),
        "ConnectionTimeout": (integer, False),
        "DateFormat": (str, False),
        "EmptyAsNull": (boolean, False),
        "EncryptionMode": (str, False),
        "ExplicitIds": (boolean, False),
        "FileTransferUploadStreams": (integer, False),
        "LoadTimeout": (integer, False),
        "MapBooleanAsBoolean": (boolean, False),
        "MaxFileSize": (integer, False),
        "RemoveQuotes": (boolean, False),
        "ReplaceChars": (str, False),
        "ReplaceInvalidChars": (str, False),
        "SecretsManagerAccessRoleArn": (str, False),
        "SecretsManagerSecretId": (str, False),
        "ServerSideEncryptionKmsKeyId": (str, False),
        "ServiceAccessRoleArn": (str, False),
        "TimeFormat": (str, False),
        "TrimBlanks": (boolean, False),
        "TruncateColumns": (boolean, False),
        "WriteBufferSize": (integer, False),
    }


class Settings(AWSProperty):
    """
    `Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-dataprovider-settings.html>`__
    """

    props: PropsDictType = {
        "DocDbSettings": (DocDbSettings, False),
        "IbmDb2LuwSettings": (IbmDb2LuwSettings, False),
        "IbmDb2zOsSettings": (IbmDb2zOsSettings, False),
        "MariaDbSettings": (MariaDbSettings, False),
        "MicrosoftSqlServerSettings": (MicrosoftSqlServerSettings, False),
        "MongoDbSettings": (MongoDbSettings, False),
        "MySqlSettings": (MySqlSettings, False),
        "OracleSettings": (OracleSettings, False),
        "PostgreSqlSettings": (PostgreSqlSettings, False),
        "RedshiftSettings": (RedshiftSettings, False),
    }


class DataProvider(AWSObject):
    """
    `DataProvider <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-dms-dataprovider.html>`__
    """

    resource_type = "AWS::DMS::DataProvider"

    props: PropsDictType = {
        "DataProviderIdentifier": (str, False),
        "DataProviderName": (str, False),
        "Description": (str, False),
        "Engine": (str, True),
        "ExactSettings": (boolean, False),
        "Settings": (Settings, False),
        "Tags": (Tags, False),
    }


class DynamoDbSettings(AWSProperty):
    """
    `DynamoDbSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-endpoint-dynamodbsettings.html>`__
    """

    props: PropsDictType = {
        "ServiceAccessRoleArn": (str, False),
    }


class ElasticsearchSettings(AWSProperty):
    """
    `ElasticsearchSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-endpoint-elasticsearchsettings.html>`__
    """

    props: PropsDictType = {
        "EndpointUri": (str, False),
        "ErrorRetryDuration": (integer, False),
        "FullLoadErrorPercentage": (integer, False),
        "ServiceAccessRoleArn": (str, False),
    }


class GcpMySQLSettings(AWSProperty):
    """
    `GcpMySQLSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-endpoint-gcpmysqlsettings.html>`__
    """

    props: PropsDictType = {
        "AfterConnectScript": (str, False),
        "CleanSourceMetadataOnMismatch": (boolean, False),
        "DatabaseName": (str, False),
        "EventsPollInterval": (integer, False),
        "MaxFileSize": (integer, False),
        "ParallelLoadThreads": (integer, False),
        "Password": (str, False),
        "Port": (integer, False),
        "SecretsManagerAccessRoleArn": (str, False),
        "SecretsManagerSecretId": (str, False),
        "ServerName": (str, False),
        "ServerTimezone": (str, False),
        "Username": (str, False),
    }


class IbmDb2Settings(AWSProperty):
    """
    `IbmDb2Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-endpoint-ibmdb2settings.html>`__
    """

    props: PropsDictType = {
        "CurrentLsn": (str, False),
        "KeepCsvFiles": (boolean, False),
        "LoadTimeout": (integer, False),
        "MaxFileSize": (integer, False),
        "MaxKBytesPerRead": (integer, False),
        "SecretsManagerAccessRoleArn": (str, False),
        "SecretsManagerSecretId": (str, False),
        "SetDataCaptureChanges": (boolean, False),
        "WriteBufferSize": (integer, False),
    }


class KafkaSettings(AWSProperty):
    """
    `KafkaSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-endpoint-kafkasettings.html>`__
    """

    props: PropsDictType = {
        "Broker": (str, False),
        "IncludeControlDetails": (boolean, False),
        "IncludeNullAndEmpty": (boolean, False),
        "IncludePartitionValue": (boolean, False),
        "IncludeTableAlterOperations": (boolean, False),
        "IncludeTransactionDetails": (boolean, False),
        "MessageFormat": (str, False),
        "MessageMaxBytes": (integer, False),
        "NoHexPrefix": (boolean, False),
        "PartitionIncludeSchemaTable": (boolean, False),
        "SaslPassword": (str, False),
        "SaslUserName": (str, False),
        "SecurityProtocol": (str, False),
        "SslCaCertificateArn": (str, False),
        "SslClientCertificateArn": (str, False),
        "SslClientKeyArn": (str, False),
        "SslClientKeyPassword": (str, False),
        "Topic": (str, False),
    }


class KinesisSettings(AWSProperty):
    """
    `KinesisSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-endpoint-kinesissettings.html>`__
    """

    props: PropsDictType = {
        "IncludeControlDetails": (boolean, False),
        "IncludeNullAndEmpty": (boolean, False),
        "IncludePartitionValue": (boolean, False),
        "IncludeTableAlterOperations": (boolean, False),
        "IncludeTransactionDetails": (boolean, False),
        "MessageFormat": (str, False),
        "NoHexPrefix": (boolean, False),
        "PartitionIncludeSchemaTable": (boolean, False),
        "ServiceAccessRoleArn": (str, False),
        "StreamArn": (str, False),
    }


class NeptuneSettings(AWSProperty):
    """
    `NeptuneSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-endpoint-neptunesettings.html>`__
    """

    props: PropsDictType = {
        "ErrorRetryDuration": (integer, False),
        "IamAuthEnabled": (boolean, False),
        "MaxFileSize": (integer, False),
        "MaxRetryCount": (integer, False),
        "S3BucketFolder": (str, False),
        "S3BucketName": (str, False),
        "ServiceAccessRoleArn": (str, False),
    }


class RedisSettings(AWSProperty):
    """
    `RedisSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-endpoint-redissettings.html>`__
    """

    props: PropsDictType = {
        "AuthPassword": (str, False),
        "AuthType": (str, False),
        "AuthUserName": (str, False),
        "Port": (validate_network_port, False),
        "ServerName": (str, False),
        "SslCaCertificateArn": (str, False),
        "SslSecurityProtocol": (str, False),
    }


class S3Settings(AWSProperty):
    """
    `S3Settings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-endpoint-s3settings.html>`__
    """

    props: PropsDictType = {
        "AddColumnName": (boolean, False),
        "AddTrailingPaddingCharacter": (boolean, False),
        "BucketFolder": (str, False),
        "BucketName": (str, False),
        "CannedAclForObjects": (str, False),
        "CdcInsertsAndUpdates": (boolean, False),
        "CdcInsertsOnly": (boolean, False),
        "CdcMaxBatchInterval": (integer, False),
        "CdcMinFileSize": (integer, False),
        "CdcPath": (str, False),
        "CompressionType": (str, False),
        "CsvDelimiter": (str, False),
        "CsvNoSupValue": (str, False),
        "CsvNullValue": (str, False),
        "CsvRowDelimiter": (str, False),
        "DataFormat": (str, False),
        "DataPageSize": (integer, False),
        "DatePartitionDelimiter": (str, False),
        "DatePartitionEnabled": (boolean, False),
        "DatePartitionSequence": (str, False),
        "DatePartitionTimezone": (str, False),
        "DictPageSizeLimit": (integer, False),
        "EnableStatistics": (boolean, False),
        "EncodingType": (str, False),
        "EncryptionMode": (str, False),
        "ExpectedBucketOwner": (str, False),
        "ExternalTableDefinition": (str, False),
        "GlueCatalogGeneration": (boolean, False),
        "IgnoreHeaderRows": (integer, False),
        "IncludeOpForFullLoad": (boolean, False),
        "MaxFileSize": (integer, False),
        "ParquetTimestampInMillisecond": (boolean, False),
        "ParquetVersion": (str, False),
        "PreserveTransactions": (boolean, False),
        "Rfc4180": (boolean, False),
        "RowGroupLength": (integer, False),
        "ServerSideEncryptionKmsKeyId": (str, False),
        "ServiceAccessRoleArn": (str, False),
        "TimestampColumnName": (str, False),
        "UseCsvNoSupValue": (boolean, False),
        "UseTaskStartTimeForFullLoadTimestamp": (boolean, False),
    }


class SybaseSettings(AWSProperty):
    """
    `SybaseSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-endpoint-sybasesettings.html>`__
    """

    props: PropsDictType = {
        "SecretsManagerAccessRoleArn": (str, False),
        "SecretsManagerSecretId": (str, False),
    }


class Endpoint(AWSObject):
    """
    `Endpoint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-dms-endpoint.html>`__
    """

    resource_type = "AWS::DMS::Endpoint"

    props: PropsDictType = {
        "CertificateArn": (str, False),
        "DatabaseName": (str, False),
        "DocDbSettings": (DocDbSettings, False),
        "DynamoDbSettings": (DynamoDbSettings, False),
        "ElasticsearchSettings": (ElasticsearchSettings, False),
        "EndpointIdentifier": (str, False),
        "EndpointType": (str, True),
        "EngineName": (str, True),
        "ExtraConnectionAttributes": (str, False),
        "GcpMySQLSettings": (GcpMySQLSettings, False),
        "IbmDb2Settings": (IbmDb2Settings, False),
        "KafkaSettings": (KafkaSettings, False),
        "KinesisSettings": (KinesisSettings, False),
        "KmsKeyId": (str, False),
        "MicrosoftSqlServerSettings": (MicrosoftSqlServerSettings, False),
        "MongoDbSettings": (MongoDbSettings, False),
        "MySqlSettings": (MySqlSettings, False),
        "NeptuneSettings": (NeptuneSettings, False),
        "OracleSettings": (OracleSettings, False),
        "Password": (str, False),
        "Port": (validate_network_port, False),
        "PostgreSqlSettings": (PostgreSqlSettings, False),
        "RedisSettings": (RedisSettings, False),
        "RedshiftSettings": (RedshiftSettings, False),
        "ResourceIdentifier": (str, False),
        "S3Settings": (S3Settings, False),
        "ServerName": (str, False),
        "SslMode": (str, False),
        "SybaseSettings": (SybaseSettings, False),
        "Tags": (Tags, False),
        "Username": (str, False),
    }


class EventSubscription(AWSObject):
    """
    `EventSubscription <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-dms-eventsubscription.html>`__
    """

    resource_type = "AWS::DMS::EventSubscription"

    props: PropsDictType = {
        "Enabled": (boolean, False),
        "EventCategories": ([str], False),
        "SnsTopicArn": (str, True),
        "SourceIds": ([str], False),
        "SourceType": (str, False),
        "SubscriptionName": (str, False),
        "Tags": (Tags, False),
    }


class InstanceProfile(AWSObject):
    """
    `InstanceProfile <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-dms-instanceprofile.html>`__
    """

    resource_type = "AWS::DMS::InstanceProfile"

    props: PropsDictType = {
        "AvailabilityZone": (str, False),
        "Description": (str, False),
        "InstanceProfileIdentifier": (str, False),
        "InstanceProfileName": (str, False),
        "KmsKeyArn": (str, False),
        "NetworkType": (str, False),
        "PubliclyAccessible": (boolean, False),
        "SubnetGroupIdentifier": (str, False),
        "Tags": (Tags, False),
        "VpcSecurityGroups": ([str], False),
    }


class DataProviderDescriptor(AWSProperty):
    """
    `DataProviderDescriptor <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-migrationproject-dataproviderdescriptor.html>`__
    """

    props: PropsDictType = {
        "DataProviderArn": (str, False),
        "DataProviderIdentifier": (str, False),
        "DataProviderName": (str, False),
        "SecretsManagerAccessRoleArn": (str, False),
        "SecretsManagerSecretId": (str, False),
    }


class SchemaConversionApplicationAttributes(AWSProperty):
    """
    `SchemaConversionApplicationAttributes <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-migrationproject-schemaconversionapplicationattributes.html>`__
    """

    props: PropsDictType = {
        "S3BucketPath": (str, False),
        "S3BucketRoleArn": (str, False),
    }


class MigrationProject(AWSObject):
    """
    `MigrationProject <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-dms-migrationproject.html>`__
    """

    resource_type = "AWS::DMS::MigrationProject"

    props: PropsDictType = {
        "Description": (str, False),
        "InstanceProfileArn": (str, False),
        "InstanceProfileIdentifier": (str, False),
        "InstanceProfileName": (str, False),
        "MigrationProjectIdentifier": (str, False),
        "MigrationProjectName": (str, False),
        "SchemaConversionApplicationAttributes": (
            SchemaConversionApplicationAttributes,
            False,
        ),
        "SourceDataProviderDescriptors": ([DataProviderDescriptor], False),
        "Tags": (Tags, False),
        "TargetDataProviderDescriptors": ([DataProviderDescriptor], False),
        "TransformationRules": (str, False),
    }


class ComputeConfig(AWSProperty):
    """
    `ComputeConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-dms-replicationconfig-computeconfig.html>`__
    """

    props: PropsDictType = {
        "AvailabilityZone": (str, False),
        "DnsNameServers": (str, False),
        "KmsKeyId": (str, False),
        "MaxCapacityUnits": (integer, True),
        "MinCapacityUnits": (integer, False),
        "MultiAZ": (boolean, False),
        "PreferredMaintenanceWindow": (str, False),
        "ReplicationSubnetGroupId": (str, False),
        "VpcSecurityGroupIds": ([str], False),
    }


class ReplicationConfig(AWSObject):
    """
    `ReplicationConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-dms-replicationconfig.html>`__
    """

    resource_type = "AWS::DMS::ReplicationConfig"

    props: PropsDictType = {
        "ComputeConfig": (ComputeConfig, True),
        "ReplicationConfigIdentifier": (str, True),
        "ReplicationSettings": (dict, False),
        "ReplicationType": (str, True),
        "ResourceIdentifier": (str, False),
        "SourceEndpointArn": (str, True),
        "SupplementalSettings": (dict, False),
        "TableMappings": (dict, True),
        "Tags": (Tags, False),
        "TargetEndpointArn": (str, True),
    }


class ReplicationInstance(AWSObject):
    """
    `ReplicationInstance <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-dms-replicationinstance.html>`__
    """

    resource_type = "AWS::DMS::ReplicationInstance"

    props: PropsDictType = {
        "AllocatedStorage": (integer, False),
        "AllowMajorVersionUpgrade": (boolean, False),
        "AutoMinorVersionUpgrade": (boolean, False),
        "AvailabilityZone": (str, False),
        "DnsNameServers": (str, False),
        "EngineVersion": (str, False),
        "KmsKeyId": (str, False),
        "MultiAZ": (boolean, False),
        "NetworkType": (str, False),
        "PreferredMaintenanceWindow": (str, False),
        "PubliclyAccessible": (boolean, False),
        "ReplicationInstanceClass": (str, True),
        "ReplicationInstanceIdentifier": (str, False),
        "ReplicationSubnetGroupIdentifier": (str, False),
        "ResourceIdentifier": (str, False),
        "Tags": (Tags, False),
        "VpcSecurityGroupIds": ([str], False),
    }


class ReplicationSubnetGroup(AWSObject):
    """
    `ReplicationSubnetGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-dms-replicationsubnetgroup.html>`__
    """

    resource_type = "AWS::DMS::ReplicationSubnetGroup"

    props: PropsDictType = {
        "ReplicationSubnetGroupDescription": (str, True),
        "ReplicationSubnetGroupIdentifier": (str, False),
        "SubnetIds": ([str], True),
        "Tags": (Tags, False),
    }


class ReplicationTask(AWSObject):
    """
    `ReplicationTask <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-dms-replicationtask.html>`__
    """

    resource_type = "AWS::DMS::ReplicationTask"

    props: PropsDictType = {
        "CdcStartPosition": (str, False),
        "CdcStartTime": (double, False),
        "CdcStopPosition": (str, False),
        "MigrationType": (str, True),
        "ReplicationInstanceArn": (str, True),
        "ReplicationTaskIdentifier": (str, False),
        "ReplicationTaskSettings": (str, False),
        "ResourceIdentifier": (str, False),
        "SourceEndpointArn": (str, True),
        "TableMappings": (str, True),
        "Tags": (Tags, False),
        "TargetEndpointArn": (str, True),
        "TaskData": (str, False),
    }
