# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double


class AwsLocation(AWSProperty):
    """
    `AwsLocation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-awslocation.html>`__
    """

    props: PropsDictType = {
        "AccessRole": (str, False),
        "AwsAccountId": (str, False),
        "AwsRegion": (str, False),
        "IamConnectionId": (str, False),
    }


class AthenaPropertiesInput(AWSProperty):
    """
    `AthenaPropertiesInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-athenapropertiesinput.html>`__
    """

    props: PropsDictType = {
        "WorkgroupName": (str, True),
    }


class BasicAuthenticationCredentials(AWSProperty):
    """
    `BasicAuthenticationCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-basicauthenticationcredentials.html>`__
    """

    props: PropsDictType = {
        "Password": (str, False),
        "UserName": (str, False),
    }


class AuthorizationCodeProperties(AWSProperty):
    """
    `AuthorizationCodeProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-authorizationcodeproperties.html>`__
    """

    props: PropsDictType = {
        "AuthorizationCode": (str, False),
        "RedirectUri": (str, False),
    }


class GlueOAuth2Credentials(AWSProperty):
    """
    `GlueOAuth2Credentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-glueoauth2credentials.html>`__
    """

    props: PropsDictType = {
        "AccessToken": (str, False),
        "JwtToken": (str, False),
        "RefreshToken": (str, False),
        "UserManagedClientApplicationClientSecret": (str, False),
    }


class OAuth2ClientApplication(AWSProperty):
    """
    `OAuth2ClientApplication <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-oauth2clientapplication.html>`__
    """

    props: PropsDictType = {
        "AWSManagedClientApplicationReference": (str, False),
        "UserManagedClientApplicationClientId": (str, False),
    }


class OAuth2Properties(AWSProperty):
    """
    `OAuth2Properties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-oauth2properties.html>`__
    """

    props: PropsDictType = {
        "AuthorizationCodeProperties": (AuthorizationCodeProperties, False),
        "OAuth2ClientApplication": (OAuth2ClientApplication, False),
        "OAuth2Credentials": (GlueOAuth2Credentials, False),
        "OAuth2GrantType": (str, False),
        "TokenUrl": (str, False),
        "TokenUrlParametersMap": (dict, False),
    }


class AuthenticationConfigurationInput(AWSProperty):
    """
    `AuthenticationConfigurationInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-authenticationconfigurationinput.html>`__
    """

    props: PropsDictType = {
        "AuthenticationType": (str, False),
        "BasicAuthenticationCredentials": (BasicAuthenticationCredentials, False),
        "CustomAuthenticationCredentials": (dict, False),
        "KmsKeyArn": (str, False),
        "OAuth2Properties": (OAuth2Properties, False),
        "SecretArn": (str, False),
    }


class PhysicalConnectionRequirements(AWSProperty):
    """
    `PhysicalConnectionRequirements <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-physicalconnectionrequirements.html>`__
    """

    props: PropsDictType = {
        "AvailabilityZone": (str, False),
        "SecurityGroupIdList": ([str], False),
        "SubnetId": (str, False),
        "SubnetIdList": ([str], False),
    }


class GlueConnectionInput(AWSProperty):
    """
    `GlueConnectionInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-glueconnectioninput.html>`__
    """

    props: PropsDictType = {
        "AthenaProperties": (dict, False),
        "AuthenticationConfiguration": (AuthenticationConfigurationInput, False),
        "ConnectionProperties": (dict, False),
        "ConnectionType": (str, False),
        "Description": (str, False),
        "MatchCriteria": (str, False),
        "Name": (str, False),
        "PhysicalConnectionRequirements": (PhysicalConnectionRequirements, False),
        "PythonProperties": (dict, False),
        "SparkProperties": (dict, False),
        "ValidateCredentials": (boolean, False),
        "ValidateForComputeEnvironments": ([str], False),
    }


class GluePropertiesInput(AWSProperty):
    """
    `GluePropertiesInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-gluepropertiesinput.html>`__
    """

    props: PropsDictType = {
        "GlueConnectionInput": (GlueConnectionInput, False),
    }


class HyperPodPropertiesInput(AWSProperty):
    """
    `HyperPodPropertiesInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-hyperpodpropertiesinput.html>`__
    """

    props: PropsDictType = {
        "ClusterName": (str, True),
    }


class IamPropertiesInput(AWSProperty):
    """
    `IamPropertiesInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-iampropertiesinput.html>`__
    """

    props: PropsDictType = {
        "GlueLineageSyncEnabled": (boolean, False),
    }


class UsernamePassword(AWSProperty):
    """
    `UsernamePassword <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-usernamepassword.html>`__
    """

    props: PropsDictType = {
        "Password": (str, True),
        "Username": (str, True),
    }


class RedshiftCredentials(AWSProperty):
    """
    `RedshiftCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-redshiftcredentials.html>`__
    """

    props: PropsDictType = {
        "SecretArn": (str, False),
        "UsernamePassword": (UsernamePassword, False),
    }


class LineageSyncSchedule(AWSProperty):
    """
    `LineageSyncSchedule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-lineagesyncschedule.html>`__
    """

    props: PropsDictType = {
        "Schedule": (str, False),
    }


class RedshiftLineageSyncConfigurationInput(AWSProperty):
    """
    `RedshiftLineageSyncConfigurationInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-redshiftlineagesyncconfigurationinput.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, False),
        "Schedule": (LineageSyncSchedule, False),
    }


class RedshiftStorageProperties(AWSProperty):
    """
    `RedshiftStorageProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-redshiftstorageproperties.html>`__
    """

    props: PropsDictType = {
        "ClusterName": (str, False),
        "WorkgroupName": (str, False),
    }


class RedshiftPropertiesInput(AWSProperty):
    """
    `RedshiftPropertiesInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-redshiftpropertiesinput.html>`__
    """

    props: PropsDictType = {
        "Credentials": (RedshiftCredentials, False),
        "DatabaseName": (str, False),
        "Host": (str, False),
        "LineageSync": (RedshiftLineageSyncConfigurationInput, False),
        "Port": (double, False),
        "Storage": (RedshiftStorageProperties, False),
    }


class SparkEmrPropertiesInput(AWSProperty):
    """
    `SparkEmrPropertiesInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-sparkemrpropertiesinput.html>`__
    """

    props: PropsDictType = {
        "ComputeArn": (str, False),
        "InstanceProfileArn": (str, False),
        "JavaVirtualEnv": (str, False),
        "LogUri": (str, False),
        "PythonVirtualEnv": (str, False),
        "RuntimeRole": (str, False),
        "TrustedCertificatesS3Uri": (str, False),
    }


class SparkGlueArgs(AWSProperty):
    """
    `SparkGlueArgs <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-sparkglueargs.html>`__
    """

    props: PropsDictType = {
        "Connection": (str, False),
    }


class SparkGluePropertiesInput(AWSProperty):
    """
    `SparkGluePropertiesInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-sparkgluepropertiesinput.html>`__
    """

    props: PropsDictType = {
        "AdditionalArgs": (SparkGlueArgs, False),
        "GlueConnectionName": (str, False),
        "GlueVersion": (str, False),
        "IdleTimeout": (double, False),
        "JavaVirtualEnv": (str, False),
        "NumberOfWorkers": (double, False),
        "PythonVirtualEnv": (str, False),
        "WorkerType": (str, False),
    }


class ConnectionPropertiesInput(AWSProperty):
    """
    `ConnectionPropertiesInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-connection-connectionpropertiesinput.html>`__
    """

    props: PropsDictType = {
        "AthenaProperties": (AthenaPropertiesInput, False),
        "GlueProperties": (GluePropertiesInput, False),
        "HyperPodProperties": (HyperPodPropertiesInput, False),
        "IamProperties": (IamPropertiesInput, False),
        "RedshiftProperties": (RedshiftPropertiesInput, False),
        "SparkEmrProperties": (SparkEmrPropertiesInput, False),
        "SparkGlueProperties": (SparkGluePropertiesInput, False),
    }


class Connection(AWSObject):
    """
    `Connection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-datazone-connection.html>`__
    """

    resource_type = "AWS::DataZone::Connection"

    props: PropsDictType = {
        "AwsLocation": (AwsLocation, False),
        "Description": (str, False),
        "DomainIdentifier": (str, True),
        "EnvironmentIdentifier": (str, True),
        "Name": (str, True),
        "Props": (ConnectionPropertiesInput, False),
    }


class FilterExpression(AWSProperty):
    """
    `FilterExpression <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-datasource-filterexpression.html>`__
    """

    props: PropsDictType = {
        "Expression": (str, True),
        "Type": (str, True),
    }


class RelationalFilterConfiguration(AWSProperty):
    """
    `RelationalFilterConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-datasource-relationalfilterconfiguration.html>`__
    """

    props: PropsDictType = {
        "DatabaseName": (str, True),
        "FilterExpressions": ([FilterExpression], False),
        "SchemaName": (str, False),
    }


class GlueRunConfigurationInput(AWSProperty):
    """
    `GlueRunConfigurationInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-datasource-gluerunconfigurationinput.html>`__
    """

    props: PropsDictType = {
        "AutoImportDataQualityResult": (boolean, False),
        "CatalogName": (str, False),
        "DataAccessRole": (str, False),
        "RelationalFilterConfigurations": ([RelationalFilterConfiguration], True),
    }


class RedshiftCredentialConfiguration(AWSProperty):
    """
    `RedshiftCredentialConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-datasource-redshiftcredentialconfiguration.html>`__
    """

    props: PropsDictType = {
        "SecretManagerArn": (str, True),
    }


class RedshiftClusterStorage(AWSProperty):
    """
    `RedshiftClusterStorage <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-datasource-redshiftclusterstorage.html>`__
    """

    props: PropsDictType = {
        "ClusterName": (str, True),
    }


class RedshiftServerlessStorage(AWSProperty):
    """
    `RedshiftServerlessStorage <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-datasource-redshiftserverlessstorage.html>`__
    """

    props: PropsDictType = {
        "WorkgroupName": (str, True),
    }


class RedshiftStorage(AWSProperty):
    """
    `RedshiftStorage <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-datasource-redshiftstorage.html>`__
    """

    props: PropsDictType = {
        "RedshiftClusterSource": (RedshiftClusterStorage, False),
        "RedshiftServerlessSource": (RedshiftServerlessStorage, False),
    }


class RedshiftRunConfigurationInput(AWSProperty):
    """
    `RedshiftRunConfigurationInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-datasource-redshiftrunconfigurationinput.html>`__
    """

    props: PropsDictType = {
        "DataAccessRole": (str, False),
        "RedshiftCredentialConfiguration": (RedshiftCredentialConfiguration, False),
        "RedshiftStorage": (RedshiftStorage, False),
        "RelationalFilterConfigurations": ([RelationalFilterConfiguration], True),
    }


class SageMakerRunConfigurationInput(AWSProperty):
    """
    `SageMakerRunConfigurationInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-datasource-sagemakerrunconfigurationinput.html>`__
    """

    props: PropsDictType = {
        "TrackingAssets": (dict, True),
    }


class DataSourceConfigurationInput(AWSProperty):
    """
    `DataSourceConfigurationInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-datasource-datasourceconfigurationinput.html>`__
    """

    props: PropsDictType = {
        "GlueRunConfiguration": (GlueRunConfigurationInput, False),
        "RedshiftRunConfiguration": (RedshiftRunConfigurationInput, False),
        "SageMakerRunConfiguration": (SageMakerRunConfigurationInput, False),
    }


class FormInput(AWSProperty):
    """
    `FormInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-datasource-forminput.html>`__
    """

    props: PropsDictType = {
        "Content": (str, False),
        "FormName": (str, True),
        "TypeIdentifier": (str, False),
        "TypeRevision": (str, False),
    }


class RecommendationConfiguration(AWSProperty):
    """
    `RecommendationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-datasource-recommendationconfiguration.html>`__
    """

    props: PropsDictType = {
        "EnableBusinessNameGeneration": (boolean, False),
    }


class ScheduleConfiguration(AWSProperty):
    """
    `ScheduleConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-datasource-scheduleconfiguration.html>`__
    """

    props: PropsDictType = {
        "Schedule": (str, False),
        "Timezone": (str, False),
    }


class DataSource(AWSObject):
    """
    `DataSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-datazone-datasource.html>`__
    """

    resource_type = "AWS::DataZone::DataSource"

    props: PropsDictType = {
        "AssetFormsInput": ([FormInput], False),
        "Configuration": (DataSourceConfigurationInput, False),
        "ConnectionIdentifier": (str, False),
        "Description": (str, False),
        "DomainIdentifier": (str, True),
        "EnableSetting": (str, False),
        "EnvironmentIdentifier": (str, False),
        "Name": (str, True),
        "ProjectIdentifier": (str, True),
        "PublishOnImport": (boolean, False),
        "Recommendation": (RecommendationConfiguration, False),
        "Schedule": (ScheduleConfiguration, False),
        "Type": (str, True),
    }


class SingleSignOn(AWSProperty):
    """
    `SingleSignOn <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-domain-singlesignon.html>`__
    """

    props: PropsDictType = {
        "Type": (str, False),
        "UserAssignment": (str, False),
    }


class Domain(AWSObject):
    """
    `Domain <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-datazone-domain.html>`__
    """

    resource_type = "AWS::DataZone::Domain"

    props: PropsDictType = {
        "Description": (str, False),
        "DomainExecutionRole": (str, True),
        "DomainVersion": (str, False),
        "KmsKeyIdentifier": (str, False),
        "Name": (str, True),
        "ServiceRole": (str, False),
        "SingleSignOn": (SingleSignOn, False),
        "Tags": (Tags, False),
    }


class EnvironmentParameter(AWSProperty):
    """
    `EnvironmentParameter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-environmentprofile-environmentparameter.html>`__
    """

    props: PropsDictType = {
        "Name": (str, False),
        "Value": (str, False),
    }


class Environment(AWSObject):
    """
    `Environment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-datazone-environment.html>`__
    """

    resource_type = "AWS::DataZone::Environment"

    props: PropsDictType = {
        "Description": (str, False),
        "DomainIdentifier": (str, True),
        "EnvironmentAccountIdentifier": (str, False),
        "EnvironmentAccountRegion": (str, False),
        "EnvironmentProfileIdentifier": (str, False),
        "EnvironmentRoleArn": (str, False),
        "GlossaryTerms": ([str], False),
        "Name": (str, True),
        "ProjectIdentifier": (str, True),
        "UserParameters": ([EnvironmentParameter], False),
    }


class AwsConsoleLinkParameters(AWSProperty):
    """
    `AwsConsoleLinkParameters <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-environmentactions-awsconsolelinkparameters.html>`__
    """

    props: PropsDictType = {
        "Uri": (str, False),
    }


class EnvironmentActions(AWSObject):
    """
    `EnvironmentActions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-datazone-environmentactions.html>`__
    """

    resource_type = "AWS::DataZone::EnvironmentActions"

    props: PropsDictType = {
        "Description": (str, False),
        "DomainIdentifier": (str, False),
        "EnvironmentIdentifier": (str, False),
        "Identifier": (str, False),
        "Name": (str, True),
        "Parameters": (AwsConsoleLinkParameters, False),
    }


class RegionalParameter(AWSProperty):
    """
    `RegionalParameter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-environmentblueprintconfiguration-regionalparameter.html>`__
    """

    props: PropsDictType = {
        "Parameters": (dict, False),
        "Region": (str, False),
    }


class EnvironmentBlueprintConfiguration(AWSObject):
    """
    `EnvironmentBlueprintConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-datazone-environmentblueprintconfiguration.html>`__
    """

    resource_type = "AWS::DataZone::EnvironmentBlueprintConfiguration"

    props: PropsDictType = {
        "DomainIdentifier": (str, True),
        "EnabledRegions": ([str], True),
        "EnvironmentBlueprintIdentifier": (str, True),
        "ManageAccessRoleArn": (str, False),
        "ProvisioningRoleArn": (str, False),
        "RegionalParameters": ([RegionalParameter], False),
    }


class EnvironmentProfile(AWSObject):
    """
    `EnvironmentProfile <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-datazone-environmentprofile.html>`__
    """

    resource_type = "AWS::DataZone::EnvironmentProfile"

    props: PropsDictType = {
        "AwsAccountId": (str, True),
        "AwsAccountRegion": (str, True),
        "Description": (str, False),
        "DomainIdentifier": (str, True),
        "EnvironmentBlueprintIdentifier": (str, True),
        "Name": (str, True),
        "ProjectIdentifier": (str, True),
        "UserParameters": ([EnvironmentParameter], False),
    }


class GroupProfile(AWSObject):
    """
    `GroupProfile <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-datazone-groupprofile.html>`__
    """

    resource_type = "AWS::DataZone::GroupProfile"

    props: PropsDictType = {
        "DomainIdentifier": (str, True),
        "GroupIdentifier": (str, True),
        "Status": (str, False),
    }


class Project(AWSObject):
    """
    `Project <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-datazone-project.html>`__
    """

    resource_type = "AWS::DataZone::Project"

    props: PropsDictType = {
        "Description": (str, False),
        "DomainIdentifier": (str, True),
        "GlossaryTerms": ([str], False),
        "Name": (str, True),
    }


class Member(AWSProperty):
    """
    `Member <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-projectmembership-member.html>`__
    """

    props: PropsDictType = {
        "GroupIdentifier": (str, False),
        "UserIdentifier": (str, False),
    }


class ProjectMembership(AWSObject):
    """
    `ProjectMembership <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-datazone-projectmembership.html>`__
    """

    resource_type = "AWS::DataZone::ProjectMembership"

    props: PropsDictType = {
        "Designation": (str, True),
        "DomainIdentifier": (str, True),
        "Member": (Member, True),
        "ProjectIdentifier": (str, True),
    }


class SubscriptionTargetForm(AWSProperty):
    """
    `SubscriptionTargetForm <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-subscriptiontarget-subscriptiontargetform.html>`__
    """

    props: PropsDictType = {
        "Content": (str, True),
        "FormName": (str, True),
    }


class SubscriptionTarget(AWSObject):
    """
    `SubscriptionTarget <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-datazone-subscriptiontarget.html>`__
    """

    resource_type = "AWS::DataZone::SubscriptionTarget"

    props: PropsDictType = {
        "ApplicableAssetTypes": ([str], True),
        "AuthorizedPrincipals": ([str], True),
        "DomainIdentifier": (str, True),
        "EnvironmentIdentifier": (str, True),
        "ManageAccessRole": (str, False),
        "Name": (str, True),
        "Provider": (str, False),
        "SubscriptionTargetConfig": ([SubscriptionTargetForm], True),
        "Type": (str, True),
    }


class UserProfile(AWSObject):
    """
    `UserProfile <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-datazone-userprofile.html>`__
    """

    resource_type = "AWS::DataZone::UserProfile"

    props: PropsDictType = {
        "DomainIdentifier": (str, True),
        "Status": (str, False),
        "UserIdentifier": (str, True),
        "UserType": (str, False),
    }


class IamUserProfileDetails(AWSProperty):
    """
    `IamUserProfileDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-userprofile-iamuserprofiledetails.html>`__
    """

    props: PropsDictType = {
        "Arn": (str, False),
    }


class SsoUserProfileDetails(AWSProperty):
    """
    `SsoUserProfileDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-userprofile-ssouserprofiledetails.html>`__
    """

    props: PropsDictType = {
        "FirstName": (str, False),
        "LastName": (str, False),
        "Username": (str, False),
    }


class UserProfileDetails(AWSProperty):
    """
    `UserProfileDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datazone-userprofile-userprofiledetails.html>`__
    """

    props: PropsDictType = {
        "Iam": (IamUserProfileDetails, False),
        "Sso": (SsoUserProfileDetails, False),
    }
