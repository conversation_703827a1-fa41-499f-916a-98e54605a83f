# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, PropsDictType, Tags
from .validators.organizations import validate_policy_type


class Account(AWSObject):
    """
    `Account <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-organizations-account.html>`__
    """

    resource_type = "AWS::Organizations::Account"

    props: PropsDictType = {
        "AccountName": (str, True),
        "Email": (str, True),
        "ParentIds": ([str], False),
        "RoleName": (str, False),
        "Tags": (Tags, False),
    }


class Organization(AWSObject):
    """
    `Organization <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-organizations-organization.html>`__
    """

    resource_type = "AWS::Organizations::Organization"

    props: PropsDictType = {
        "FeatureSet": (str, False),
    }


class OrganizationalUnit(AWSObject):
    """
    `OrganizationalUnit <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-organizations-organizationalunit.html>`__
    """

    resource_type = "AWS::Organizations::OrganizationalUnit"

    props: PropsDictType = {
        "Name": (str, True),
        "ParentId": (str, True),
        "Tags": (Tags, False),
    }


class Policy(AWSObject):
    """
    `Policy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-organizations-policy.html>`__
    """

    resource_type = "AWS::Organizations::Policy"

    props: PropsDictType = {
        "Content": (dict, True),
        "Description": (str, False),
        "Name": (str, True),
        "Tags": (Tags, False),
        "TargetIds": ([str], False),
        "Type": (validate_policy_type, True),
    }


class ResourcePolicy(AWSObject):
    """
    `ResourcePolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-organizations-resourcepolicy.html>`__
    """

    resource_type = "AWS::Organizations::ResourcePolicy"

    props: PropsDictType = {
        "Content": (dict, True),
        "Tags": (Tags, False),
    }
