# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer
from .validators.wafv2 import (
    validate_comparison_operator,
    validate_custom_response_bodies,
    validate_ipaddress_version,
    validate_positional_constraint,
    validate_statement,
    validate_statements,
    validate_transformation_type,
    wafv2_custom_body_response_content,
    wafv2_custom_body_response_content_type,
)


class IPSet(AWSObject):
    """
    `IPSet <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wafv2-ipset.html>`__
    """

    resource_type = "AWS::WAFv2::IPSet"

    props: PropsDictType = {
        "Addresses": ([str], True),
        "Description": (str, False),
        "IPAddressVersion": (validate_ipaddress_version, True),
        "Name": (str, False),
        "Scope": (str, True),
        "Tags": (Tags, False),
    }


class SingleHeader(AWSProperty):
    """
    `SingleHeader <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-singleheader.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
    }


class LoggingConfigurationFieldToMatch(AWSProperty):
    """
    `LoggingConfigurationFieldToMatch <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-loggingconfiguration-fieldtomatch.html>`__
    """

    props: PropsDictType = {
        "Method": (dict, False),
        "QueryString": (dict, False),
        "SingleHeader": (SingleHeader, False),
        "UriPath": (dict, False),
    }


class ActionCondition(AWSProperty):
    """
    `ActionCondition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-loggingconfiguration-actioncondition.html>`__
    """

    props: PropsDictType = {
        "Action": (str, True),
    }


class LabelNameCondition(AWSProperty):
    """
    `LabelNameCondition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-loggingconfiguration-labelnamecondition.html>`__
    """

    props: PropsDictType = {
        "LabelName": (str, True),
    }


class Condition(AWSProperty):
    """
    `Condition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-loggingconfiguration-condition.html>`__
    """

    props: PropsDictType = {
        "ActionCondition": (ActionCondition, False),
        "LabelNameCondition": (LabelNameCondition, False),
    }


class Filter(AWSProperty):
    """
    `Filter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-loggingconfiguration-filter.html>`__
    """

    props: PropsDictType = {
        "Behavior": (str, True),
        "Conditions": ([Condition], True),
        "Requirement": (str, True),
    }


class LoggingFilter(AWSProperty):
    """
    `LoggingFilter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-loggingconfiguration-loggingfilter.html>`__
    """

    props: PropsDictType = {
        "DefaultBehavior": (str, True),
        "Filters": ([Filter], True),
    }


class LoggingConfiguration(AWSObject):
    """
    `LoggingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wafv2-loggingconfiguration.html>`__
    """

    resource_type = "AWS::WAFv2::LoggingConfiguration"

    props: PropsDictType = {
        "LogDestinationConfigs": ([str], True),
        "LoggingFilter": (LoggingFilter, False),
        "RedactedFields": ([LoggingConfigurationFieldToMatch], False),
        "ResourceArn": (str, True),
    }


class RegexPatternSet(AWSObject):
    """
    `RegexPatternSet <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wafv2-regexpatternset.html>`__
    """

    resource_type = "AWS::WAFv2::RegexPatternSet"

    props: PropsDictType = {
        "Description": (str, False),
        "Name": (str, False),
        "RegularExpressionList": ([str], True),
        "Scope": (str, True),
        "Tags": (Tags, False),
    }


class CustomResponseBody(AWSProperty):
    """
    `CustomResponseBody <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-customresponsebody.html>`__
    """

    props: PropsDictType = {
        "Content": (wafv2_custom_body_response_content, True),
        "ContentType": (wafv2_custom_body_response_content_type, True),
    }


class LabelSummary(AWSProperty):
    """
    `LabelSummary <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-rulegroup-labelsummary.html>`__
    """

    props: PropsDictType = {
        "Name": (str, False),
    }


class ImmunityTimeProperty(AWSProperty):
    """
    `ImmunityTimeProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-immunitytimeproperty.html>`__
    """

    props: PropsDictType = {
        "ImmunityTime": (integer, True),
    }


class CaptchaConfig(AWSProperty):
    """
    `CaptchaConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-captchaconfig.html>`__
    """

    props: PropsDictType = {
        "ImmunityTimeProperty": (ImmunityTimeProperty, False),
    }


class ChallengeConfig(AWSProperty):
    """
    `ChallengeConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-challengeconfig.html>`__
    """

    props: PropsDictType = {
        "ImmunityTimeProperty": (ImmunityTimeProperty, False),
    }


class Label(AWSProperty):
    """
    `Label <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-label.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
    }


class CustomHTTPHeader(AWSProperty):
    """
    `CustomHTTPHeader <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-customhttpheader.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "Value": (str, True),
    }


class CustomRequestHandling(AWSProperty):
    """
    `CustomRequestHandling <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-customrequesthandling.html>`__
    """

    props: PropsDictType = {
        "InsertHeaders": ([CustomHTTPHeader], True),
    }


class AllowAction(AWSProperty):
    """
    `AllowAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-allowaction.html>`__
    """

    props: PropsDictType = {
        "CustomRequestHandling": (CustomRequestHandling, False),
    }


class CustomResponse(AWSProperty):
    """
    `CustomResponse <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-customresponse.html>`__
    """

    props: PropsDictType = {
        "CustomResponseBodyKey": (str, False),
        "ResponseCode": (integer, True),
        "ResponseHeaders": ([CustomHTTPHeader], False),
    }


class BlockAction(AWSProperty):
    """
    `BlockAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-blockaction.html>`__
    """

    props: PropsDictType = {
        "CustomResponse": (CustomResponse, False),
    }


class CaptchaAction(AWSProperty):
    """
    `CaptchaAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-captchaaction.html>`__
    """

    props: PropsDictType = {
        "CustomRequestHandling": (CustomRequestHandling, False),
    }


class ChallengeAction(AWSProperty):
    """
    `ChallengeAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-challengeaction.html>`__
    """

    props: PropsDictType = {
        "CustomRequestHandling": (CustomRequestHandling, False),
    }


class CountAction(AWSProperty):
    """
    `CountAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-countaction.html>`__
    """

    props: PropsDictType = {
        "CustomRequestHandling": (CustomRequestHandling, False),
    }


class RuleAction(AWSProperty):
    """
    `RuleAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-ruleaction.html>`__
    """

    props: PropsDictType = {
        "Allow": (AllowAction, False),
        "Block": (BlockAction, False),
        "Captcha": (CaptchaAction, False),
        "Challenge": (ChallengeAction, False),
        "Count": (CountAction, False),
    }


class AndStatement(AWSProperty):
    """
    `AndStatement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-andstatement.html>`__
    """

    props: PropsDictType = {
        "Statements": (validate_statements, True),
    }


class Body(AWSProperty):
    """
    `Body <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-body.html>`__
    """

    props: PropsDictType = {
        "OversizeHandling": (str, False),
    }


class CookieMatchPattern(AWSProperty):
    """
    `CookieMatchPattern <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-cookiematchpattern.html>`__
    """

    props: PropsDictType = {
        "All": (dict, False),
        "ExcludedCookies": ([str], False),
        "IncludedCookies": ([str], False),
    }


class Cookies(AWSProperty):
    """
    `Cookies <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-cookies.html>`__
    """

    props: PropsDictType = {
        "MatchPattern": (CookieMatchPattern, True),
        "MatchScope": (str, True),
        "OversizeHandling": (str, True),
    }


class HeaderMatchPattern(AWSProperty):
    """
    `HeaderMatchPattern <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-headermatchpattern.html>`__
    """

    props: PropsDictType = {
        "All": (dict, False),
        "ExcludedHeaders": ([str], False),
        "IncludedHeaders": ([str], False),
    }


class Headers(AWSProperty):
    """
    `Headers <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-headers.html>`__
    """

    props: PropsDictType = {
        "MatchPattern": (HeaderMatchPattern, True),
        "MatchScope": (str, True),
        "OversizeHandling": (str, True),
    }


class JA3Fingerprint(AWSProperty):
    """
    `JA3Fingerprint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-ja3fingerprint.html>`__
    """

    props: PropsDictType = {
        "FallbackBehavior": (str, True),
    }


class JA4Fingerprint(AWSProperty):
    """
    `JA4Fingerprint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-ja4fingerprint.html>`__
    """

    props: PropsDictType = {
        "FallbackBehavior": (str, True),
    }


class JsonMatchPattern(AWSProperty):
    """
    `JsonMatchPattern <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-jsonmatchpattern.html>`__
    """

    props: PropsDictType = {
        "All": (dict, False),
        "IncludedPaths": ([str], False),
    }


class JsonBody(AWSProperty):
    """
    `JsonBody <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-jsonbody.html>`__
    """

    props: PropsDictType = {
        "InvalidFallbackBehavior": (str, False),
        "MatchPattern": (JsonMatchPattern, True),
        "MatchScope": (str, True),
        "OversizeHandling": (str, False),
    }


class SingleQueryArgument(AWSProperty):
    """
    `SingleQueryArgument <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-singlequeryargument.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
    }


class UriFragment(AWSProperty):
    """
    `UriFragment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-urifragment.html>`__
    """

    props: PropsDictType = {
        "FallbackBehavior": (str, False),
    }


class FieldToMatch(AWSProperty):
    """
    `FieldToMatch <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-fieldtomatch.html>`__
    """

    props: PropsDictType = {
        "AllQueryArguments": (dict, False),
        "Body": (Body, False),
        "Cookies": (Cookies, False),
        "Headers": (Headers, False),
        "JA3Fingerprint": (JA3Fingerprint, False),
        "JA4Fingerprint": (JA4Fingerprint, False),
        "JsonBody": (JsonBody, False),
        "Method": (dict, False),
        "QueryString": (dict, False),
        "SingleHeader": (SingleHeader, False),
        "SingleQueryArgument": (SingleQueryArgument, False),
        "UriFragment": (UriFragment, False),
        "UriPath": (dict, False),
    }


class TextTransformation(AWSProperty):
    """
    `TextTransformation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-texttransformation.html>`__
    """

    props: PropsDictType = {
        "Priority": (integer, True),
        "Type": (validate_transformation_type, True),
    }


class ByteMatchStatement(AWSProperty):
    """
    `ByteMatchStatement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-bytematchstatement.html>`__
    """

    props: PropsDictType = {
        "FieldToMatch": (FieldToMatch, True),
        "PositionalConstraint": (validate_positional_constraint, True),
        "SearchString": (str, False),
        "SearchStringBase64": (str, False),
        "TextTransformations": ([TextTransformation], True),
    }


class ForwardedIPConfiguration(AWSProperty):
    """
    `ForwardedIPConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-forwardedipconfiguration.html>`__
    """

    props: PropsDictType = {
        "FallbackBehavior": (str, True),
        "HeaderName": (str, True),
    }


class GeoMatchStatement(AWSProperty):
    """
    `GeoMatchStatement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-geomatchstatement.html>`__
    """

    props: PropsDictType = {
        "CountryCodes": ([str], False),
        "ForwardedIPConfig": (ForwardedIPConfiguration, False),
    }


class IPSetForwardedIPConfiguration(AWSProperty):
    """
    `IPSetForwardedIPConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-ipsetforwardedipconfiguration.html>`__
    """

    props: PropsDictType = {
        "FallbackBehavior": (str, True),
        "HeaderName": (str, True),
        "Position": (str, True),
    }


class IPSetReferenceStatement(AWSProperty):
    """
    `IPSetReferenceStatement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-ipsetreferencestatement.html>`__
    """

    props: PropsDictType = {
        "Arn": (str, True),
        "IPSetForwardedIPConfig": (IPSetForwardedIPConfiguration, False),
    }


class LabelMatchStatement(AWSProperty):
    """
    `LabelMatchStatement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-labelmatchstatement.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Scope": (str, True),
    }


class ExcludedRule(AWSProperty):
    """
    `ExcludedRule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-excludedrule.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
    }


class FieldIdentifier(AWSProperty):
    """
    `FieldIdentifier <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-fieldidentifier.html>`__
    """

    props: PropsDictType = {
        "Identifier": (str, True),
    }


class RequestInspectionACFP(AWSProperty):
    """
    `RequestInspectionACFP <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-requestinspectionacfp.html>`__
    """

    props: PropsDictType = {
        "AddressFields": ([FieldIdentifier], False),
        "EmailField": (FieldIdentifier, False),
        "PasswordField": (FieldIdentifier, False),
        "PayloadType": (str, True),
        "PhoneNumberFields": ([FieldIdentifier], False),
        "UsernameField": (FieldIdentifier, False),
    }


class ResponseInspectionBodyContains(AWSProperty):
    """
    `ResponseInspectionBodyContains <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-responseinspectionbodycontains.html>`__
    """

    props: PropsDictType = {
        "FailureStrings": ([str], True),
        "SuccessStrings": ([str], True),
    }


class ResponseInspectionHeader(AWSProperty):
    """
    `ResponseInspectionHeader <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-responseinspectionheader.html>`__
    """

    props: PropsDictType = {
        "FailureValues": ([str], True),
        "Name": (str, True),
        "SuccessValues": ([str], True),
    }


class ResponseInspectionJson(AWSProperty):
    """
    `ResponseInspectionJson <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-responseinspectionjson.html>`__
    """

    props: PropsDictType = {
        "FailureValues": ([str], True),
        "Identifier": (str, True),
        "SuccessValues": ([str], True),
    }


class ResponseInspectionStatusCode(AWSProperty):
    """
    `ResponseInspectionStatusCode <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-responseinspectionstatuscode.html>`__
    """

    props: PropsDictType = {
        "FailureCodes": ([integer], True),
        "SuccessCodes": ([integer], True),
    }


class ResponseInspection(AWSProperty):
    """
    `ResponseInspection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-responseinspection.html>`__
    """

    props: PropsDictType = {
        "BodyContains": (ResponseInspectionBodyContains, False),
        "Header": (ResponseInspectionHeader, False),
        "Json": (ResponseInspectionJson, False),
        "StatusCode": (ResponseInspectionStatusCode, False),
    }


class AWSManagedRulesACFPRuleSet(AWSProperty):
    """
    `AWSManagedRulesACFPRuleSet <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-awsmanagedrulesacfpruleset.html>`__
    """

    props: PropsDictType = {
        "CreationPath": (str, True),
        "EnableRegexInPath": (boolean, False),
        "RegistrationPagePath": (str, True),
        "RequestInspection": (RequestInspectionACFP, True),
        "ResponseInspection": (ResponseInspection, False),
    }


class RequestInspection(AWSProperty):
    """
    `RequestInspection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-requestinspection.html>`__
    """

    props: PropsDictType = {
        "PasswordField": (FieldIdentifier, True),
        "PayloadType": (str, True),
        "UsernameField": (FieldIdentifier, True),
    }


class AWSManagedRulesATPRuleSet(AWSProperty):
    """
    `AWSManagedRulesATPRuleSet <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-awsmanagedrulesatpruleset.html>`__
    """

    props: PropsDictType = {
        "EnableRegexInPath": (boolean, False),
        "LoginPath": (str, True),
        "RequestInspection": (RequestInspection, False),
        "ResponseInspection": (ResponseInspection, False),
    }


class AWSManagedRulesBotControlRuleSet(AWSProperty):
    """
    `AWSManagedRulesBotControlRuleSet <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-awsmanagedrulesbotcontrolruleset.html>`__
    """

    props: PropsDictType = {
        "EnableMachineLearning": (boolean, False),
        "InspectionLevel": (str, True),
    }


class ManagedRuleGroupConfig(AWSProperty):
    """
    `ManagedRuleGroupConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-managedrulegroupconfig.html>`__
    """

    props: PropsDictType = {
        "AWSManagedRulesACFPRuleSet": (AWSManagedRulesACFPRuleSet, False),
        "AWSManagedRulesATPRuleSet": (AWSManagedRulesATPRuleSet, False),
        "AWSManagedRulesBotControlRuleSet": (AWSManagedRulesBotControlRuleSet, False),
        "LoginPath": (str, False),
        "PasswordField": (FieldIdentifier, False),
        "PayloadType": (str, False),
        "UsernameField": (FieldIdentifier, False),
    }


class RuleActionOverride(AWSProperty):
    """
    `RuleActionOverride <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-ruleactionoverride.html>`__
    """

    props: PropsDictType = {
        "ActionToUse": (RuleAction, True),
        "Name": (str, True),
    }


class ManagedRuleGroupStatement(AWSProperty):
    """
    `ManagedRuleGroupStatement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-managedrulegroupstatement.html>`__
    """

    props: PropsDictType = {
        "ExcludedRules": ([ExcludedRule], False),
        "ManagedRuleGroupConfigs": ([ManagedRuleGroupConfig], False),
        "Name": (str, True),
        "RuleActionOverrides": ([RuleActionOverride], False),
        "ScopeDownStatement": (validate_statement, False),
        "VendorName": (str, True),
        "Version": (str, False),
    }


class NotStatement(AWSProperty):
    """
    `NotStatement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-notstatement.html>`__
    """

    props: PropsDictType = {
        "Statement": (validate_statement, True),
    }


class OrStatement(AWSProperty):
    """
    `OrStatement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-orstatement.html>`__
    """

    props: PropsDictType = {
        "Statements": (validate_statements, True),
    }


class RateLimitCookie(AWSProperty):
    """
    `RateLimitCookie <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-ratelimitcookie.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "TextTransformations": ([TextTransformation], True),
    }


class RateLimitHeader(AWSProperty):
    """
    `RateLimitHeader <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-ratelimitheader.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "TextTransformations": ([TextTransformation], True),
    }


class RateLimitJA3Fingerprint(AWSProperty):
    """
    `RateLimitJA3Fingerprint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-ratelimitja3fingerprint.html>`__
    """

    props: PropsDictType = {
        "FallbackBehavior": (str, True),
    }


class RateLimitJA4Fingerprint(AWSProperty):
    """
    `RateLimitJA4Fingerprint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-ratelimitja4fingerprint.html>`__
    """

    props: PropsDictType = {
        "FallbackBehavior": (str, True),
    }


class RateLimitLabelNamespace(AWSProperty):
    """
    `RateLimitLabelNamespace <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-ratelimitlabelnamespace.html>`__
    """

    props: PropsDictType = {
        "Namespace": (str, True),
    }


class RateLimitQueryArgument(AWSProperty):
    """
    `RateLimitQueryArgument <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-ratelimitqueryargument.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "TextTransformations": ([TextTransformation], True),
    }


class RateLimitQueryString(AWSProperty):
    """
    `RateLimitQueryString <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-ratelimitquerystring.html>`__
    """

    props: PropsDictType = {
        "TextTransformations": ([TextTransformation], True),
    }


class RateLimitUriPath(AWSProperty):
    """
    `RateLimitUriPath <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-ratelimituripath.html>`__
    """

    props: PropsDictType = {
        "TextTransformations": ([TextTransformation], True),
    }


class RateBasedStatementCustomKey(AWSProperty):
    """
    `RateBasedStatementCustomKey <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-ratebasedstatementcustomkey.html>`__
    """

    props: PropsDictType = {
        "Cookie": (RateLimitCookie, False),
        "ForwardedIP": (dict, False),
        "HTTPMethod": (dict, False),
        "Header": (RateLimitHeader, False),
        "IP": (dict, False),
        "JA3Fingerprint": (RateLimitJA3Fingerprint, False),
        "JA4Fingerprint": (RateLimitJA4Fingerprint, False),
        "LabelNamespace": (RateLimitLabelNamespace, False),
        "QueryArgument": (RateLimitQueryArgument, False),
        "QueryString": (RateLimitQueryString, False),
        "UriPath": (RateLimitUriPath, False),
    }


class RateBasedStatement(AWSProperty):
    """
    `RateBasedStatement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-ratebasedstatement.html>`__
    """

    props: PropsDictType = {
        "AggregateKeyType": (str, True),
        "CustomKeys": ([RateBasedStatementCustomKey], False),
        "EvaluationWindowSec": (integer, False),
        "ForwardedIPConfig": (ForwardedIPConfiguration, False),
        "Limit": (integer, True),
        "ScopeDownStatement": (validate_statement, False),
    }


class RegexMatchStatement(AWSProperty):
    """
    `RegexMatchStatement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-regexmatchstatement.html>`__
    """

    props: PropsDictType = {
        "FieldToMatch": (FieldToMatch, True),
        "RegexString": (str, True),
        "TextTransformations": ([TextTransformation], True),
    }


class RegexPatternSetReferenceStatement(AWSProperty):
    """
    `RegexPatternSetReferenceStatement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-regexpatternsetreferencestatement.html>`__
    """

    props: PropsDictType = {
        "Arn": (str, True),
        "FieldToMatch": (FieldToMatch, True),
        "TextTransformations": ([TextTransformation], True),
    }


class RuleGroupReferenceStatement(AWSProperty):
    """
    `RuleGroupReferenceStatement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-rulegroupreferencestatement.html>`__
    """

    props: PropsDictType = {
        "Arn": (str, True),
        "ExcludedRules": ([ExcludedRule], False),
        "RuleActionOverrides": ([RuleActionOverride], False),
    }


class SizeConstraintStatement(AWSProperty):
    """
    `SizeConstraintStatement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-sizeconstraintstatement.html>`__
    """

    props: PropsDictType = {
        "ComparisonOperator": (validate_comparison_operator, True),
        "FieldToMatch": (FieldToMatch, True),
        "Size": (double, True),
        "TextTransformations": ([TextTransformation], True),
    }


class SqliMatchStatement(AWSProperty):
    """
    `SqliMatchStatement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-sqlimatchstatement.html>`__
    """

    props: PropsDictType = {
        "FieldToMatch": (FieldToMatch, True),
        "SensitivityLevel": (str, False),
        "TextTransformations": ([TextTransformation], True),
    }


class XssMatchStatement(AWSProperty):
    """
    `XssMatchStatement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-xssmatchstatement.html>`__
    """

    props: PropsDictType = {
        "FieldToMatch": (FieldToMatch, True),
        "TextTransformations": ([TextTransformation], True),
    }


class Statement(AWSProperty):
    """
    `Statement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-statement.html>`__
    """

    props: PropsDictType = {
        "AndStatement": (AndStatement, False),
        "ByteMatchStatement": (ByteMatchStatement, False),
        "GeoMatchStatement": (GeoMatchStatement, False),
        "IPSetReferenceStatement": (IPSetReferenceStatement, False),
        "LabelMatchStatement": (LabelMatchStatement, False),
        "ManagedRuleGroupStatement": (ManagedRuleGroupStatement, False),
        "NotStatement": (NotStatement, False),
        "OrStatement": (OrStatement, False),
        "RateBasedStatement": (RateBasedStatement, False),
        "RegexMatchStatement": (RegexMatchStatement, False),
        "RegexPatternSetReferenceStatement": (RegexPatternSetReferenceStatement, False),
        "RuleGroupReferenceStatement": (RuleGroupReferenceStatement, False),
        "SizeConstraintStatement": (SizeConstraintStatement, False),
        "SqliMatchStatement": (SqliMatchStatement, False),
        "XssMatchStatement": (XssMatchStatement, False),
    }


class VisibilityConfig(AWSProperty):
    """
    `VisibilityConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-visibilityconfig.html>`__
    """

    props: PropsDictType = {
        "CloudWatchMetricsEnabled": (boolean, True),
        "MetricName": (str, True),
        "SampledRequestsEnabled": (boolean, True),
    }


class RuleGroupRule(AWSProperty):
    """
    `RuleGroupRule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-rulegroup-rule.html>`__
    """

    props: PropsDictType = {
        "Action": (RuleAction, False),
        "CaptchaConfig": (CaptchaConfig, False),
        "ChallengeConfig": (ChallengeConfig, False),
        "Name": (str, True),
        "Priority": (integer, True),
        "RuleLabels": ([Label], False),
        "Statement": (validate_statement, True),
        "VisibilityConfig": (VisibilityConfig, True),
    }


class RuleGroup(AWSObject):
    """
    `RuleGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wafv2-rulegroup.html>`__
    """

    resource_type = "AWS::WAFv2::RuleGroup"

    props: PropsDictType = {
        "AvailableLabels": ([LabelSummary], False),
        "Capacity": (integer, True),
        "ConsumedLabels": ([LabelSummary], False),
        "CustomResponseBodies": (validate_custom_response_bodies, False),
        "Description": (str, False),
        "Name": (str, False),
        "Rules": ([RuleGroupRule], False),
        "Scope": (str, True),
        "Tags": (Tags, False),
        "VisibilityConfig": (VisibilityConfig, True),
    }


class RequestBodyAssociatedResourceTypeConfig(AWSProperty):
    """
    `RequestBodyAssociatedResourceTypeConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-requestbodyassociatedresourcetypeconfig.html>`__
    """

    props: PropsDictType = {
        "DefaultSizeInspectionLimit": (str, True),
    }


class AssociationConfig(AWSProperty):
    """
    `AssociationConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-associationconfig.html>`__
    """

    props: PropsDictType = {
        "RequestBody": (dict, False),
    }


class FieldToProtect(AWSProperty):
    """
    `FieldToProtect <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-fieldtoprotect.html>`__
    """

    props: PropsDictType = {
        "FieldKeys": ([str], False),
        "FieldType": (str, True),
    }


class DataProtect(AWSProperty):
    """
    `DataProtect <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-dataprotect.html>`__
    """

    props: PropsDictType = {
        "Action": (str, True),
        "ExcludeRateBasedDetails": (boolean, False),
        "ExcludeRuleMatchDetails": (boolean, False),
        "Field": (FieldToProtect, True),
    }


class DataProtectionConfig(AWSProperty):
    """
    `DataProtectionConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-dataprotectionconfig.html>`__
    """

    props: PropsDictType = {
        "DataProtections": ([DataProtect], True),
    }


class DefaultAction(AWSProperty):
    """
    `DefaultAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-defaultaction.html>`__
    """

    props: PropsDictType = {
        "Allow": (AllowAction, False),
        "Block": (BlockAction, False),
    }


class OverrideAction(AWSProperty):
    """
    `OverrideAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-overrideaction.html>`__
    """

    props: PropsDictType = {
        "Count": (dict, False),
        "None": (dict, False),
    }


class WebACLRule(AWSProperty):
    """
    `WebACLRule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-wafv2-webacl-rule.html>`__
    """

    props: PropsDictType = {
        "Action": (RuleAction, False),
        "CaptchaConfig": (CaptchaConfig, False),
        "ChallengeConfig": (ChallengeConfig, False),
        "Name": (str, True),
        "OverrideAction": (OverrideAction, False),
        "Priority": (integer, True),
        "RuleLabels": ([Label], False),
        "Statement": (validate_statement, True),
        "VisibilityConfig": (VisibilityConfig, True),
    }


class WebACL(AWSObject):
    """
    `WebACL <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wafv2-webacl.html>`__
    """

    resource_type = "AWS::WAFv2::WebACL"

    props: PropsDictType = {
        "AssociationConfig": (AssociationConfig, False),
        "CaptchaConfig": (CaptchaConfig, False),
        "ChallengeConfig": (ChallengeConfig, False),
        "CustomResponseBodies": (validate_custom_response_bodies, False),
        "DataProtectionConfig": (DataProtectionConfig, False),
        "DefaultAction": (DefaultAction, True),
        "Description": (str, False),
        "Name": (str, False),
        "Rules": ([WebACLRule], False),
        "Scope": (str, True),
        "Tags": (Tags, False),
        "TokenDomains": ([str], False),
        "VisibilityConfig": (VisibilityConfig, True),
    }


class WebACLAssociation(AWSObject):
    """
    `WebACLAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-wafv2-webaclassociation.html>`__
    """

    resource_type = "AWS::WAFv2::WebACLAssociation"

    props: PropsDictType = {
        "ResourceArn": (str, True),
        "WebACLArn": (str, True),
    }
