# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType
from .validators import boolean, double, integer
from .validators.batch import (
    validate_allocation_strategy,
    validate_environment_state,
    validate_launch_template_specification,
    validate_queue_state,
)


class Ec2ConfigurationObject(AWSProperty):
    """
    `Ec2ConfigurationObject <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-computeenvironment-ec2configurationobject.html>`__
    """

    props: PropsDictType = {
        "ImageIdOverride": (str, False),
        "ImageKubernetesVersion": (str, False),
        "ImageType": (str, True),
    }


class LaunchTemplateSpecificationOverride(AWSProperty):
    """
    `LaunchTemplateSpecificationOverride <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-computeenvironment-launchtemplatespecificationoverride.html>`__
    """

    props: PropsDictType = {
        "LaunchTemplateId": (str, False),
        "LaunchTemplateName": (str, False),
        "TargetInstanceTypes": ([str], False),
        "Version": (str, False),
    }


class LaunchTemplateSpecification(AWSProperty):
    """
    `LaunchTemplateSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-computeenvironment-launchtemplatespecification.html>`__
    """

    props: PropsDictType = {
        "LaunchTemplateId": (str, False),
        "LaunchTemplateName": (str, False),
        "Overrides": ([LaunchTemplateSpecificationOverride], False),
        "Version": (str, False),
    }

    def validate(self):
        validate_launch_template_specification(self)


class ComputeResources(AWSProperty):
    """
    `ComputeResources <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-computeenvironment-computeresources.html>`__
    """

    props: PropsDictType = {
        "AllocationStrategy": (validate_allocation_strategy, False),
        "BidPercentage": (integer, False),
        "DesiredvCpus": (integer, False),
        "Ec2Configuration": ([Ec2ConfigurationObject], False),
        "Ec2KeyPair": (str, False),
        "ImageId": (str, False),
        "InstanceRole": (str, False),
        "InstanceTypes": ([str], False),
        "LaunchTemplate": (LaunchTemplateSpecification, False),
        "MaxvCpus": (integer, True),
        "MinvCpus": (integer, False),
        "PlacementGroup": (str, False),
        "SecurityGroupIds": ([str], False),
        "SpotIamFleetRole": (str, False),
        "Subnets": ([str], True),
        "Tags": (dict, False),
        "Type": (str, True),
        "UpdateToLatestImageVersion": (boolean, False),
    }


class EksConfiguration(AWSProperty):
    """
    `EksConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-computeenvironment-eksconfiguration.html>`__
    """

    props: PropsDictType = {
        "EksClusterArn": (str, True),
        "KubernetesNamespace": (str, True),
    }


class UpdatePolicy(AWSProperty):
    """
    `UpdatePolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-computeenvironment-updatepolicy.html>`__
    """

    props: PropsDictType = {
        "JobExecutionTimeoutMinutes": (integer, False),
        "TerminateJobsOnUpdate": (boolean, False),
    }


class ComputeEnvironment(AWSObject):
    """
    `ComputeEnvironment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-batch-computeenvironment.html>`__
    """

    resource_type = "AWS::Batch::ComputeEnvironment"

    props: PropsDictType = {
        "ComputeEnvironmentName": (str, False),
        "ComputeResources": (ComputeResources, False),
        "Context": (str, False),
        "EksConfiguration": (EksConfiguration, False),
        "ReplaceComputeEnvironment": (boolean, False),
        "ServiceRole": (str, False),
        "State": (validate_environment_state, False),
        "Tags": (dict, False),
        "Type": (str, True),
        "UnmanagedvCpus": (integer, False),
        "UpdatePolicy": (UpdatePolicy, False),
    }


class ConsumableResource(AWSObject):
    """
    `ConsumableResource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-batch-consumableresource.html>`__
    """

    resource_type = "AWS::Batch::ConsumableResource"

    props: PropsDictType = {
        "ConsumableResourceName": (str, False),
        "ResourceType": (str, True),
        "Tags": (dict, False),
        "TotalQuantity": (integer, True),
    }


class ConsumableResourceRequirement(AWSProperty):
    """
    `ConsumableResourceRequirement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-consumableresourcerequirement.html>`__
    """

    props: PropsDictType = {
        "ConsumableResource": (str, True),
        "Quantity": (integer, True),
    }


class ConsumableResourceProperties(AWSProperty):
    """
    `ConsumableResourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-consumableresourceproperties.html>`__
    """

    props: PropsDictType = {
        "ConsumableResourceList": ([ConsumableResourceRequirement], True),
    }


class Environment(AWSProperty):
    """
    `Environment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-environment.html>`__
    """

    props: PropsDictType = {
        "Name": (str, False),
        "Value": (str, False),
    }


class EphemeralStorage(AWSProperty):
    """
    `EphemeralStorage <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-ephemeralstorage.html>`__
    """

    props: PropsDictType = {
        "SizeInGiB": (integer, True),
    }


class FargatePlatformConfiguration(AWSProperty):
    """
    `FargatePlatformConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-fargateplatformconfiguration.html>`__
    """

    props: PropsDictType = {
        "PlatformVersion": (str, False),
    }


class Device(AWSProperty):
    """
    `Device <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-device.html>`__
    """

    props: PropsDictType = {
        "ContainerPath": (str, False),
        "HostPath": (str, False),
        "Permissions": ([str], False),
    }


class Tmpfs(AWSProperty):
    """
    `Tmpfs <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-tmpfs.html>`__
    """

    props: PropsDictType = {
        "ContainerPath": (str, True),
        "MountOptions": ([str], False),
        "Size": (integer, True),
    }


class LinuxParameters(AWSProperty):
    """
    `LinuxParameters <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-linuxparameters.html>`__
    """

    props: PropsDictType = {
        "Devices": ([Device], False),
        "InitProcessEnabled": (boolean, False),
        "MaxSwap": (integer, False),
        "SharedMemorySize": (integer, False),
        "Swappiness": (integer, False),
        "Tmpfs": ([Tmpfs], False),
    }


class Secret(AWSProperty):
    """
    `Secret <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-secret.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "ValueFrom": (str, True),
    }


class LogConfiguration(AWSProperty):
    """
    `LogConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-logconfiguration.html>`__
    """

    props: PropsDictType = {
        "LogDriver": (str, True),
        "Options": (dict, False),
        "SecretOptions": ([Secret], False),
    }


class MountPoints(AWSProperty):
    """
    `MountPoints <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-mountpoint.html>`__
    """

    props: PropsDictType = {
        "ContainerPath": (str, False),
        "ReadOnly": (boolean, False),
        "SourceVolume": (str, False),
    }


class NetworkConfiguration(AWSProperty):
    """
    `NetworkConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-networkconfiguration.html>`__
    """

    props: PropsDictType = {
        "AssignPublicIp": (str, False),
    }


class RepositoryCredentials(AWSProperty):
    """
    `RepositoryCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-repositorycredentials.html>`__
    """

    props: PropsDictType = {
        "CredentialsParameter": (str, True),
    }


class ResourceRequirement(AWSProperty):
    """
    `ResourceRequirement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-resourcerequirement.html>`__
    """

    props: PropsDictType = {
        "Type": (str, False),
        "Value": (str, False),
    }


class RuntimePlatform(AWSProperty):
    """
    `RuntimePlatform <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-runtimeplatform.html>`__
    """

    props: PropsDictType = {
        "CpuArchitecture": (str, False),
        "OperatingSystemFamily": (str, False),
    }


class Ulimit(AWSProperty):
    """
    `Ulimit <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-ulimit.html>`__
    """

    props: PropsDictType = {
        "HardLimit": (integer, True),
        "Name": (str, True),
        "SoftLimit": (integer, True),
    }


class AuthorizationConfig(AWSProperty):
    """
    `AuthorizationConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-efsauthorizationconfig.html>`__
    """

    props: PropsDictType = {
        "AccessPointId": (str, False),
        "Iam": (str, False),
    }


class EfsVolumeConfiguration(AWSProperty):
    """
    `EfsVolumeConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-efsvolumeconfiguration.html>`__
    """

    props: PropsDictType = {
        "AuthorizationConfig": (AuthorizationConfig, False),
        "FileSystemId": (str, True),
        "RootDirectory": (str, False),
        "TransitEncryption": (str, False),
        "TransitEncryptionPort": (integer, False),
    }


class VolumesHost(AWSProperty):
    """
    `VolumesHost <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-host.html>`__
    """

    props: PropsDictType = {
        "SourcePath": (str, False),
    }


class Volumes(AWSProperty):
    """
    `Volumes <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-volume.html>`__
    """

    props: PropsDictType = {
        "EfsVolumeConfiguration": (EfsVolumeConfiguration, False),
        "Host": (VolumesHost, False),
        "Name": (str, False),
    }


class ContainerProperties(AWSProperty):
    """
    `ContainerProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-containerproperties.html>`__
    """

    props: PropsDictType = {
        "Command": ([str], False),
        "Environment": ([Environment], False),
        "EphemeralStorage": (EphemeralStorage, False),
        "ExecutionRoleArn": (str, False),
        "FargatePlatformConfiguration": (FargatePlatformConfiguration, False),
        "Image": (str, True),
        "JobRoleArn": (str, False),
        "LinuxParameters": (LinuxParameters, False),
        "LogConfiguration": (LogConfiguration, False),
        "Memory": (integer, False),
        "MountPoints": ([MountPoints], False),
        "NetworkConfiguration": (NetworkConfiguration, False),
        "Privileged": (boolean, False),
        "ReadonlyRootFilesystem": (boolean, False),
        "RepositoryCredentials": (RepositoryCredentials, False),
        "ResourceRequirements": ([ResourceRequirement], False),
        "RuntimePlatform": (RuntimePlatform, False),
        "Secrets": ([Secret], False),
        "Ulimits": ([Ulimit], False),
        "User": (str, False),
        "Vcpus": (integer, False),
        "Volumes": ([Volumes], False),
    }


class TaskContainerDependency(AWSProperty):
    """
    `TaskContainerDependency <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-taskcontainerdependency.html>`__
    """

    props: PropsDictType = {
        "Condition": (str, True),
        "ContainerName": (str, True),
    }


class TaskContainerProperties(AWSProperty):
    """
    `TaskContainerProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-taskcontainerproperties.html>`__
    """

    props: PropsDictType = {
        "Command": ([str], False),
        "DependsOn": ([TaskContainerDependency], False),
        "Environment": ([Environment], False),
        "Essential": (boolean, False),
        "Image": (str, True),
        "LinuxParameters": (LinuxParameters, False),
        "LogConfiguration": (LogConfiguration, False),
        "MountPoints": ([MountPoints], False),
        "Name": (str, False),
        "Privileged": (boolean, False),
        "ReadonlyRootFilesystem": (boolean, False),
        "RepositoryCredentials": (RepositoryCredentials, False),
        "ResourceRequirements": ([ResourceRequirement], False),
        "Secrets": ([Secret], False),
        "Ulimits": ([Ulimit], False),
        "User": (str, False),
    }


class EcsTaskProperties(AWSProperty):
    """
    `EcsTaskProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-ecstaskproperties.html>`__
    """

    props: PropsDictType = {
        "Containers": ([TaskContainerProperties], False),
        "EphemeralStorage": (EphemeralStorage, False),
        "ExecutionRoleArn": (str, False),
        "IpcMode": (str, False),
        "NetworkConfiguration": (NetworkConfiguration, False),
        "PidMode": (str, False),
        "PlatformVersion": (str, False),
        "RuntimePlatform": (RuntimePlatform, False),
        "TaskRoleArn": (str, False),
        "Volumes": ([Volumes], False),
    }


class EcsProperties(AWSProperty):
    """
    `EcsProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-ecsproperties.html>`__
    """

    props: PropsDictType = {
        "TaskProperties": ([EcsTaskProperties], True),
    }


class EksContainerEnvironmentVariable(AWSProperty):
    """
    `EksContainerEnvironmentVariable <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-ekscontainerenvironmentvariable.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "Value": (str, False),
    }


class EksContainerResourceRequirements(AWSProperty):
    """
    `EksContainerResourceRequirements <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-ekscontainerresourcerequirements.html>`__
    """

    props: PropsDictType = {
        "Limits": (dict, False),
        "Requests": (dict, False),
    }


class EksContainerSecurityContext(AWSProperty):
    """
    `EksContainerSecurityContext <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-ekscontainersecuritycontext.html>`__
    """

    props: PropsDictType = {
        "AllowPrivilegeEscalation": (boolean, False),
        "Privileged": (boolean, False),
        "ReadOnlyRootFilesystem": (boolean, False),
        "RunAsGroup": (integer, False),
        "RunAsNonRoot": (boolean, False),
        "RunAsUser": (integer, False),
    }


class EksContainerVolumeMount(AWSProperty):
    """
    `EksContainerVolumeMount <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-ekscontainervolumemount.html>`__
    """

    props: PropsDictType = {
        "MountPath": (str, False),
        "Name": (str, False),
        "ReadOnly": (boolean, False),
        "SubPath": (str, False),
    }


class EksContainer(AWSProperty):
    """
    `EksContainer <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-ekscontainer.html>`__
    """

    props: PropsDictType = {
        "Args": ([str], False),
        "Command": ([str], False),
        "Env": ([EksContainerEnvironmentVariable], False),
        "Image": (str, True),
        "ImagePullPolicy": (str, False),
        "Name": (str, False),
        "Resources": (EksContainerResourceRequirements, False),
        "SecurityContext": (EksContainerSecurityContext, False),
        "VolumeMounts": ([EksContainerVolumeMount], False),
    }


class EksEmptyDir(AWSProperty):
    """
    `EksEmptyDir <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-eksemptydir.html>`__
    """

    props: PropsDictType = {
        "Medium": (str, False),
        "SizeLimit": (str, False),
    }


class EksHostPath(AWSProperty):
    """
    `EksHostPath <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-ekshostpath.html>`__
    """

    props: PropsDictType = {
        "Path": (str, False),
    }


class EksPersistentVolumeClaim(AWSProperty):
    """
    `EksPersistentVolumeClaim <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-ekspersistentvolumeclaim.html>`__
    """

    props: PropsDictType = {
        "ClaimName": (str, True),
        "ReadOnly": (boolean, False),
    }


class EksSecret(AWSProperty):
    """
    `EksSecret <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-ekssecret.html>`__
    """

    props: PropsDictType = {
        "Optional": (boolean, False),
        "SecretName": (str, True),
    }


class EksVolume(AWSProperty):
    """
    `EksVolume <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-eksvolume.html>`__
    """

    props: PropsDictType = {
        "EmptyDir": (EksEmptyDir, False),
        "HostPath": (EksHostPath, False),
        "Name": (str, True),
        "PersistentVolumeClaim": (EksPersistentVolumeClaim, False),
        "Secret": (EksSecret, False),
    }


class ImagePullSecret(AWSProperty):
    """
    `ImagePullSecret <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-imagepullsecret.html>`__
    """

    props: PropsDictType = {
        "Name": (str, False),
    }


class Metadata(AWSProperty):
    """
    `Metadata <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-eksmetadata.html>`__
    """

    props: PropsDictType = {
        "Annotations": (dict, False),
        "Labels": (dict, False),
        "Namespace": (str, False),
    }


class PodProperties(AWSProperty):
    """
    `PodProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-ekspodproperties.html>`__
    """

    props: PropsDictType = {
        "Containers": ([EksContainer], False),
        "DnsPolicy": (str, False),
        "HostNetwork": (boolean, False),
        "ImagePullSecrets": ([ImagePullSecret], False),
        "InitContainers": ([EksContainer], False),
        "Metadata": (Metadata, False),
        "ServiceAccountName": (str, False),
        "ShareProcessNamespace": (boolean, False),
        "Volumes": ([EksVolume], False),
    }


class EksProperties(AWSProperty):
    """
    `EksProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-eksproperties.html>`__
    """

    props: PropsDictType = {
        "PodProperties": (PodProperties, False),
    }


class MultiNodeContainerProperties(AWSProperty):
    """
    `MultiNodeContainerProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-multinodecontainerproperties.html>`__
    """

    props: PropsDictType = {
        "Command": ([str], False),
        "Environment": ([Environment], False),
        "EphemeralStorage": (EphemeralStorage, False),
        "ExecutionRoleArn": (str, False),
        "Image": (str, True),
        "InstanceType": (str, False),
        "JobRoleArn": (str, False),
        "LinuxParameters": (LinuxParameters, False),
        "LogConfiguration": (LogConfiguration, False),
        "Memory": (integer, False),
        "MountPoints": ([MountPoints], False),
        "Privileged": (boolean, False),
        "ReadonlyRootFilesystem": (boolean, False),
        "RepositoryCredentials": (RepositoryCredentials, False),
        "ResourceRequirements": ([ResourceRequirement], False),
        "RuntimePlatform": (RuntimePlatform, False),
        "Secrets": ([Secret], False),
        "Ulimits": ([Ulimit], False),
        "User": (str, False),
        "Vcpus": (integer, False),
        "Volumes": ([Volumes], False),
    }


class MultiNodeEcsTaskProperties(AWSProperty):
    """
    `MultiNodeEcsTaskProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-multinodeecstaskproperties.html>`__
    """

    props: PropsDictType = {
        "Containers": ([TaskContainerProperties], False),
        "ExecutionRoleArn": (str, False),
        "IpcMode": (str, False),
        "PidMode": (str, False),
        "TaskRoleArn": (str, False),
        "Volumes": ([Volumes], False),
    }


class MultiNodeEcsProperties(AWSProperty):
    """
    `MultiNodeEcsProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-multinodeecsproperties.html>`__
    """

    props: PropsDictType = {
        "TaskProperties": ([MultiNodeEcsTaskProperties], True),
    }


class NodeRangeProperty(AWSProperty):
    """
    `NodeRangeProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-noderangeproperty.html>`__
    """

    props: PropsDictType = {
        "ConsumableResourceProperties": (ConsumableResourceProperties, False),
        "Container": (MultiNodeContainerProperties, False),
        "EcsProperties": (MultiNodeEcsProperties, False),
        "EksProperties": (EksProperties, False),
        "InstanceTypes": ([str], False),
        "TargetNodes": (str, True),
    }


class NodeProperties(AWSProperty):
    """
    `NodeProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-nodeproperties.html>`__
    """

    props: PropsDictType = {
        "MainNode": (integer, True),
        "NodeRangeProperties": ([NodeRangeProperty], True),
        "NumNodes": (integer, True),
    }


class EvaluateOnExit(AWSProperty):
    """
    `EvaluateOnExit <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-evaluateonexit.html>`__
    """

    props: PropsDictType = {
        "Action": (str, True),
        "OnExitCode": (str, False),
        "OnReason": (str, False),
        "OnStatusReason": (str, False),
    }


class RetryStrategy(AWSProperty):
    """
    `RetryStrategy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-retrystrategy.html>`__
    """

    props: PropsDictType = {
        "Attempts": (integer, False),
        "EvaluateOnExit": ([EvaluateOnExit], False),
    }


class Timeout(AWSProperty):
    """
    `Timeout <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobdefinition-jobtimeout.html>`__
    """

    props: PropsDictType = {
        "AttemptDurationSeconds": (integer, False),
    }


class JobDefinition(AWSObject):
    """
    `JobDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-batch-jobdefinition.html>`__
    """

    resource_type = "AWS::Batch::JobDefinition"

    props: PropsDictType = {
        "ConsumableResourceProperties": (ConsumableResourceProperties, False),
        "ContainerProperties": (ContainerProperties, False),
        "EcsProperties": (EcsProperties, False),
        "EksProperties": (EksProperties, False),
        "JobDefinitionName": (str, False),
        "NodeProperties": (NodeProperties, False),
        "Parameters": (dict, False),
        "PlatformCapabilities": ([str], False),
        "PropagateTags": (boolean, False),
        "RetryStrategy": (RetryStrategy, False),
        "SchedulingPriority": (integer, False),
        "Tags": (dict, False),
        "Timeout": (Timeout, False),
        "Type": (str, True),
    }


class ComputeEnvironmentOrder(AWSProperty):
    """
    `ComputeEnvironmentOrder <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobqueue-computeenvironmentorder.html>`__
    """

    props: PropsDictType = {
        "ComputeEnvironment": (str, True),
        "Order": (integer, True),
    }


class JobStateTimeLimitAction(AWSProperty):
    """
    `JobStateTimeLimitAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-jobqueue-jobstatetimelimitaction.html>`__
    """

    props: PropsDictType = {
        "Action": (str, True),
        "MaxTimeSeconds": (integer, True),
        "Reason": (str, True),
        "State": (str, True),
    }


class JobQueue(AWSObject):
    """
    `JobQueue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-batch-jobqueue.html>`__
    """

    resource_type = "AWS::Batch::JobQueue"

    props: PropsDictType = {
        "ComputeEnvironmentOrder": ([ComputeEnvironmentOrder], True),
        "JobQueueName": (str, False),
        "JobStateTimeLimitActions": ([JobStateTimeLimitAction], False),
        "Priority": (integer, True),
        "SchedulingPolicyArn": (str, False),
        "State": (validate_queue_state, False),
        "Tags": (dict, False),
    }


class ShareAttributes(AWSProperty):
    """
    `ShareAttributes <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-schedulingpolicy-shareattributes.html>`__
    """

    props: PropsDictType = {
        "ShareIdentifier": (str, False),
        "WeightFactor": (double, False),
    }


class FairsharePolicy(AWSProperty):
    """
    `FairsharePolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-batch-schedulingpolicy-fairsharepolicy.html>`__
    """

    props: PropsDictType = {
        "ComputeReservation": (double, False),
        "ShareDecaySeconds": (double, False),
        "ShareDistribution": ([ShareAttributes], False),
    }


class SchedulingPolicy(AWSObject):
    """
    `SchedulingPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-batch-schedulingpolicy.html>`__
    """

    resource_type = "AWS::Batch::SchedulingPolicy"

    props: PropsDictType = {
        "FairsharePolicy": (FairsharePolicy, False),
        "Name": (str, False),
        "Tags": (dict, False),
    }
