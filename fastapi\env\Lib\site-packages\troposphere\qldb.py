# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean


class Ledger(AWSObject):
    """
    `Ledger <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-qldb-ledger.html>`__
    """

    resource_type = "AWS::QLDB::Ledger"

    props: PropsDictType = {
        "DeletionProtection": (boolean, False),
        "KmsKey": (str, False),
        "Name": (str, False),
        "PermissionsMode": (str, True),
        "Tags": (Tags, False),
    }


class KinesisConfiguration(AWSProperty):
    """
    `KinesisConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qldb-stream-kinesisconfiguration.html>`__
    """

    props: PropsDictType = {
        "AggregationEnabled": (boolean, False),
        "StreamArn": (str, False),
    }


class Stream(AWSObject):
    """
    `Stream <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-qldb-stream.html>`__
    """

    resource_type = "AWS::QLDB::Stream"

    props: PropsDictType = {
        "ExclusiveEndTime": (str, False),
        "InclusiveStartTime": (str, True),
        "KinesisConfiguration": (KinesisConfiguration, True),
        "LedgerName": (str, True),
        "RoleArn": (str, True),
        "StreamName": (str, True),
        "Tags": (Tags, False),
    }
