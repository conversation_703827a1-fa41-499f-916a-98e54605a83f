# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags


class EncryptionConfig(AWSProperty):
    """
    `EncryptionConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-forecast-dataset-encryptionconfig.html>`__
    """

    props: PropsDictType = {
        "KmsKeyArn": (str, False),
        "RoleArn": (str, False),
    }


class AttributesItems(AWSProperty):
    """
    `AttributesItems <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-forecast-dataset-attributesitems.html>`__
    """

    props: PropsDictType = {
        "AttributeName": (str, False),
        "AttributeType": (str, False),
    }


class Schema(AWSProperty):
    """
    `Schema <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-forecast-dataset-schema.html>`__
    """

    props: PropsDictType = {
        "Attributes": ([AttributesItems], False),
    }


class TagsItems(AWSProperty):
    """
    `TagsItems <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-forecast-dataset-tagsitems.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (str, True),
    }


class Dataset(AWSObject):
    """
    `Dataset <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-forecast-dataset.html>`__
    """

    resource_type = "AWS::Forecast::Dataset"

    props: PropsDictType = {
        "DataFrequency": (str, False),
        "DatasetName": (str, True),
        "DatasetType": (str, True),
        "Domain": (str, True),
        "EncryptionConfig": (EncryptionConfig, False),
        "Schema": (Schema, True),
        "Tags": ([TagsItems], False),
    }


class DatasetGroup(AWSObject):
    """
    `DatasetGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-forecast-datasetgroup.html>`__
    """

    resource_type = "AWS::Forecast::DatasetGroup"

    props: PropsDictType = {
        "DatasetArns": ([str], False),
        "DatasetGroupName": (str, True),
        "Domain": (str, True),
        "Tags": (Tags, False),
    }
