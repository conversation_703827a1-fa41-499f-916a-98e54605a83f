# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, PropsDictType, Tags
from .validators import boolean


class Permission(AWSObject):
    """
    `Permission <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ram-permission.html>`__
    """

    resource_type = "AWS::RAM::Permission"

    props: PropsDictType = {
        "Name": (str, True),
        "PolicyTemplate": (dict, True),
        "ResourceType": (str, True),
        "Tags": (Tags, False),
    }


class ResourceShare(AWSObject):
    """
    `ResourceShare <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ram-resourceshare.html>`__
    """

    resource_type = "AWS::RAM::ResourceShare"

    props: PropsDictType = {
        "AllowExternalPrincipals": (boolean, False),
        "Name": (str, True),
        "PermissionArns": ([str], False),
        "Principals": ([str], False),
        "ResourceArns": ([str], False),
        "Sources": ([str], False),
        "Tags": (Tags, False),
    }
