# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer


class ServerlessV2ScalingConfiguration(AWSProperty):
    """
    `ServerlessV2ScalingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-docdb-dbcluster-serverlessv2scalingconfiguration.html>`__
    """

    props: PropsDictType = {
        "MaxCapacity": (double, True),
        "MinCapacity": (double, True),
    }


class DBCluster(AWSObject):
    """
    `DBCluster <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-docdb-dbcluster.html>`__
    """

    resource_type = "AWS::DocDB::DBCluster"

    props: PropsDictType = {
        "AvailabilityZones": ([str], False),
        "BackupRetentionPeriod": (integer, False),
        "CopyTagsToSnapshot": (boolean, False),
        "DBClusterIdentifier": (str, False),
        "DBClusterParameterGroupName": (str, False),
        "DBSubnetGroupName": (str, False),
        "DeletionProtection": (boolean, False),
        "EnableCloudwatchLogsExports": ([str], False),
        "EngineVersion": (str, False),
        "KmsKeyId": (str, False),
        "ManageMasterUserPassword": (boolean, False),
        "MasterUserPassword": (str, False),
        "MasterUserSecretKmsKeyId": (str, False),
        "MasterUsername": (str, False),
        "Port": (integer, False),
        "PreferredBackupWindow": (str, False),
        "PreferredMaintenanceWindow": (str, False),
        "RestoreToTime": (str, False),
        "RestoreType": (str, False),
        "RotateMasterUserPassword": (boolean, False),
        "ServerlessV2ScalingConfiguration": (ServerlessV2ScalingConfiguration, False),
        "SnapshotIdentifier": (str, False),
        "SourceDBClusterIdentifier": (str, False),
        "StorageEncrypted": (boolean, False),
        "StorageType": (str, False),
        "Tags": (Tags, False),
        "UseLatestRestorableTime": (boolean, False),
        "VpcSecurityGroupIds": ([str], False),
    }


class DBClusterParameterGroup(AWSObject):
    """
    `DBClusterParameterGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-docdb-dbclusterparametergroup.html>`__
    """

    resource_type = "AWS::DocDB::DBClusterParameterGroup"

    props: PropsDictType = {
        "Description": (str, True),
        "Family": (str, True),
        "Name": (str, False),
        "Parameters": (dict, True),
        "Tags": (Tags, False),
    }


class DBInstance(AWSObject):
    """
    `DBInstance <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-docdb-dbinstance.html>`__
    """

    resource_type = "AWS::DocDB::DBInstance"

    props: PropsDictType = {
        "AutoMinorVersionUpgrade": (boolean, False),
        "AvailabilityZone": (str, False),
        "CACertificateIdentifier": (str, False),
        "CertificateRotationRestart": (boolean, False),
        "DBClusterIdentifier": (str, True),
        "DBInstanceClass": (str, True),
        "DBInstanceIdentifier": (str, False),
        "EnablePerformanceInsights": (boolean, False),
        "PreferredMaintenanceWindow": (str, False),
        "Tags": (Tags, False),
    }


class DBSubnetGroup(AWSObject):
    """
    `DBSubnetGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-docdb-dbsubnetgroup.html>`__
    """

    resource_type = "AWS::DocDB::DBSubnetGroup"

    props: PropsDictType = {
        "DBSubnetGroupDescription": (str, True),
        "DBSubnetGroupName": (str, False),
        "SubnetIds": ([str], True),
        "Tags": (Tags, False),
    }


class EventSubscription(AWSObject):
    """
    `EventSubscription <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-docdb-eventsubscription.html>`__
    """

    resource_type = "AWS::DocDB::EventSubscription"

    props: PropsDictType = {
        "Enabled": (boolean, False),
        "EventCategories": ([str], False),
        "SnsTopicArn": (str, True),
        "SourceIds": ([str], False),
        "SourceType": (str, False),
        "SubscriptionName": (str, False),
    }
