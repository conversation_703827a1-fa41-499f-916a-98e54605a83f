# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer
from .validators.ec2 import NO_DEVICE  # noqa: F401
from .validators.ec2 import PERMISSION_EIP_ASSOCIATE  # noqa: F401
from .validators.ec2 import PERMISSION_INSTANCE_ATTACH  # noqa: F401
from .validators.ec2 import Ipv6Addresses  # noqa: F401
from .validators.ec2 import Tag  # noqa: F401
from .validators.ec2 import (
    instance_tenancy,
    policytypes,
    validate_clientvpnendpoint_selfserviceportal,
    validate_clientvpnendpoint_vpnport,
    validate_elasticinferenceaccelerator_type,
    validate_int_to_str,
    validate_network_acl_entry,
    validate_network_port,
    validate_networkaclentry_rulenumber,
    validate_placement_spread_level,
    validate_placement_strategy,
    validate_route,
    validate_security_group_egress,
    validate_security_group_ingress,
    validate_spot_fleet_request_config_data,
    validate_subnet,
    validate_tags_or_list,
    validate_vpn_connection,
    vpc_endpoint_type,
    vpn_pre_shared_key,
    vpn_tunnel_inside_cidr,
)


class TagSpecifications(AWSProperty):
    """
    `TagSpecifications <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-capacityreservation-tagspecification.html>`__
    """

    props: PropsDictType = {
        "ResourceType": (str, False),
        "Tags": (validate_tags_or_list, False),
    }


class CapacityReservation(AWSObject):
    """
    `CapacityReservation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-capacityreservation.html>`__
    """

    resource_type = "AWS::EC2::CapacityReservation"

    props: PropsDictType = {
        "AvailabilityZone": (str, False),
        "AvailabilityZoneId": (str, False),
        "EbsOptimized": (boolean, False),
        "EndDate": (str, False),
        "EndDateType": (str, False),
        "EphemeralStorage": (boolean, False),
        "InstanceCount": (integer, True),
        "InstanceMatchCriteria": (str, False),
        "InstancePlatform": (str, True),
        "InstanceType": (str, True),
        "OutPostArn": (str, False),
        "PlacementGroupArn": (str, False),
        "TagSpecifications": ([TagSpecifications], False),
        "Tenancy": (str, False),
        "UnusedReservationBillingOwnerId": (str, False),
    }


class InstanceTypeSpecification(AWSProperty):
    """
    `InstanceTypeSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-capacityreservationfleet-instancetypespecification.html>`__
    """

    props: PropsDictType = {
        "AvailabilityZone": (str, False),
        "AvailabilityZoneId": (str, False),
        "EbsOptimized": (boolean, False),
        "InstancePlatform": (str, False),
        "InstanceType": (str, False),
        "Priority": (integer, False),
        "Weight": (double, False),
    }


class CapacityReservationFleet(AWSObject):
    """
    `CapacityReservationFleet <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-capacityreservationfleet.html>`__
    """

    resource_type = "AWS::EC2::CapacityReservationFleet"

    props: PropsDictType = {
        "AllocationStrategy": (str, False),
        "EndDate": (str, False),
        "InstanceMatchCriteria": (str, False),
        "InstanceTypeSpecifications": ([InstanceTypeSpecification], False),
        "NoRemoveEndDate": (boolean, False),
        "RemoveEndDate": (boolean, False),
        "TagSpecifications": ([TagSpecifications], False),
        "Tenancy": (str, False),
        "TotalTargetCapacity": (integer, False),
    }


class CarrierGateway(AWSObject):
    """
    `CarrierGateway <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-carriergateway.html>`__
    """

    resource_type = "AWS::EC2::CarrierGateway"

    props: PropsDictType = {
        "Tags": (Tags, False),
        "VpcId": (str, True),
    }


class ClientVpnAuthorizationRule(AWSObject):
    """
    `ClientVpnAuthorizationRule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-clientvpnauthorizationrule.html>`__
    """

    resource_type = "AWS::EC2::ClientVpnAuthorizationRule"

    props: PropsDictType = {
        "AccessGroupId": (str, False),
        "AuthorizeAllGroups": (boolean, False),
        "ClientVpnEndpointId": (str, True),
        "Description": (str, False),
        "TargetNetworkCidr": (str, True),
    }


class CertificateAuthenticationRequest(AWSProperty):
    """
    `CertificateAuthenticationRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-clientvpnendpoint-certificateauthenticationrequest.html>`__
    """

    props: PropsDictType = {
        "ClientRootCertificateChainArn": (str, True),
    }


class DirectoryServiceAuthenticationRequest(AWSProperty):
    """
    `DirectoryServiceAuthenticationRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-clientvpnendpoint-directoryserviceauthenticationrequest.html>`__
    """

    props: PropsDictType = {
        "DirectoryId": (str, True),
    }


class FederatedAuthenticationRequest(AWSProperty):
    """
    `FederatedAuthenticationRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-clientvpnendpoint-federatedauthenticationrequest.html>`__
    """

    props: PropsDictType = {
        "SAMLProviderArn": (str, True),
        "SelfServiceSAMLProviderArn": (str, False),
    }


class ClientAuthenticationRequest(AWSProperty):
    """
    `ClientAuthenticationRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-clientvpnendpoint-clientauthenticationrequest.html>`__
    """

    props: PropsDictType = {
        "ActiveDirectory": (DirectoryServiceAuthenticationRequest, False),
        "FederatedAuthentication": (FederatedAuthenticationRequest, False),
        "MutualAuthentication": (CertificateAuthenticationRequest, False),
        "Type": (str, True),
    }


class ClientConnectOptions(AWSProperty):
    """
    `ClientConnectOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-clientvpnendpoint-clientconnectoptions.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, True),
        "LambdaFunctionArn": (str, False),
    }


class ClientLoginBannerOptions(AWSProperty):
    """
    `ClientLoginBannerOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-clientvpnendpoint-clientloginbanneroptions.html>`__
    """

    props: PropsDictType = {
        "BannerText": (str, False),
        "Enabled": (boolean, True),
    }


class ConnectionLogOptions(AWSProperty):
    """
    `ConnectionLogOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-clientvpnendpoint-connectionlogoptions.html>`__
    """

    props: PropsDictType = {
        "CloudwatchLogGroup": (str, False),
        "CloudwatchLogStream": (str, False),
        "Enabled": (boolean, True),
    }


class ClientVpnEndpoint(AWSObject):
    """
    `ClientVpnEndpoint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-clientvpnendpoint.html>`__
    """

    resource_type = "AWS::EC2::ClientVpnEndpoint"

    props: PropsDictType = {
        "AuthenticationOptions": ([ClientAuthenticationRequest], True),
        "ClientCidrBlock": (str, True),
        "ClientConnectOptions": (ClientConnectOptions, False),
        "ClientLoginBannerOptions": (ClientLoginBannerOptions, False),
        "ConnectionLogOptions": (ConnectionLogOptions, True),
        "Description": (str, False),
        "DisconnectOnSessionTimeout": (boolean, False),
        "DnsServers": ([str], False),
        "SecurityGroupIds": ([str], False),
        "SelfServicePortal": (validate_clientvpnendpoint_selfserviceportal, False),
        "ServerCertificateArn": (str, True),
        "SessionTimeoutHours": (integer, False),
        "SplitTunnel": (boolean, False),
        "TagSpecifications": ([TagSpecifications], False),
        "TransportProtocol": (str, False),
        "VpcId": (str, False),
        "VpnPort": (validate_clientvpnendpoint_vpnport, False),
    }


class ClientVpnRoute(AWSObject):
    """
    `ClientVpnRoute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-clientvpnroute.html>`__
    """

    resource_type = "AWS::EC2::ClientVpnRoute"

    props: PropsDictType = {
        "ClientVpnEndpointId": (str, True),
        "Description": (str, False),
        "DestinationCidrBlock": (str, True),
        "TargetVpcSubnetId": (str, True),
    }


class ClientVpnTargetNetworkAssociation(AWSObject):
    """
    `ClientVpnTargetNetworkAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-clientvpntargetnetworkassociation.html>`__
    """

    resource_type = "AWS::EC2::ClientVpnTargetNetworkAssociation"

    props: PropsDictType = {
        "ClientVpnEndpointId": (str, True),
        "SubnetId": (str, True),
    }


class CustomerGateway(AWSObject):
    """
    `CustomerGateway <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-customergateway.html>`__
    """

    resource_type = "AWS::EC2::CustomerGateway"

    props: PropsDictType = {
        "BgpAsn": (integer, False),
        "BgpAsnExtended": (double, False),
        "CertificateArn": (str, False),
        "DeviceName": (str, False),
        "IpAddress": (str, True),
        "Tags": (validate_tags_or_list, False),
        "Type": (str, True),
    }


class DHCPOptions(AWSObject):
    """
    `DHCPOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-dhcpoptions.html>`__
    """

    resource_type = "AWS::EC2::DHCPOptions"

    props: PropsDictType = {
        "DomainName": (str, False),
        "DomainNameServers": ([str], False),
        "Ipv6AddressPreferredLeaseTime": (integer, False),
        "NetbiosNameServers": ([str], False),
        "NetbiosNodeType": (integer, False),
        "NtpServers": ([str], False),
        "Tags": (validate_tags_or_list, False),
    }


class AcceleratorCountRequest(AWSProperty):
    """
    `AcceleratorCountRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-acceleratorcountrequest.html>`__
    """

    props: PropsDictType = {
        "Max": (integer, False),
        "Min": (integer, False),
    }


class AcceleratorTotalMemoryMiBRequest(AWSProperty):
    """
    `AcceleratorTotalMemoryMiBRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-acceleratortotalmemorymibrequest.html>`__
    """

    props: PropsDictType = {
        "Max": (integer, False),
        "Min": (integer, False),
    }


class BaselineEbsBandwidthMbpsRequest(AWSProperty):
    """
    `BaselineEbsBandwidthMbpsRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-baselineebsbandwidthmbpsrequest.html>`__
    """

    props: PropsDictType = {
        "Max": (integer, False),
        "Min": (integer, False),
    }


class PerformanceFactorReferenceRequest(AWSProperty):
    """
    `PerformanceFactorReferenceRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-performancefactorreferencerequest.html>`__
    """

    props: PropsDictType = {
        "InstanceFamily": (str, False),
    }


class CpuPerformanceFactorRequest(AWSProperty):
    """
    `CpuPerformanceFactorRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-cpuperformancefactorrequest.html>`__
    """

    props: PropsDictType = {
        "References": ([PerformanceFactorReferenceRequest], False),
    }


class BaselinePerformanceFactorsRequest(AWSProperty):
    """
    `BaselinePerformanceFactorsRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-baselineperformancefactorsrequest.html>`__
    """

    props: PropsDictType = {
        "Cpu": (CpuPerformanceFactorRequest, False),
    }


class MemoryGiBPerVCpuRequest(AWSProperty):
    """
    `MemoryGiBPerVCpuRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-memorygibpervcpurequest.html>`__
    """

    props: PropsDictType = {
        "Max": (double, False),
        "Min": (double, False),
    }


class MemoryMiBRequest(AWSProperty):
    """
    `MemoryMiBRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-memorymibrequest.html>`__
    """

    props: PropsDictType = {
        "Max": (integer, False),
        "Min": (integer, False),
    }


class NetworkBandwidthGbpsRequest(AWSProperty):
    """
    `NetworkBandwidthGbpsRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-networkbandwidthgbpsrequest.html>`__
    """

    props: PropsDictType = {
        "Max": (double, False),
        "Min": (double, False),
    }


class NetworkInterfaceCountRequest(AWSProperty):
    """
    `NetworkInterfaceCountRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-networkinterfacecountrequest.html>`__
    """

    props: PropsDictType = {
        "Max": (integer, False),
        "Min": (integer, False),
    }


class TotalLocalStorageGBRequest(AWSProperty):
    """
    `TotalLocalStorageGBRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-totallocalstoragegbrequest.html>`__
    """

    props: PropsDictType = {
        "Max": (double, False),
        "Min": (double, False),
    }


class VCpuCountRangeRequest(AWSProperty):
    """
    `VCpuCountRangeRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-vcpucountrangerequest.html>`__
    """

    props: PropsDictType = {
        "Max": (integer, False),
        "Min": (integer, False),
    }


class InstanceRequirementsRequest(AWSProperty):
    """
    `InstanceRequirementsRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-instancerequirementsrequest.html>`__
    """

    props: PropsDictType = {
        "AcceleratorCount": (AcceleratorCountRequest, False),
        "AcceleratorManufacturers": ([str], False),
        "AcceleratorNames": ([str], False),
        "AcceleratorTotalMemoryMiB": (AcceleratorTotalMemoryMiBRequest, False),
        "AcceleratorTypes": ([str], False),
        "AllowedInstanceTypes": ([str], False),
        "BareMetal": (str, False),
        "BaselineEbsBandwidthMbps": (BaselineEbsBandwidthMbpsRequest, False),
        "BaselinePerformanceFactors": (BaselinePerformanceFactorsRequest, False),
        "BurstablePerformance": (str, False),
        "CpuManufacturers": ([str], False),
        "ExcludedInstanceTypes": ([str], False),
        "InstanceGenerations": ([str], False),
        "LocalStorage": (str, False),
        "LocalStorageTypes": ([str], False),
        "MaxSpotPriceAsPercentageOfOptimalOnDemandPrice": (integer, False),
        "MemoryGiBPerVCpu": (MemoryGiBPerVCpuRequest, False),
        "MemoryMiB": (MemoryMiBRequest, False),
        "NetworkBandwidthGbps": (NetworkBandwidthGbpsRequest, False),
        "NetworkInterfaceCount": (NetworkInterfaceCountRequest, False),
        "OnDemandMaxPricePercentageOverLowestPrice": (integer, False),
        "RequireHibernateSupport": (boolean, False),
        "SpotMaxPricePercentageOverLowestPrice": (integer, False),
        "TotalLocalStorageGB": (TotalLocalStorageGBRequest, False),
        "VCpuCount": (VCpuCountRangeRequest, False),
    }


class Placement(AWSProperty):
    """
    `Placement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-placement.html>`__
    """

    props: PropsDictType = {
        "Affinity": (str, False),
        "AvailabilityZone": (str, False),
        "GroupId": (str, False),
        "GroupName": (str, False),
        "HostId": (str, False),
        "HostResourceGroupArn": (str, False),
        "PartitionNumber": (integer, False),
        "SpreadDomain": (str, False),
        "Tenancy": (str, False),
    }


class FleetLaunchTemplateOverridesRequest(AWSProperty):
    """
    `FleetLaunchTemplateOverridesRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-ec2fleet-fleetlaunchtemplateoverridesrequest.html>`__
    """

    props: PropsDictType = {
        "AvailabilityZone": (str, False),
        "InstanceRequirements": (InstanceRequirementsRequest, False),
        "InstanceType": (str, False),
        "MaxPrice": (str, False),
        "Placement": (Placement, False),
        "Priority": (double, False),
        "SubnetId": (str, False),
        "WeightedCapacity": (double, False),
    }


class FleetLaunchTemplateSpecificationRequest(AWSProperty):
    """
    `FleetLaunchTemplateSpecificationRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-ec2fleet-fleetlaunchtemplatespecificationrequest.html>`__
    """

    props: PropsDictType = {
        "LaunchTemplateId": (str, False),
        "LaunchTemplateName": (str, False),
        "Version": (str, True),
    }


class FleetLaunchTemplateConfigRequest(AWSProperty):
    """
    `FleetLaunchTemplateConfigRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-ec2fleet-fleetlaunchtemplateconfigrequest.html>`__
    """

    props: PropsDictType = {
        "LaunchTemplateSpecification": (FleetLaunchTemplateSpecificationRequest, False),
        "Overrides": ([FleetLaunchTemplateOverridesRequest], False),
    }


class CapacityReservationOptionsRequest(AWSProperty):
    """
    `CapacityReservationOptionsRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-ec2fleet-capacityreservationoptionsrequest.html>`__
    """

    props: PropsDictType = {
        "UsageStrategy": (str, False),
    }


class OnDemandOptionsRequest(AWSProperty):
    """
    `OnDemandOptionsRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-ec2fleet-ondemandoptionsrequest.html>`__
    """

    props: PropsDictType = {
        "AllocationStrategy": (str, False),
        "CapacityReservationOptions": (CapacityReservationOptionsRequest, False),
        "MaxTotalPrice": (str, False),
        "MinTargetCapacity": (integer, False),
        "SingleAvailabilityZone": (boolean, False),
        "SingleInstanceType": (boolean, False),
    }


class CapacityRebalance(AWSProperty):
    """
    `CapacityRebalance <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-ec2fleet-capacityrebalance.html>`__
    """

    props: PropsDictType = {
        "ReplacementStrategy": (str, False),
        "TerminationDelay": (integer, False),
    }


class MaintenanceStrategies(AWSProperty):
    """
    `MaintenanceStrategies <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-ec2fleet-maintenancestrategies.html>`__
    """

    props: PropsDictType = {
        "CapacityRebalance": (CapacityRebalance, False),
    }


class SpotOptionsRequest(AWSProperty):
    """
    `SpotOptionsRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-ec2fleet-spotoptionsrequest.html>`__
    """

    props: PropsDictType = {
        "AllocationStrategy": (str, False),
        "InstanceInterruptionBehavior": (str, False),
        "InstancePoolsToUseCount": (integer, False),
        "MaintenanceStrategies": (MaintenanceStrategies, False),
        "MaxTotalPrice": (str, False),
        "MinTargetCapacity": (integer, False),
        "SingleAvailabilityZone": (boolean, False),
        "SingleInstanceType": (boolean, False),
    }


class TargetCapacitySpecificationRequest(AWSProperty):
    """
    `TargetCapacitySpecificationRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-ec2fleet-targetcapacityspecificationrequest.html>`__
    """

    props: PropsDictType = {
        "DefaultTargetCapacityType": (str, False),
        "OnDemandTargetCapacity": (integer, False),
        "SpotTargetCapacity": (integer, False),
        "TargetCapacityUnitType": (str, False),
        "TotalTargetCapacity": (integer, True),
    }


class EC2Fleet(AWSObject):
    """
    `EC2Fleet <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-ec2fleet.html>`__
    """

    resource_type = "AWS::EC2::EC2Fleet"

    props: PropsDictType = {
        "Context": (str, False),
        "ExcessCapacityTerminationPolicy": (str, False),
        "LaunchTemplateConfigs": ([FleetLaunchTemplateConfigRequest], True),
        "OnDemandOptions": (OnDemandOptionsRequest, False),
        "ReplaceUnhealthyInstances": (boolean, False),
        "SpotOptions": (SpotOptionsRequest, False),
        "TagSpecifications": ([TagSpecifications], False),
        "TargetCapacitySpecification": (TargetCapacitySpecificationRequest, True),
        "TerminateInstancesWithExpiration": (boolean, False),
        "Type": (str, False),
        "ValidFrom": (str, False),
        "ValidUntil": (str, False),
    }


class EIP(AWSObject):
    """
    `EIP <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-eip.html>`__
    """

    resource_type = "AWS::EC2::EIP"

    props: PropsDictType = {
        "Address": (str, False),
        "Domain": (str, False),
        "InstanceId": (str, False),
        "IpamPoolId": (str, False),
        "NetworkBorderGroup": (str, False),
        "PublicIpv4Pool": (str, False),
        "Tags": (Tags, False),
        "TransferAddress": (str, False),
    }


class EIPAssociation(AWSObject):
    """
    `EIPAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-eipassociation.html>`__
    """

    resource_type = "AWS::EC2::EIPAssociation"

    props: PropsDictType = {
        "AllocationId": (str, False),
        "InstanceId": (str, False),
        "NetworkInterfaceId": (str, False),
        "PrivateIpAddress": (str, False),
    }


class EgressOnlyInternetGateway(AWSObject):
    """
    `EgressOnlyInternetGateway <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-egressonlyinternetgateway.html>`__
    """

    resource_type = "AWS::EC2::EgressOnlyInternetGateway"

    props: PropsDictType = {
        "VpcId": (str, True),
    }


class EnclaveCertificateIamRoleAssociation(AWSObject):
    """
    `EnclaveCertificateIamRoleAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-enclavecertificateiamroleassociation.html>`__
    """

    resource_type = "AWS::EC2::EnclaveCertificateIamRoleAssociation"

    props: PropsDictType = {
        "CertificateArn": (str, True),
        "RoleArn": (str, True),
    }


class DestinationOptions(AWSProperty):
    """
    `DestinationOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-flowlog-destinationoptions.html>`__
    """

    props: PropsDictType = {
        "FileFormat": (str, True),
        "HiveCompatiblePartitions": (boolean, True),
        "PerHourPartition": (boolean, True),
    }


class FlowLog(AWSObject):
    """
    `FlowLog <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-flowlog.html>`__
    """

    resource_type = "AWS::EC2::FlowLog"

    props: PropsDictType = {
        "DeliverCrossAccountRole": (str, False),
        "DeliverLogsPermissionArn": (str, False),
        "DestinationOptions": (DestinationOptions, False),
        "LogDestination": (str, False),
        "LogDestinationType": (str, False),
        "LogFormat": (str, False),
        "LogGroupName": (str, False),
        "MaxAggregationInterval": (integer, False),
        "ResourceId": (str, True),
        "ResourceType": (str, True),
        "Tags": (Tags, False),
        "TrafficType": (str, False),
    }


class GatewayRouteTableAssociation(AWSObject):
    """
    `GatewayRouteTableAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-gatewayroutetableassociation.html>`__
    """

    resource_type = "AWS::EC2::GatewayRouteTableAssociation"

    props: PropsDictType = {
        "GatewayId": (str, True),
        "RouteTableId": (str, True),
    }


class Host(AWSObject):
    """
    `Host <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-host.html>`__
    """

    resource_type = "AWS::EC2::Host"

    props: PropsDictType = {
        "AssetId": (str, False),
        "AutoPlacement": (str, False),
        "AvailabilityZone": (str, True),
        "HostMaintenance": (str, False),
        "HostRecovery": (str, False),
        "InstanceFamily": (str, False),
        "InstanceType": (str, False),
        "OutpostArn": (str, False),
    }


class IpamOperatingRegion(AWSProperty):
    """
    `IpamOperatingRegion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-ipamresourcediscovery-ipamoperatingregion.html>`__
    """

    props: PropsDictType = {
        "RegionName": (str, True),
    }


class IpamOrganizationalUnitExclusion(AWSProperty):
    """
    `IpamOrganizationalUnitExclusion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-ipam-ipamorganizationalunitexclusion.html>`__
    """

    props: PropsDictType = {
        "OrganizationsEntityPath": (str, True),
    }


class IPAM(AWSObject):
    """
    `IPAM <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-ipam.html>`__
    """

    resource_type = "AWS::EC2::IPAM"

    props: PropsDictType = {
        "DefaultResourceDiscoveryOrganizationalUnitExclusions": (
            [IpamOrganizationalUnitExclusion],
            False,
        ),
        "Description": (str, False),
        "EnablePrivateGua": (boolean, False),
        "OperatingRegions": ([IpamOperatingRegion], False),
        "Tags": (Tags, False),
        "Tier": (str, False),
    }


class IPAMAllocation(AWSObject):
    """
    `IPAMAllocation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-ipamallocation.html>`__
    """

    resource_type = "AWS::EC2::IPAMAllocation"

    props: PropsDictType = {
        "Cidr": (str, False),
        "Description": (str, False),
        "IpamPoolId": (str, True),
        "NetmaskLength": (integer, False),
    }


class ProvisionedCidr(AWSProperty):
    """
    `ProvisionedCidr <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-ipampool-provisionedcidr.html>`__
    """

    props: PropsDictType = {
        "Cidr": (str, True),
    }


class SourceResource(AWSProperty):
    """
    `SourceResource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-ipampool-sourceresource.html>`__
    """

    props: PropsDictType = {
        "ResourceId": (str, True),
        "ResourceOwner": (str, True),
        "ResourceRegion": (str, True),
        "ResourceType": (str, True),
    }


class IPAMPool(AWSObject):
    """
    `IPAMPool <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-ipampool.html>`__
    """

    resource_type = "AWS::EC2::IPAMPool"

    props: PropsDictType = {
        "AddressFamily": (str, True),
        "AllocationDefaultNetmaskLength": (integer, False),
        "AllocationMaxNetmaskLength": (integer, False),
        "AllocationMinNetmaskLength": (integer, False),
        "AllocationResourceTags": (Tags, False),
        "AutoImport": (boolean, False),
        "AwsService": (str, False),
        "Description": (str, False),
        "IpamScopeId": (str, True),
        "Locale": (str, False),
        "ProvisionedCidrs": ([ProvisionedCidr], False),
        "PublicIpSource": (str, False),
        "PubliclyAdvertisable": (boolean, False),
        "SourceIpamPoolId": (str, False),
        "SourceResource": (SourceResource, False),
        "Tags": (Tags, False),
    }


class IPAMPoolCidr(AWSObject):
    """
    `IPAMPoolCidr <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-ipampoolcidr.html>`__
    """

    resource_type = "AWS::EC2::IPAMPoolCidr"

    props: PropsDictType = {
        "Cidr": (str, False),
        "IpamPoolId": (str, True),
        "NetmaskLength": (integer, False),
    }


class IpamResourceDiscoveryOrganizationalUnitExclusion(AWSProperty):
    """
    `IpamResourceDiscoveryOrganizationalUnitExclusion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-ipamresourcediscovery-ipamresourcediscoveryorganizationalunitexclusion.html>`__
    """

    props: PropsDictType = {
        "OrganizationsEntityPath": (str, True),
    }


class IPAMResourceDiscovery(AWSObject):
    """
    `IPAMResourceDiscovery <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-ipamresourcediscovery.html>`__
    """

    resource_type = "AWS::EC2::IPAMResourceDiscovery"

    props: PropsDictType = {
        "Description": (str, False),
        "OperatingRegions": ([IpamOperatingRegion], False),
        "OrganizationalUnitExclusions": (
            [IpamResourceDiscoveryOrganizationalUnitExclusion],
            False,
        ),
        "Tags": (Tags, False),
    }


class IPAMResourceDiscoveryAssociation(AWSObject):
    """
    `IPAMResourceDiscoveryAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-ipamresourcediscoveryassociation.html>`__
    """

    resource_type = "AWS::EC2::IPAMResourceDiscoveryAssociation"

    props: PropsDictType = {
        "IpamId": (str, True),
        "IpamResourceDiscoveryId": (str, True),
        "Tags": (Tags, False),
    }


class IPAMScope(AWSObject):
    """
    `IPAMScope <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-ipamscope.html>`__
    """

    resource_type = "AWS::EC2::IPAMScope"

    props: PropsDictType = {
        "Description": (str, False),
        "IpamId": (str, True),
        "Tags": (Tags, False),
    }


class EBSBlockDevice(AWSProperty):
    """
    `EBSBlockDevice <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-ebs.html>`__
    """

    props: PropsDictType = {
        "DeleteOnTermination": (boolean, False),
        "Encrypted": (boolean, False),
        "Iops": (integer, False),
        "KmsKeyId": (str, False),
        "SnapshotId": (str, False),
        "Throughput": (integer, False),
        "VolumeSize": (integer, False),
        "VolumeType": (str, False),
    }


class BlockDeviceMapping(AWSProperty):
    """
    `BlockDeviceMapping <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-instance-blockdevicemapping.html>`__
    """

    props: PropsDictType = {
        "DeviceName": (str, True),
        "Ebs": (EBSBlockDevice, False),
        "NoDevice": (dict, False),
        "VirtualName": (str, False),
    }


class CpuOptions(AWSProperty):
    """
    `CpuOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-cpuoptions.html>`__
    """

    props: PropsDictType = {
        "AmdSevSnp": (str, False),
        "CoreCount": (integer, False),
        "ThreadsPerCore": (integer, False),
    }


class CreditSpecification(AWSProperty):
    """
    `CreditSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-instance-creditspecification.html>`__
    """

    props: PropsDictType = {
        "CPUCredits": (str, False),
    }


class ElasticGpuSpecification(AWSProperty):
    """
    `ElasticGpuSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-elasticgpuspecification.html>`__
    """

    props: PropsDictType = {
        "Type": (str, False),
    }


class ElasticInferenceAccelerator(AWSProperty):
    """
    `ElasticInferenceAccelerator <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-instance-elasticinferenceaccelerator.html>`__
    """

    props: PropsDictType = {
        "Count": (integer, False),
        "Type": (validate_elasticinferenceaccelerator_type, True),
    }


class EnclaveOptions(AWSProperty):
    """
    `EnclaveOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-enclaveoptions.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, False),
    }


class HibernationOptions(AWSProperty):
    """
    `HibernationOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-hibernationoptions.html>`__
    """

    props: PropsDictType = {
        "Configured": (boolean, False),
    }


class InstanceIpv6Address(AWSProperty):
    """
    `InstanceIpv6Address <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-instanceipv6address.html>`__
    """

    props: PropsDictType = {
        "Ipv6Address": (str, True),
    }


class LaunchTemplateSpecification(AWSProperty):
    """
    `LaunchTemplateSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-instance-launchtemplatespecification.html>`__
    """

    props: PropsDictType = {
        "LaunchTemplateId": (str, False),
        "LaunchTemplateName": (str, False),
        "Version": (str, True),
    }


class LicenseSpecification(AWSProperty):
    """
    `LicenseSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-licensespecification.html>`__
    """

    props: PropsDictType = {
        "LicenseConfigurationArn": (str, False),
    }


class EnaSrdUdpSpecification(AWSProperty):
    """
    `EnaSrdUdpSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-networkinterfaceattachment-enasrdudpspecification.html>`__
    """

    props: PropsDictType = {
        "EnaSrdUdpEnabled": (boolean, False),
    }


class EnaSrdSpecification(AWSProperty):
    """
    `EnaSrdSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-networkinterfaceattachment-enasrdspecification.html>`__
    """

    props: PropsDictType = {
        "EnaSrdEnabled": (boolean, False),
        "EnaSrdUdpSpecification": (EnaSrdUdpSpecification, False),
    }


class PrivateIpAddressSpecification(AWSProperty):
    """
    `PrivateIpAddressSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-privateipaddressspecification.html>`__
    """

    props: PropsDictType = {
        "Primary": (boolean, False),
        "PrivateIpAddress": (str, True),
    }


class NetworkInterfaceProperty(AWSProperty):
    """
    `NetworkInterfaceProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-instance-networkinterface.html>`__
    """

    props: PropsDictType = {
        "AssociateCarrierIpAddress": (boolean, False),
        "AssociatePublicIpAddress": (boolean, False),
        "DeleteOnTermination": (boolean, False),
        "Description": (str, False),
        "DeviceIndex": (validate_int_to_str, True),
        "EnaSrdSpecification": (EnaSrdSpecification, False),
        "GroupSet": ([str], False),
        "Ipv6AddressCount": (integer, False),
        "Ipv6Addresses": ([InstanceIpv6Address], False),
        "NetworkInterfaceId": (str, False),
        "PrivateIpAddress": (str, False),
        "PrivateIpAddresses": ([PrivateIpAddressSpecification], False),
        "SecondaryPrivateIpAddressCount": (integer, False),
        "SubnetId": (str, False),
    }


class PrivateDnsNameOptions(AWSProperty):
    """
    `PrivateDnsNameOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-privatednsnameoptions.html>`__
    """

    props: PropsDictType = {
        "EnableResourceNameDnsAAAARecord": (boolean, False),
        "EnableResourceNameDnsARecord": (boolean, False),
        "HostnameType": (str, False),
    }


class AssociationParameters(AWSProperty):
    """
    `AssociationParameters <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-instance-associationparameter.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": ([str], True),
    }


class SsmAssociations(AWSProperty):
    """
    `SsmAssociations <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-instance-ssmassociation.html>`__
    """

    props: PropsDictType = {
        "AssociationParameters": ([AssociationParameters], False),
        "DocumentName": (str, True),
    }


class Instance(AWSObject):
    """
    `Instance <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-instance.html>`__
    """

    resource_type = "AWS::EC2::Instance"

    props: PropsDictType = {
        "AdditionalInfo": (str, False),
        "Affinity": (str, False),
        "AvailabilityZone": (str, False),
        "BlockDeviceMappings": ([BlockDeviceMapping], False),
        "CpuOptions": (CpuOptions, False),
        "CreditSpecification": (CreditSpecification, False),
        "DisableApiTermination": (boolean, False),
        "EbsOptimized": (boolean, False),
        "ElasticGpuSpecifications": ([ElasticGpuSpecification], False),
        "ElasticInferenceAccelerators": ([ElasticInferenceAccelerator], False),
        "EnclaveOptions": (EnclaveOptions, False),
        "HibernationOptions": (HibernationOptions, False),
        "HostId": (str, False),
        "HostResourceGroupArn": (str, False),
        "IamInstanceProfile": (str, False),
        "ImageId": (str, False),
        "InstanceInitiatedShutdownBehavior": (str, False),
        "InstanceType": (str, False),
        "Ipv6AddressCount": (integer, False),
        "Ipv6Addresses": ([InstanceIpv6Address], False),
        "KernelId": (str, False),
        "KeyName": (str, False),
        "LaunchTemplate": (LaunchTemplateSpecification, False),
        "LicenseSpecifications": ([LicenseSpecification], False),
        "Monitoring": (boolean, False),
        "NetworkInterfaces": ([NetworkInterfaceProperty], False),
        "PlacementGroupName": (str, False),
        "PrivateDnsNameOptions": (PrivateDnsNameOptions, False),
        "PrivateIpAddress": (str, False),
        "PropagateTagsToVolumeOnCreation": (boolean, False),
        "RamdiskId": (str, False),
        "SecurityGroupIds": (list, False),
        "SecurityGroups": ([str], False),
        "SourceDestCheck": (boolean, False),
        "SsmAssociations": ([SsmAssociations], False),
        "SubnetId": (str, False),
        "Tags": (validate_tags_or_list, False),
        "Tenancy": (str, False),
        "UserData": (str, False),
        "Volumes": (list, False),
    }


class InstanceConnectEndpoint(AWSObject):
    """
    `InstanceConnectEndpoint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-instanceconnectendpoint.html>`__
    """

    resource_type = "AWS::EC2::InstanceConnectEndpoint"

    props: PropsDictType = {
        "ClientToken": (str, False),
        "PreserveClientIp": (boolean, False),
        "SecurityGroupIds": ([str], False),
        "SubnetId": (str, True),
        "Tags": (Tags, False),
    }


class InternetGateway(AWSObject):
    """
    `InternetGateway <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-internetgateway.html>`__
    """

    resource_type = "AWS::EC2::InternetGateway"

    props: PropsDictType = {
        "Tags": (validate_tags_or_list, False),
    }


class KeyPair(AWSObject):
    """
    `KeyPair <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-keypair.html>`__
    """

    resource_type = "AWS::EC2::KeyPair"

    props: PropsDictType = {
        "KeyFormat": (str, False),
        "KeyName": (str, True),
        "KeyType": (str, False),
        "PublicKeyMaterial": (str, False),
        "Tags": (Tags, False),
    }


class CapacityReservationTarget(AWSProperty):
    """
    `CapacityReservationTarget <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-capacityreservationtarget.html>`__
    """

    props: PropsDictType = {
        "CapacityReservationId": (str, False),
        "CapacityReservationResourceGroupArn": (str, False),
    }


class CapacityReservationSpecification(AWSProperty):
    """
    `CapacityReservationSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-capacityreservationspecification.html>`__
    """

    props: PropsDictType = {
        "CapacityReservationPreference": (str, False),
        "CapacityReservationTarget": (CapacityReservationTarget, False),
    }


class IamInstanceProfile(AWSProperty):
    """
    `IamInstanceProfile <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-iaminstanceprofile.html>`__
    """

    props: PropsDictType = {
        "Arn": (str, False),
        "Name": (str, False),
    }


class SpotOptions(AWSProperty):
    """
    `SpotOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-spotoptions.html>`__
    """

    props: PropsDictType = {
        "BlockDurationMinutes": (integer, False),
        "InstanceInterruptionBehavior": (str, False),
        "MaxPrice": (str, False),
        "SpotInstanceType": (str, False),
        "ValidUntil": (str, False),
    }


class InstanceMarketOptions(AWSProperty):
    """
    `InstanceMarketOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-instancemarketoptions.html>`__
    """

    props: PropsDictType = {
        "MarketType": (str, False),
        "SpotOptions": (SpotOptions, False),
    }


class AcceleratorCount(AWSProperty):
    """
    `AcceleratorCount <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-acceleratorcount.html>`__
    """

    props: PropsDictType = {
        "Max": (integer, False),
        "Min": (integer, False),
    }


class AcceleratorTotalMemoryMiB(AWSProperty):
    """
    `AcceleratorTotalMemoryMiB <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-acceleratortotalmemorymib.html>`__
    """

    props: PropsDictType = {
        "Max": (integer, False),
        "Min": (integer, False),
    }


class BaselineEbsBandwidthMbps(AWSProperty):
    """
    `BaselineEbsBandwidthMbps <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-baselineebsbandwidthmbps.html>`__
    """

    props: PropsDictType = {
        "Max": (integer, False),
        "Min": (integer, False),
    }


class Reference(AWSProperty):
    """
    `Reference <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-reference.html>`__
    """

    props: PropsDictType = {
        "InstanceFamily": (str, False),
    }


class Cpu(AWSProperty):
    """
    `Cpu <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-cpu.html>`__
    """

    props: PropsDictType = {
        "References": ([Reference], False),
    }


class BaselinePerformanceFactors(AWSProperty):
    """
    `BaselinePerformanceFactors <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-baselineperformancefactors.html>`__
    """

    props: PropsDictType = {
        "Cpu": (Cpu, False),
    }


class MemoryGiBPerVCpu(AWSProperty):
    """
    `MemoryGiBPerVCpu <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-memorygibpervcpu.html>`__
    """

    props: PropsDictType = {
        "Max": (double, False),
        "Min": (double, False),
    }


class MemoryMiB(AWSProperty):
    """
    `MemoryMiB <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-memorymib.html>`__
    """

    props: PropsDictType = {
        "Max": (integer, False),
        "Min": (integer, False),
    }


class NetworkBandwidthGbps(AWSProperty):
    """
    `NetworkBandwidthGbps <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-networkbandwidthgbps.html>`__
    """

    props: PropsDictType = {
        "Max": (double, False),
        "Min": (double, False),
    }


class NetworkInterfaceCount(AWSProperty):
    """
    `NetworkInterfaceCount <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-networkinterfacecount.html>`__
    """

    props: PropsDictType = {
        "Max": (integer, False),
        "Min": (integer, False),
    }


class TotalLocalStorageGB(AWSProperty):
    """
    `TotalLocalStorageGB <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-totallocalstoragegb.html>`__
    """

    props: PropsDictType = {
        "Max": (double, False),
        "Min": (double, False),
    }


class VCpuCount(AWSProperty):
    """
    `VCpuCount <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-vcpucount.html>`__
    """

    props: PropsDictType = {
        "Max": (integer, False),
        "Min": (integer, False),
    }


class InstanceRequirements(AWSProperty):
    """
    `InstanceRequirements <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-instancerequirements.html>`__
    """

    props: PropsDictType = {
        "AcceleratorCount": (AcceleratorCount, False),
        "AcceleratorManufacturers": ([str], False),
        "AcceleratorNames": ([str], False),
        "AcceleratorTotalMemoryMiB": (AcceleratorTotalMemoryMiB, False),
        "AcceleratorTypes": ([str], False),
        "AllowedInstanceTypes": ([str], False),
        "BareMetal": (str, False),
        "BaselineEbsBandwidthMbps": (BaselineEbsBandwidthMbps, False),
        "BaselinePerformanceFactors": (BaselinePerformanceFactors, False),
        "BurstablePerformance": (str, False),
        "CpuManufacturers": ([str], False),
        "ExcludedInstanceTypes": ([str], False),
        "InstanceGenerations": ([str], False),
        "LocalStorage": (str, False),
        "LocalStorageTypes": ([str], False),
        "MaxSpotPriceAsPercentageOfOptimalOnDemandPrice": (integer, False),
        "MemoryGiBPerVCpu": (MemoryGiBPerVCpu, False),
        "MemoryMiB": (MemoryMiB, False),
        "NetworkBandwidthGbps": (NetworkBandwidthGbps, False),
        "NetworkInterfaceCount": (NetworkInterfaceCount, False),
        "OnDemandMaxPricePercentageOverLowestPrice": (integer, False),
        "RequireHibernateSupport": (boolean, False),
        "SpotMaxPricePercentageOverLowestPrice": (integer, False),
        "TotalLocalStorageGB": (TotalLocalStorageGB, False),
        "VCpuCount": (VCpuCount, False),
    }


class LaunchTemplateBlockDeviceMapping(AWSProperty):
    """
    `LaunchTemplateBlockDeviceMapping <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-blockdevicemapping.html>`__
    """

    props: PropsDictType = {
        "DeviceName": (str, False),
        "Ebs": (EBSBlockDevice, False),
        "NoDevice": (str, False),
        "VirtualName": (str, False),
    }


class LaunchTemplateCreditSpecification(AWSProperty):
    """
    `LaunchTemplateCreditSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-creditspecification.html>`__
    """

    props: PropsDictType = {
        "CpuCredits": (str, False),
    }


class LaunchTemplateElasticInferenceAccelerator(AWSProperty):
    """
    `LaunchTemplateElasticInferenceAccelerator <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-launchtemplateelasticinferenceaccelerator.html>`__
    """

    props: PropsDictType = {
        "Count": (integer, False),
        "Type": (validate_elasticinferenceaccelerator_type, False),
    }


class MaintenanceOptions(AWSProperty):
    """
    `MaintenanceOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-maintenanceoptions.html>`__
    """

    props: PropsDictType = {
        "AutoRecovery": (str, False),
    }


class MetadataOptions(AWSProperty):
    """
    `MetadataOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-metadataoptions.html>`__
    """

    props: PropsDictType = {
        "HttpEndpoint": (str, False),
        "HttpProtocolIpv6": (str, False),
        "HttpPutResponseHopLimit": (integer, False),
        "HttpTokens": (str, False),
        "InstanceMetadataTags": (str, False),
    }


class Monitoring(AWSProperty):
    """
    `Monitoring <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-spotfleetmonitoring.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, False),
    }


class ConnectionTrackingSpecification(AWSProperty):
    """
    `ConnectionTrackingSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-networkinterface-connectiontrackingspecification.html>`__
    """

    props: PropsDictType = {
        "TcpEstablishedTimeout": (integer, False),
        "UdpStreamTimeout": (integer, False),
        "UdpTimeout": (integer, False),
    }


class Ipv4PrefixSpecification(AWSProperty):
    """
    `Ipv4PrefixSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-networkinterface-ipv4prefixspecification.html>`__
    """

    props: PropsDictType = {
        "Ipv4Prefix": (str, True),
    }


class Ipv6Add(AWSProperty):
    """
    `Ipv6Add <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-ipv6add.html>`__
    """

    props: PropsDictType = {
        "Ipv6Address": (str, False),
    }


class Ipv6PrefixSpecification(AWSProperty):
    """
    `Ipv6PrefixSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-networkinterface-ipv6prefixspecification.html>`__
    """

    props: PropsDictType = {
        "Ipv6Prefix": (str, True),
    }


class NetworkInterfaces(AWSProperty):
    """
    `NetworkInterfaces <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-networkinterface.html>`__
    """

    props: PropsDictType = {
        "AssociateCarrierIpAddress": (boolean, False),
        "AssociatePublicIpAddress": (boolean, False),
        "ConnectionTrackingSpecification": (ConnectionTrackingSpecification, False),
        "DeleteOnTermination": (boolean, False),
        "Description": (str, False),
        "DeviceIndex": (integer, False),
        "EnaSrdSpecification": (EnaSrdSpecification, False),
        "Groups": ([str], False),
        "InterfaceType": (str, False),
        "Ipv4PrefixCount": (integer, False),
        "Ipv4Prefixes": ([Ipv4PrefixSpecification], False),
        "Ipv6AddressCount": (integer, False),
        "Ipv6Addresses": ([Ipv6Add], False),
        "Ipv6PrefixCount": (integer, False),
        "Ipv6Prefixes": ([Ipv6PrefixSpecification], False),
        "NetworkCardIndex": (integer, False),
        "NetworkInterfaceId": (str, False),
        "PrimaryIpv6": (boolean, False),
        "PrivateIpAddress": (str, False),
        "PrivateIpAddresses": ([PrivateIpAddressSpecification], False),
        "SecondaryPrivateIpAddressCount": (integer, False),
        "SubnetId": (str, False),
    }


class NetworkPerformanceOptions(AWSProperty):
    """
    `NetworkPerformanceOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-networkperformanceoptions.html>`__
    """

    props: PropsDictType = {
        "BandwidthWeighting": (str, False),
    }


class LaunchTemplateData(AWSProperty):
    """
    `LaunchTemplateData <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-launchtemplate-launchtemplatedata.html>`__
    """

    props: PropsDictType = {
        "BlockDeviceMappings": ([LaunchTemplateBlockDeviceMapping], False),
        "CapacityReservationSpecification": (CapacityReservationSpecification, False),
        "CpuOptions": (CpuOptions, False),
        "CreditSpecification": (LaunchTemplateCreditSpecification, False),
        "DisableApiStop": (boolean, False),
        "DisableApiTermination": (boolean, False),
        "EbsOptimized": (boolean, False),
        "ElasticGpuSpecifications": ([ElasticGpuSpecification], False),
        "ElasticInferenceAccelerators": (
            [LaunchTemplateElasticInferenceAccelerator],
            False,
        ),
        "EnclaveOptions": (EnclaveOptions, False),
        "HibernationOptions": (HibernationOptions, False),
        "IamInstanceProfile": (IamInstanceProfile, False),
        "ImageId": (str, False),
        "InstanceInitiatedShutdownBehavior": (str, False),
        "InstanceMarketOptions": (InstanceMarketOptions, False),
        "InstanceRequirements": (InstanceRequirements, False),
        "InstanceType": (str, False),
        "KernelId": (str, False),
        "KeyName": (str, False),
        "LicenseSpecifications": ([LicenseSpecification], False),
        "MaintenanceOptions": (MaintenanceOptions, False),
        "MetadataOptions": (MetadataOptions, False),
        "Monitoring": (Monitoring, False),
        "NetworkInterfaces": ([NetworkInterfaces], False),
        "NetworkPerformanceOptions": (NetworkPerformanceOptions, False),
        "Placement": (Placement, False),
        "PrivateDnsNameOptions": (PrivateDnsNameOptions, False),
        "RamDiskId": (str, False),
        "SecurityGroupIds": ([str], False),
        "SecurityGroups": ([str], False),
        "TagSpecifications": ([TagSpecifications], False),
        "UserData": (str, False),
    }


class LaunchTemplate(AWSObject):
    """
    `LaunchTemplate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-launchtemplate.html>`__
    """

    resource_type = "AWS::EC2::LaunchTemplate"

    props: PropsDictType = {
        "LaunchTemplateData": (LaunchTemplateData, True),
        "LaunchTemplateName": (str, False),
        "TagSpecifications": ([TagSpecifications], False),
        "VersionDescription": (str, False),
    }


class LocalGatewayRoute(AWSObject):
    """
    `LocalGatewayRoute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-localgatewayroute.html>`__
    """

    resource_type = "AWS::EC2::LocalGatewayRoute"

    props: PropsDictType = {
        "DestinationCidrBlock": (str, True),
        "LocalGatewayRouteTableId": (str, True),
        "LocalGatewayVirtualInterfaceGroupId": (str, False),
        "NetworkInterfaceId": (str, False),
    }


class LocalGatewayRouteTable(AWSObject):
    """
    `LocalGatewayRouteTable <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-localgatewayroutetable.html>`__
    """

    resource_type = "AWS::EC2::LocalGatewayRouteTable"

    props: PropsDictType = {
        "LocalGatewayId": (str, True),
        "Mode": (str, False),
        "Tags": (Tags, False),
    }


class LocalGatewayRouteTableVPCAssociation(AWSObject):
    """
    `LocalGatewayRouteTableVPCAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-localgatewayroutetablevpcassociation.html>`__
    """

    resource_type = "AWS::EC2::LocalGatewayRouteTableVPCAssociation"

    props: PropsDictType = {
        "LocalGatewayRouteTableId": (str, True),
        "Tags": (validate_tags_or_list, False),
        "VpcId": (str, True),
    }


class LocalGatewayRouteTableVirtualInterfaceGroupAssociation(AWSObject):
    """
    `LocalGatewayRouteTableVirtualInterfaceGroupAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-localgatewayroutetablevirtualinterfacegroupassociation.html>`__
    """

    resource_type = "AWS::EC2::LocalGatewayRouteTableVirtualInterfaceGroupAssociation"

    props: PropsDictType = {
        "LocalGatewayRouteTableId": (str, True),
        "LocalGatewayVirtualInterfaceGroupId": (str, True),
        "Tags": (Tags, False),
    }


class NatGateway(AWSObject):
    """
    `NatGateway <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-natgateway.html>`__
    """

    resource_type = "AWS::EC2::NatGateway"

    props: PropsDictType = {
        "AllocationId": (str, False),
        "ConnectivityType": (str, False),
        "MaxDrainDurationSeconds": (integer, False),
        "PrivateIpAddress": (str, False),
        "SecondaryAllocationIds": ([str], False),
        "SecondaryPrivateIpAddressCount": (integer, False),
        "SecondaryPrivateIpAddresses": ([str], False),
        "SubnetId": (str, True),
        "Tags": (validate_tags_or_list, False),
    }


class NetworkAcl(AWSObject):
    """
    `NetworkAcl <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-networkacl.html>`__
    """

    resource_type = "AWS::EC2::NetworkAcl"

    props: PropsDictType = {
        "Tags": (validate_tags_or_list, False),
        "VpcId": (str, True),
    }


class ICMP(AWSProperty):
    """
    `ICMP <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-networkaclentry-icmp.html>`__
    """

    props: PropsDictType = {
        "Code": (integer, False),
        "Type": (integer, False),
    }


class PortRange(AWSProperty):
    """
    `PortRange <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-networkaclentry-portrange.html>`__
    """

    props: PropsDictType = {
        "From": (validate_network_port, False),
        "To": (validate_network_port, False),
    }


class NetworkAclEntry(AWSObject):
    """
    `NetworkAclEntry <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-networkaclentry.html>`__
    """

    resource_type = "AWS::EC2::NetworkAclEntry"

    props: PropsDictType = {
        "CidrBlock": (str, False),
        "Egress": (boolean, False),
        "Icmp": (ICMP, False),
        "Ipv6CidrBlock": (str, False),
        "NetworkAclId": (str, True),
        "PortRange": (PortRange, False),
        "Protocol": (validate_network_port, True),
        "RuleAction": (str, True),
        "RuleNumber": (validate_networkaclentry_rulenumber, True),
    }

    def validate(self):
        validate_network_acl_entry(self)


class PacketHeaderStatementRequest(AWSProperty):
    """
    `PacketHeaderStatementRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-networkinsightsaccessscope-packetheaderstatementrequest.html>`__
    """

    props: PropsDictType = {
        "DestinationAddresses": ([str], False),
        "DestinationPorts": ([str], False),
        "DestinationPrefixLists": ([str], False),
        "Protocols": ([str], False),
        "SourceAddresses": ([str], False),
        "SourcePorts": ([str], False),
        "SourcePrefixLists": ([str], False),
    }


class ResourceStatementRequest(AWSProperty):
    """
    `ResourceStatementRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-networkinsightsaccessscope-resourcestatementrequest.html>`__
    """

    props: PropsDictType = {
        "ResourceTypes": ([str], False),
        "Resources": ([str], False),
    }


class PathStatementRequest(AWSProperty):
    """
    `PathStatementRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-networkinsightsaccessscope-pathstatementrequest.html>`__
    """

    props: PropsDictType = {
        "PacketHeaderStatement": (PacketHeaderStatementRequest, False),
        "ResourceStatement": (ResourceStatementRequest, False),
    }


class ThroughResourcesStatementRequest(AWSProperty):
    """
    `ThroughResourcesStatementRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-networkinsightsaccessscope-throughresourcesstatementrequest.html>`__
    """

    props: PropsDictType = {
        "ResourceStatement": (ResourceStatementRequest, False),
    }


class AccessScopePathRequest(AWSProperty):
    """
    `AccessScopePathRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-networkinsightsaccessscope-accessscopepathrequest.html>`__
    """

    props: PropsDictType = {
        "Destination": (PathStatementRequest, False),
        "Source": (PathStatementRequest, False),
        "ThroughResources": ([ThroughResourcesStatementRequest], False),
    }


class NetworkInsightsAccessScope(AWSObject):
    """
    `NetworkInsightsAccessScope <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-networkinsightsaccessscope.html>`__
    """

    resource_type = "AWS::EC2::NetworkInsightsAccessScope"

    props: PropsDictType = {
        "ExcludePaths": ([AccessScopePathRequest], False),
        "MatchPaths": ([AccessScopePathRequest], False),
        "Tags": (Tags, False),
    }


class NetworkInsightsAccessScopeAnalysis(AWSObject):
    """
    `NetworkInsightsAccessScopeAnalysis <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-networkinsightsaccessscopeanalysis.html>`__
    """

    resource_type = "AWS::EC2::NetworkInsightsAccessScopeAnalysis"

    props: PropsDictType = {
        "NetworkInsightsAccessScopeId": (str, True),
        "Tags": (Tags, False),
    }


class NetworkInsightsAnalysis(AWSObject):
    """
    `NetworkInsightsAnalysis <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-networkinsightsanalysis.html>`__
    """

    resource_type = "AWS::EC2::NetworkInsightsAnalysis"

    props: PropsDictType = {
        "AdditionalAccounts": ([str], False),
        "FilterInArns": ([str], False),
        "NetworkInsightsPathId": (str, True),
        "Tags": (Tags, False),
    }


class FilterPortRange(AWSProperty):
    """
    `FilterPortRange <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-networkinsightspath-filterportrange.html>`__
    """

    props: PropsDictType = {
        "FromPort": (integer, False),
        "ToPort": (integer, False),
    }


class PathFilter(AWSProperty):
    """
    `PathFilter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-networkinsightspath-pathfilter.html>`__
    """

    props: PropsDictType = {
        "DestinationAddress": (str, False),
        "DestinationPortRange": (FilterPortRange, False),
        "SourceAddress": (str, False),
        "SourcePortRange": (FilterPortRange, False),
    }


class NetworkInsightsPath(AWSObject):
    """
    `NetworkInsightsPath <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-networkinsightspath.html>`__
    """

    resource_type = "AWS::EC2::NetworkInsightsPath"

    props: PropsDictType = {
        "Destination": (str, False),
        "DestinationIp": (str, False),
        "DestinationPort": (integer, False),
        "FilterAtDestination": (PathFilter, False),
        "FilterAtSource": (PathFilter, False),
        "Protocol": (str, True),
        "Source": (str, True),
        "SourceIp": (str, False),
        "Tags": (Tags, False),
    }


class NetworkInterface(AWSObject):
    """
    `NetworkInterface <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-networkinterface.html>`__
    """

    resource_type = "AWS::EC2::NetworkInterface"

    props: PropsDictType = {
        "ConnectionTrackingSpecification": (ConnectionTrackingSpecification, False),
        "Description": (str, False),
        "GroupSet": ([str], False),
        "InterfaceType": (str, False),
        "Ipv4PrefixCount": (integer, False),
        "Ipv4Prefixes": ([Ipv4PrefixSpecification], False),
        "Ipv6AddressCount": (integer, False),
        "Ipv6Addresses": ([InstanceIpv6Address], False),
        "Ipv6PrefixCount": (integer, False),
        "Ipv6Prefixes": ([Ipv6PrefixSpecification], False),
        "PrivateIpAddress": (str, False),
        "PrivateIpAddresses": ([PrivateIpAddressSpecification], False),
        "SecondaryPrivateIpAddressCount": (integer, False),
        "SourceDestCheck": (boolean, False),
        "SubnetId": (str, True),
        "Tags": (validate_tags_or_list, False),
    }


class NetworkInterfaceAttachment(AWSObject):
    """
    `NetworkInterfaceAttachment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-networkinterfaceattachment.html>`__
    """

    resource_type = "AWS::EC2::NetworkInterfaceAttachment"

    props: PropsDictType = {
        "DeleteOnTermination": (boolean, False),
        "DeviceIndex": (validate_int_to_str, True),
        "EnaSrdSpecification": (EnaSrdSpecification, False),
        "InstanceId": (str, True),
        "NetworkInterfaceId": (str, True),
    }


class NetworkInterfacePermission(AWSObject):
    """
    `NetworkInterfacePermission <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-networkinterfacepermission.html>`__
    """

    resource_type = "AWS::EC2::NetworkInterfacePermission"

    props: PropsDictType = {
        "AwsAccountId": (str, True),
        "NetworkInterfaceId": (str, True),
        "Permission": (str, True),
    }


class NetworkPerformanceMetricSubscription(AWSObject):
    """
    `NetworkPerformanceMetricSubscription <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-networkperformancemetricsubscription.html>`__
    """

    resource_type = "AWS::EC2::NetworkPerformanceMetricSubscription"

    props: PropsDictType = {
        "Destination": (str, True),
        "Metric": (str, True),
        "Source": (str, True),
        "Statistic": (str, True),
    }


class PlacementGroup(AWSObject):
    """
    `PlacementGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-placementgroup.html>`__
    """

    resource_type = "AWS::EC2::PlacementGroup"

    props: PropsDictType = {
        "PartitionCount": (integer, False),
        "SpreadLevel": (validate_placement_spread_level, False),
        "Strategy": (validate_placement_strategy, False),
        "Tags": (Tags, False),
    }


class Entry(AWSProperty):
    """
    `Entry <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-prefixlist-entry.html>`__
    """

    props: PropsDictType = {
        "Cidr": (str, True),
        "Description": (str, False),
    }


class PrefixList(AWSObject):
    """
    `PrefixList <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-prefixlist.html>`__
    """

    resource_type = "AWS::EC2::PrefixList"

    props: PropsDictType = {
        "AddressFamily": (str, True),
        "Entries": ([Entry], False),
        "MaxEntries": (integer, False),
        "PrefixListName": (str, True),
        "Tags": (Tags, False),
    }


class Route(AWSObject):
    """
    `Route <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-route.html>`__
    """

    resource_type = "AWS::EC2::Route"

    props: PropsDictType = {
        "CarrierGatewayId": (str, False),
        "CoreNetworkArn": (str, False),
        "DestinationCidrBlock": (str, False),
        "DestinationIpv6CidrBlock": (str, False),
        "DestinationPrefixListId": (str, False),
        "EgressOnlyInternetGatewayId": (str, False),
        "GatewayId": (str, False),
        "InstanceId": (str, False),
        "LocalGatewayId": (str, False),
        "NatGatewayId": (str, False),
        "NetworkInterfaceId": (str, False),
        "RouteTableId": (str, True),
        "TransitGatewayId": (str, False),
        "VpcEndpointId": (str, False),
        "VpcPeeringConnectionId": (str, False),
    }

    def validate(self):
        validate_route(self)


class RouteServer(AWSObject):
    """
    `RouteServer <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-routeserver.html>`__
    """

    resource_type = "AWS::EC2::RouteServer"

    props: PropsDictType = {
        "AmazonSideAsn": (integer, True),
        "PersistRoutes": (str, False),
        "PersistRoutesDuration": (integer, False),
        "SnsNotificationsEnabled": (boolean, False),
        "Tags": (Tags, False),
    }


class RouteServerAssociation(AWSObject):
    """
    `RouteServerAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-routeserverassociation.html>`__
    """

    resource_type = "AWS::EC2::RouteServerAssociation"

    props: PropsDictType = {
        "RouteServerId": (str, True),
        "VpcId": (str, True),
    }


class RouteServerEndpoint(AWSObject):
    """
    `RouteServerEndpoint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-routeserverendpoint.html>`__
    """

    resource_type = "AWS::EC2::RouteServerEndpoint"

    props: PropsDictType = {
        "RouteServerId": (str, True),
        "SubnetId": (str, True),
        "Tags": (Tags, False),
    }


class BgpOptions(AWSProperty):
    """
    `BgpOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-routeserverpeer-bgpoptions.html>`__
    """

    props: PropsDictType = {
        "PeerAsn": (integer, False),
        "PeerLivenessDetection": (str, False),
    }


class RouteServerPeer(AWSObject):
    """
    `RouteServerPeer <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-routeserverpeer.html>`__
    """

    resource_type = "AWS::EC2::RouteServerPeer"

    props: PropsDictType = {
        "BgpOptions": (BgpOptions, True),
        "PeerAddress": (str, True),
        "RouteServerEndpointId": (str, True),
        "Tags": (Tags, False),
    }


class RouteServerPropagation(AWSObject):
    """
    `RouteServerPropagation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-routeserverpropagation.html>`__
    """

    resource_type = "AWS::EC2::RouteServerPropagation"

    props: PropsDictType = {
        "RouteServerId": (str, True),
        "RouteTableId": (str, True),
    }


class RouteTable(AWSObject):
    """
    `RouteTable <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-routetable.html>`__
    """

    resource_type = "AWS::EC2::RouteTable"

    props: PropsDictType = {
        "Tags": (validate_tags_or_list, False),
        "VpcId": (str, True),
    }


class SecurityGroup(AWSObject):
    """
    `SecurityGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-securitygroup.html>`__
    """

    resource_type = "AWS::EC2::SecurityGroup"

    props: PropsDictType = {
        "GroupDescription": (str, True),
        "GroupName": (str, False),
        "SecurityGroupEgress": (list, False),
        "SecurityGroupIngress": (list, False),
        "Tags": (validate_tags_or_list, False),
        "VpcId": (str, False),
    }


class SecurityGroupEgress(AWSObject):
    """
    `SecurityGroupEgress <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-securitygroupegress.html>`__
    """

    resource_type = "AWS::EC2::SecurityGroupEgress"

    props: PropsDictType = {
        "CidrIp": (str, False),
        "CidrIpv6": (str, False),
        "Description": (str, False),
        "DestinationPrefixListId": (str, False),
        "DestinationSecurityGroupId": (str, False),
        "FromPort": (validate_network_port, False),
        "GroupId": (str, True),
        "IpProtocol": (str, True),
        "ToPort": (validate_network_port, False),
    }

    def validate(self):
        validate_security_group_egress(self)


class SecurityGroupIngress(AWSObject):
    """
    `SecurityGroupIngress <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-securitygroupingress.html>`__
    """

    resource_type = "AWS::EC2::SecurityGroupIngress"

    props: PropsDictType = {
        "CidrIp": (str, False),
        "CidrIpv6": (str, False),
        "Description": (str, False),
        "FromPort": (validate_network_port, False),
        "GroupId": (str, False),
        "GroupName": (str, False),
        "IpProtocol": (str, True),
        "SourcePrefixListId": (str, False),
        "SourceSecurityGroupId": (str, False),
        "SourceSecurityGroupName": (str, False),
        "SourceSecurityGroupOwnerId": (str, False),
        "ToPort": (validate_network_port, False),
    }

    def validate(self):
        validate_security_group_ingress(self)


class SecurityGroupVpcAssociation(AWSObject):
    """
    `SecurityGroupVpcAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-securitygroupvpcassociation.html>`__
    """

    resource_type = "AWS::EC2::SecurityGroupVpcAssociation"

    props: PropsDictType = {
        "GroupId": (str, True),
        "VpcId": (str, True),
    }


class SnapshotBlockPublicAccess(AWSObject):
    """
    `SnapshotBlockPublicAccess <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-snapshotblockpublicaccess.html>`__
    """

    resource_type = "AWS::EC2::SnapshotBlockPublicAccess"

    props: PropsDictType = {
        "State": (str, True),
    }


class IamInstanceProfileSpecification(AWSProperty):
    """
    `IamInstanceProfileSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-iaminstanceprofilespecification.html>`__
    """

    props: PropsDictType = {
        "Arn": (str, False),
    }


class InstanceNetworkInterfaceSpecification(AWSProperty):
    """
    `InstanceNetworkInterfaceSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-instancenetworkinterfacespecification.html>`__
    """

    props: PropsDictType = {
        "AssociatePublicIpAddress": (boolean, False),
        "DeleteOnTermination": (boolean, False),
        "Description": (str, False),
        "DeviceIndex": (integer, False),
        "Groups": ([str], False),
        "Ipv6AddressCount": (integer, False),
        "Ipv6Addresses": ([InstanceIpv6Address], False),
        "NetworkInterfaceId": (str, False),
        "PrivateIpAddresses": ([PrivateIpAddressSpecification], False),
        "SecondaryPrivateIpAddressCount": (integer, False),
        "SubnetId": (str, False),
    }


class SecurityGroups(AWSProperty):
    """
    `SecurityGroups <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-groupidentifier.html>`__
    """

    props: PropsDictType = {
        "GroupId": (str, True),
    }


class SpotFleetTagSpecification(AWSProperty):
    """
    `SpotFleetTagSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-spotfleettagspecification.html>`__
    """

    props: PropsDictType = {
        "ResourceType": (str, False),
        "Tags": (validate_tags_or_list, False),
    }


class SpotPlacement(AWSProperty):
    """
    `SpotPlacement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-spotplacement.html>`__
    """

    props: PropsDictType = {
        "AvailabilityZone": (str, False),
        "GroupName": (str, False),
        "Tenancy": (str, False),
    }


class LaunchSpecifications(AWSProperty):
    """
    `LaunchSpecifications <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-spotfleetlaunchspecification.html>`__
    """

    props: PropsDictType = {
        "BlockDeviceMappings": ([BlockDeviceMapping], False),
        "EbsOptimized": (boolean, False),
        "IamInstanceProfile": (IamInstanceProfileSpecification, False),
        "ImageId": (str, True),
        "InstanceRequirements": (InstanceRequirementsRequest, False),
        "InstanceType": (str, False),
        "KernelId": (str, False),
        "KeyName": (str, False),
        "Monitoring": (Monitoring, False),
        "NetworkInterfaces": ([InstanceNetworkInterfaceSpecification], False),
        "Placement": (SpotPlacement, False),
        "RamdiskId": (str, False),
        "SecurityGroups": ([SecurityGroups], False),
        "SpotPrice": (str, False),
        "SubnetId": (str, False),
        "TagSpecifications": ([SpotFleetTagSpecification], False),
        "UserData": (str, False),
        "WeightedCapacity": (double, False),
    }


class FleetLaunchTemplateSpecification(AWSProperty):
    """
    `FleetLaunchTemplateSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-fleetlaunchtemplatespecification.html>`__
    """

    props: PropsDictType = {
        "LaunchTemplateId": (str, False),
        "LaunchTemplateName": (str, False),
        "Version": (str, True),
    }


class LaunchTemplateOverrides(AWSProperty):
    """
    `LaunchTemplateOverrides <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-launchtemplateoverrides.html>`__
    """

    props: PropsDictType = {
        "AvailabilityZone": (str, False),
        "InstanceRequirements": (InstanceRequirementsRequest, False),
        "InstanceType": (str, False),
        "Priority": (double, False),
        "SpotPrice": (str, False),
        "SubnetId": (str, False),
        "WeightedCapacity": (double, False),
    }


class LaunchTemplateConfigs(AWSProperty):
    """
    `LaunchTemplateConfigs <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-launchtemplateconfig.html>`__
    """

    props: PropsDictType = {
        "LaunchTemplateSpecification": (FleetLaunchTemplateSpecification, False),
        "Overrides": ([LaunchTemplateOverrides], False),
    }


class ClassicLoadBalancer(AWSProperty):
    """
    `ClassicLoadBalancer <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-classicloadbalancer.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
    }


class ClassicLoadBalancersConfig(AWSProperty):
    """
    `ClassicLoadBalancersConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-classicloadbalancersconfig.html>`__
    """

    props: PropsDictType = {
        "ClassicLoadBalancers": ([ClassicLoadBalancer], True),
    }


class TargetGroup(AWSProperty):
    """
    `TargetGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-targetgroup.html>`__
    """

    props: PropsDictType = {
        "Arn": (str, True),
    }


class TargetGroupConfig(AWSProperty):
    """
    `TargetGroupConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-targetgroupsconfig.html>`__
    """

    props: PropsDictType = {
        "TargetGroups": ([TargetGroup], True),
    }


class LoadBalancersConfig(AWSProperty):
    """
    `LoadBalancersConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-loadbalancersconfig.html>`__
    """

    props: PropsDictType = {
        "ClassicLoadBalancersConfig": (ClassicLoadBalancersConfig, False),
        "TargetGroupsConfig": (TargetGroupConfig, False),
    }


class SpotCapacityRebalance(AWSProperty):
    """
    `SpotCapacityRebalance <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-spotcapacityrebalance.html>`__
    """

    props: PropsDictType = {
        "ReplacementStrategy": (str, False),
        "TerminationDelay": (integer, False),
    }


class SpotMaintenanceStrategies(AWSProperty):
    """
    `SpotMaintenanceStrategies <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-spotmaintenancestrategies.html>`__
    """

    props: PropsDictType = {
        "CapacityRebalance": (SpotCapacityRebalance, False),
    }


class SpotFleetRequestConfigData(AWSProperty):
    """
    `SpotFleetRequestConfigData <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-spotfleetrequestconfigdata.html>`__
    """

    props: PropsDictType = {
        "AllocationStrategy": (str, False),
        "Context": (str, False),
        "ExcessCapacityTerminationPolicy": (str, False),
        "IamFleetRole": (str, True),
        "InstanceInterruptionBehavior": (str, False),
        "InstancePoolsToUseCount": (integer, False),
        "LaunchSpecifications": ([LaunchSpecifications], False),
        "LaunchTemplateConfigs": ([LaunchTemplateConfigs], False),
        "LoadBalancersConfig": (LoadBalancersConfig, False),
        "OnDemandAllocationStrategy": (str, False),
        "OnDemandMaxTotalPrice": (str, False),
        "OnDemandTargetCapacity": (integer, False),
        "ReplaceUnhealthyInstances": (boolean, False),
        "SpotMaintenanceStrategies": (SpotMaintenanceStrategies, False),
        "SpotMaxTotalPrice": (str, False),
        "SpotPrice": (str, False),
        "TagSpecifications": ([SpotFleetTagSpecification], False),
        "TargetCapacity": (integer, True),
        "TargetCapacityUnitType": (str, False),
        "TerminateInstancesWithExpiration": (boolean, False),
        "Type": (str, False),
        "ValidFrom": (str, False),
        "ValidUntil": (str, False),
    }

    def validate(self):
        validate_spot_fleet_request_config_data(self)


class SpotFleet(AWSObject):
    """
    `SpotFleet <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-spotfleet.html>`__
    """

    resource_type = "AWS::EC2::SpotFleet"

    props: PropsDictType = {
        "SpotFleetRequestConfigData": (SpotFleetRequestConfigData, True),
    }


class PrivateDnsNameOptionsOnLaunch(AWSProperty):
    """
    `PrivateDnsNameOptionsOnLaunch <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-subnet-privatednsnameoptionsonlaunch.html>`__
    """

    props: PropsDictType = {
        "EnableResourceNameDnsAAAARecord": (boolean, False),
        "EnableResourceNameDnsARecord": (boolean, False),
        "HostnameType": (str, False),
    }


class Subnet(AWSObject):
    """
    `Subnet <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-subnet.html>`__
    """

    resource_type = "AWS::EC2::Subnet"

    props: PropsDictType = {
        "AssignIpv6AddressOnCreation": (boolean, False),
        "AvailabilityZone": (str, False),
        "AvailabilityZoneId": (str, False),
        "CidrBlock": (str, False),
        "EnableDns64": (boolean, False),
        "EnableLniAtDeviceIndex": (integer, False),
        "Ipv4IpamPoolId": (str, False),
        "Ipv4NetmaskLength": (integer, False),
        "Ipv6CidrBlock": (str, False),
        "Ipv6IpamPoolId": (str, False),
        "Ipv6Native": (boolean, False),
        "Ipv6NetmaskLength": (integer, False),
        "MapPublicIpOnLaunch": (boolean, False),
        "OutpostArn": (str, False),
        "PrivateDnsNameOptionsOnLaunch": (PrivateDnsNameOptionsOnLaunch, False),
        "Tags": (validate_tags_or_list, False),
        "VpcId": (str, True),
    }

    def validate(self):
        validate_subnet(self)


class SubnetCidrBlock(AWSObject):
    """
    `SubnetCidrBlock <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-subnetcidrblock.html>`__
    """

    resource_type = "AWS::EC2::SubnetCidrBlock"

    props: PropsDictType = {
        "Ipv6CidrBlock": (str, False),
        "Ipv6IpamPoolId": (str, False),
        "Ipv6NetmaskLength": (integer, False),
        "SubnetId": (str, True),
    }


class SubnetNetworkAclAssociation(AWSObject):
    """
    `SubnetNetworkAclAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-subnetnetworkaclassociation.html>`__
    """

    resource_type = "AWS::EC2::SubnetNetworkAclAssociation"

    props: PropsDictType = {
        "NetworkAclId": (str, True),
        "SubnetId": (str, True),
    }


class SubnetRouteTableAssociation(AWSObject):
    """
    `SubnetRouteTableAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-subnetroutetableassociation.html>`__
    """

    resource_type = "AWS::EC2::SubnetRouteTableAssociation"

    props: PropsDictType = {
        "RouteTableId": (str, True),
        "SubnetId": (str, True),
    }


class TrafficMirrorFilter(AWSObject):
    """
    `TrafficMirrorFilter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-trafficmirrorfilter.html>`__
    """

    resource_type = "AWS::EC2::TrafficMirrorFilter"

    props: PropsDictType = {
        "Description": (str, False),
        "NetworkServices": ([str], False),
        "Tags": (Tags, False),
    }


class TrafficMirrorPortRange(AWSProperty):
    """
    `TrafficMirrorPortRange <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-trafficmirrorfilterrule-trafficmirrorportrange.html>`__
    """

    props: PropsDictType = {
        "FromPort": (integer, True),
        "ToPort": (integer, True),
    }


class TrafficMirrorFilterRule(AWSObject):
    """
    `TrafficMirrorFilterRule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-trafficmirrorfilterrule.html>`__
    """

    resource_type = "AWS::EC2::TrafficMirrorFilterRule"

    props: PropsDictType = {
        "Description": (str, False),
        "DestinationCidrBlock": (str, True),
        "DestinationPortRange": (TrafficMirrorPortRange, False),
        "Protocol": (integer, False),
        "RuleAction": (str, True),
        "RuleNumber": (integer, True),
        "SourceCidrBlock": (str, True),
        "SourcePortRange": (TrafficMirrorPortRange, False),
        "Tags": (Tags, False),
        "TrafficDirection": (str, True),
        "TrafficMirrorFilterId": (str, True),
    }


class TrafficMirrorSession(AWSObject):
    """
    `TrafficMirrorSession <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-trafficmirrorsession.html>`__
    """

    resource_type = "AWS::EC2::TrafficMirrorSession"

    props: PropsDictType = {
        "Description": (str, False),
        "NetworkInterfaceId": (str, True),
        "PacketLength": (integer, False),
        "SessionNumber": (integer, True),
        "Tags": (Tags, False),
        "TrafficMirrorFilterId": (str, True),
        "TrafficMirrorTargetId": (str, True),
        "VirtualNetworkId": (integer, False),
    }


class TrafficMirrorTarget(AWSObject):
    """
    `TrafficMirrorTarget <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-trafficmirrortarget.html>`__
    """

    resource_type = "AWS::EC2::TrafficMirrorTarget"

    props: PropsDictType = {
        "Description": (str, False),
        "GatewayLoadBalancerEndpointId": (str, False),
        "NetworkInterfaceId": (str, False),
        "NetworkLoadBalancerArn": (str, False),
        "Tags": (Tags, False),
    }


class TransitGateway(AWSObject):
    """
    `TransitGateway <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-transitgateway.html>`__
    """

    resource_type = "AWS::EC2::TransitGateway"

    props: PropsDictType = {
        "AmazonSideAsn": (integer, False),
        "AssociationDefaultRouteTableId": (str, False),
        "AutoAcceptSharedAttachments": (str, False),
        "DefaultRouteTableAssociation": (str, False),
        "DefaultRouteTablePropagation": (str, False),
        "Description": (str, False),
        "DnsSupport": (str, False),
        "MulticastSupport": (str, False),
        "PropagationDefaultRouteTableId": (str, False),
        "SecurityGroupReferencingSupport": (str, False),
        "Tags": (validate_tags_or_list, False),
        "TransitGatewayCidrBlocks": ([str], False),
        "VpnEcmpSupport": (str, False),
    }


class Options(AWSProperty):
    """
    `Options <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-transitgatewayvpcattachment-options.html>`__
    """

    props: PropsDictType = {
        "ApplianceModeSupport": (str, False),
        "DnsSupport": (str, False),
        "Ipv6Support": (str, False),
        "SecurityGroupReferencingSupport": (str, False),
    }


class TransitGatewayAttachment(AWSObject):
    """
    `TransitGatewayAttachment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-transitgatewayattachment.html>`__
    """

    resource_type = "AWS::EC2::TransitGatewayAttachment"

    props: PropsDictType = {
        "Options": (Options, False),
        "SubnetIds": ([str], True),
        "Tags": (validate_tags_or_list, False),
        "TransitGatewayId": (str, True),
        "VpcId": (str, True),
    }


class TransitGatewayConnectOptions(AWSProperty):
    """
    `TransitGatewayConnectOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-transitgatewayconnect-transitgatewayconnectoptions.html>`__
    """

    props: PropsDictType = {
        "Protocol": (str, False),
    }


class TransitGatewayConnect(AWSObject):
    """
    `TransitGatewayConnect <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-transitgatewayconnect.html>`__
    """

    resource_type = "AWS::EC2::TransitGatewayConnect"

    props: PropsDictType = {
        "Options": (TransitGatewayConnectOptions, True),
        "Tags": (Tags, False),
        "TransportTransitGatewayAttachmentId": (str, True),
    }


class MulticastDomainOptions(AWSProperty):
    """
    `MulticastDomainOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-transitgatewaymulticastdomain-options.html>`__
    """

    props: PropsDictType = {
        "AutoAcceptSharedAssociations": (str, False),
        "Igmpv2Support": (str, False),
        "StaticSourcesSupport": (str, False),
    }


class TransitGatewayMulticastDomain(AWSObject):
    """
    `TransitGatewayMulticastDomain <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-transitgatewaymulticastdomain.html>`__
    """

    resource_type = "AWS::EC2::TransitGatewayMulticastDomain"

    props: PropsDictType = {
        "Options": (MulticastDomainOptions, False),
        "Tags": (Tags, False),
        "TransitGatewayId": (str, True),
    }


class TransitGatewayMulticastDomainAssociation(AWSObject):
    """
    `TransitGatewayMulticastDomainAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-transitgatewaymulticastdomainassociation.html>`__
    """

    resource_type = "AWS::EC2::TransitGatewayMulticastDomainAssociation"

    props: PropsDictType = {
        "SubnetId": (str, True),
        "TransitGatewayAttachmentId": (str, True),
        "TransitGatewayMulticastDomainId": (str, True),
    }


class TransitGatewayMulticastGroupMember(AWSObject):
    """
    `TransitGatewayMulticastGroupMember <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-transitgatewaymulticastgroupmember.html>`__
    """

    resource_type = "AWS::EC2::TransitGatewayMulticastGroupMember"

    props: PropsDictType = {
        "GroupIpAddress": (str, True),
        "NetworkInterfaceId": (str, True),
        "TransitGatewayMulticastDomainId": (str, True),
    }


class TransitGatewayMulticastGroupSource(AWSObject):
    """
    `TransitGatewayMulticastGroupSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-transitgatewaymulticastgroupsource.html>`__
    """

    resource_type = "AWS::EC2::TransitGatewayMulticastGroupSource"

    props: PropsDictType = {
        "GroupIpAddress": (str, True),
        "NetworkInterfaceId": (str, True),
        "TransitGatewayMulticastDomainId": (str, True),
    }


class TransitGatewayPeeringAttachment(AWSObject):
    """
    `TransitGatewayPeeringAttachment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-transitgatewaypeeringattachment.html>`__
    """

    resource_type = "AWS::EC2::TransitGatewayPeeringAttachment"

    props: PropsDictType = {
        "PeerAccountId": (str, True),
        "PeerRegion": (str, True),
        "PeerTransitGatewayId": (str, True),
        "Tags": (Tags, False),
        "TransitGatewayId": (str, True),
    }


class TransitGatewayRoute(AWSObject):
    """
    `TransitGatewayRoute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-transitgatewayroute.html>`__
    """

    resource_type = "AWS::EC2::TransitGatewayRoute"

    props: PropsDictType = {
        "Blackhole": (boolean, False),
        "DestinationCidrBlock": (str, True),
        "TransitGatewayAttachmentId": (str, False),
        "TransitGatewayRouteTableId": (str, True),
    }


class TransitGatewayRouteTable(AWSObject):
    """
    `TransitGatewayRouteTable <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-transitgatewayroutetable.html>`__
    """

    resource_type = "AWS::EC2::TransitGatewayRouteTable"

    props: PropsDictType = {
        "Tags": (validate_tags_or_list, False),
        "TransitGatewayId": (str, True),
    }


class TransitGatewayRouteTableAssociation(AWSObject):
    """
    `TransitGatewayRouteTableAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-transitgatewayroutetableassociation.html>`__
    """

    resource_type = "AWS::EC2::TransitGatewayRouteTableAssociation"

    props: PropsDictType = {
        "TransitGatewayAttachmentId": (str, True),
        "TransitGatewayRouteTableId": (str, True),
    }


class TransitGatewayRouteTablePropagation(AWSObject):
    """
    `TransitGatewayRouteTablePropagation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-transitgatewayroutetablepropagation.html>`__
    """

    resource_type = "AWS::EC2::TransitGatewayRouteTablePropagation"

    props: PropsDictType = {
        "TransitGatewayAttachmentId": (str, True),
        "TransitGatewayRouteTableId": (str, True),
    }


class TransitGatewayVpcAttachment(AWSObject):
    """
    `TransitGatewayVpcAttachment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-transitgatewayvpcattachment.html>`__
    """

    resource_type = "AWS::EC2::TransitGatewayVpcAttachment"

    props: PropsDictType = {
        "AddSubnetIds": ([str], False),
        "Options": (Options, False),
        "RemoveSubnetIds": ([str], False),
        "SubnetIds": ([str], True),
        "Tags": (Tags, False),
        "TransitGatewayId": (str, True),
        "VpcId": (str, True),
    }


class VPC(AWSObject):
    """
    `VPC <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-vpc.html>`__
    """

    resource_type = "AWS::EC2::VPC"

    props: PropsDictType = {
        "CidrBlock": (str, False),
        "EnableDnsHostnames": (boolean, False),
        "EnableDnsSupport": (boolean, False),
        "InstanceTenancy": (instance_tenancy, False),
        "Ipv4IpamPoolId": (str, False),
        "Ipv4NetmaskLength": (integer, False),
        "Tags": (validate_tags_or_list, False),
    }


class VPCBlockPublicAccessExclusion(AWSObject):
    """
    `VPCBlockPublicAccessExclusion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-vpcblockpublicaccessexclusion.html>`__
    """

    resource_type = "AWS::EC2::VPCBlockPublicAccessExclusion"

    props: PropsDictType = {
        "InternetGatewayExclusionMode": (str, True),
        "SubnetId": (str, False),
        "Tags": (Tags, False),
        "VpcId": (str, False),
    }


class VPCBlockPublicAccessOptions(AWSObject):
    """
    `VPCBlockPublicAccessOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-vpcblockpublicaccessoptions.html>`__
    """

    resource_type = "AWS::EC2::VPCBlockPublicAccessOptions"

    props: PropsDictType = {
        "InternetGatewayBlockMode": (str, True),
    }


class VPCCidrBlock(AWSObject):
    """
    `VPCCidrBlock <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-vpccidrblock.html>`__
    """

    resource_type = "AWS::EC2::VPCCidrBlock"

    props: PropsDictType = {
        "AmazonProvidedIpv6CidrBlock": (boolean, False),
        "CidrBlock": (str, False),
        "Ipv4IpamPoolId": (str, False),
        "Ipv4NetmaskLength": (integer, False),
        "Ipv6CidrBlock": (str, False),
        "Ipv6CidrBlockNetworkBorderGroup": (str, False),
        "Ipv6IpamPoolId": (str, False),
        "Ipv6NetmaskLength": (integer, False),
        "Ipv6Pool": (str, False),
        "VpcId": (str, True),
    }


class VPCDHCPOptionsAssociation(AWSObject):
    """
    `VPCDHCPOptionsAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-vpcdhcpoptionsassociation.html>`__
    """

    resource_type = "AWS::EC2::VPCDHCPOptionsAssociation"

    props: PropsDictType = {
        "DhcpOptionsId": (str, True),
        "VpcId": (str, True),
    }


class DnsOptionsSpecification(AWSProperty):
    """
    `DnsOptionsSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-vpcendpoint-dnsoptionsspecification.html>`__
    """

    props: PropsDictType = {
        "DnsRecordIpType": (str, False),
        "PrivateDnsOnlyForInboundResolverEndpoint": (str, False),
    }


class VPCEndpoint(AWSObject):
    """
    `VPCEndpoint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-vpcendpoint.html>`__
    """

    resource_type = "AWS::EC2::VPCEndpoint"

    props: PropsDictType = {
        "DnsOptions": (DnsOptionsSpecification, False),
        "IpAddressType": (str, False),
        "PolicyDocument": (policytypes, False),
        "PrivateDnsEnabled": (boolean, False),
        "ResourceConfigurationArn": (str, False),
        "RouteTableIds": ([str], False),
        "SecurityGroupIds": ([str], False),
        "ServiceName": (str, False),
        "ServiceNetworkArn": (str, False),
        "SubnetIds": ([str], False),
        "Tags": (Tags, False),
        "VpcEndpointType": (vpc_endpoint_type, False),
        "VpcId": (str, True),
    }


class VPCEndpointConnectionNotification(AWSObject):
    """
    `VPCEndpointConnectionNotification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-vpcendpointconnectionnotification.html>`__
    """

    resource_type = "AWS::EC2::VPCEndpointConnectionNotification"

    props: PropsDictType = {
        "ConnectionEvents": ([str], True),
        "ConnectionNotificationArn": (str, True),
        "ServiceId": (str, False),
        "VPCEndpointId": (str, False),
    }


class VPCEndpointService(AWSObject):
    """
    `VPCEndpointService <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-vpcendpointservice.html>`__
    """

    resource_type = "AWS::EC2::VPCEndpointService"

    props: PropsDictType = {
        "AcceptanceRequired": (boolean, False),
        "ContributorInsightsEnabled": (boolean, False),
        "GatewayLoadBalancerArns": ([str], False),
        "NetworkLoadBalancerArns": ([str], False),
        "PayerResponsibility": (str, False),
        "SupportedIpAddressTypes": ([str], False),
        "SupportedRegions": ([str], False),
        "Tags": (Tags, False),
    }


class VPCEndpointServicePermissions(AWSObject):
    """
    `VPCEndpointServicePermissions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-vpcendpointservicepermissions.html>`__
    """

    resource_type = "AWS::EC2::VPCEndpointServicePermissions"

    props: PropsDictType = {
        "AllowedPrincipals": ([str], False),
        "ServiceId": (str, True),
    }


class VPCGatewayAttachment(AWSObject):
    """
    `VPCGatewayAttachment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-vpcgatewayattachment.html>`__
    """

    resource_type = "AWS::EC2::VPCGatewayAttachment"

    props: PropsDictType = {
        "InternetGatewayId": (str, False),
        "VpcId": (str, True),
        "VpnGatewayId": (str, False),
    }


class VPCPeeringConnection(AWSObject):
    """
    `VPCPeeringConnection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-vpcpeeringconnection.html>`__
    """

    resource_type = "AWS::EC2::VPCPeeringConnection"

    props: PropsDictType = {
        "PeerOwnerId": (str, False),
        "PeerRegion": (str, False),
        "PeerRoleArn": (str, False),
        "PeerVpcId": (str, True),
        "Tags": (validate_tags_or_list, False),
        "VpcId": (str, True),
    }


class IKEVersionsRequestListValue(AWSProperty):
    """
    `IKEVersionsRequestListValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-vpnconnection-ikeversionsrequestlistvalue.html>`__
    """

    props: PropsDictType = {
        "Value": (str, False),
    }


class Phase1DHGroupNumbersRequestListValue(AWSProperty):
    """
    `Phase1DHGroupNumbersRequestListValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-vpnconnection-phase1dhgroupnumbersrequestlistvalue.html>`__
    """

    props: PropsDictType = {
        "Value": (integer, False),
    }


class Phase1EncryptionAlgorithmsRequestListValue(AWSProperty):
    """
    `Phase1EncryptionAlgorithmsRequestListValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-vpnconnection-phase1encryptionalgorithmsrequestlistvalue.html>`__
    """

    props: PropsDictType = {
        "Value": (str, False),
    }


class Phase1IntegrityAlgorithmsRequestListValue(AWSProperty):
    """
    `Phase1IntegrityAlgorithmsRequestListValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-vpnconnection-phase1integrityalgorithmsrequestlistvalue.html>`__
    """

    props: PropsDictType = {
        "Value": (str, False),
    }


class Phase2DHGroupNumbersRequestListValue(AWSProperty):
    """
    `Phase2DHGroupNumbersRequestListValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-vpnconnection-phase2dhgroupnumbersrequestlistvalue.html>`__
    """

    props: PropsDictType = {
        "Value": (integer, False),
    }


class Phase2EncryptionAlgorithmsRequestListValue(AWSProperty):
    """
    `Phase2EncryptionAlgorithmsRequestListValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-vpnconnection-phase2encryptionalgorithmsrequestlistvalue.html>`__
    """

    props: PropsDictType = {
        "Value": (str, False),
    }


class Phase2IntegrityAlgorithmsRequestListValue(AWSProperty):
    """
    `Phase2IntegrityAlgorithmsRequestListValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-vpnconnection-phase2integrityalgorithmsrequestlistvalue.html>`__
    """

    props: PropsDictType = {
        "Value": (str, False),
    }


class CloudwatchLogOptionsSpecification(AWSProperty):
    """
    `CloudwatchLogOptionsSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-vpnconnection-cloudwatchlogoptionsspecification.html>`__
    """

    props: PropsDictType = {
        "LogEnabled": (boolean, False),
        "LogGroupArn": (str, False),
        "LogOutputFormat": (str, False),
    }


class VpnTunnelLogOptionsSpecification(AWSProperty):
    """
    `VpnTunnelLogOptionsSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-vpnconnection-vpntunnellogoptionsspecification.html>`__
    """

    props: PropsDictType = {
        "CloudwatchLogOptions": (CloudwatchLogOptionsSpecification, False),
    }


class VpnTunnelOptionsSpecification(AWSProperty):
    """
    `VpnTunnelOptionsSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-vpnconnection-vpntunneloptionsspecification.html>`__
    """

    props: PropsDictType = {
        "DPDTimeoutAction": (str, False),
        "DPDTimeoutSeconds": (integer, False),
        "EnableTunnelLifecycleControl": (boolean, False),
        "IKEVersions": ([IKEVersionsRequestListValue], False),
        "LogOptions": (VpnTunnelLogOptionsSpecification, False),
        "Phase1DHGroupNumbers": ([Phase1DHGroupNumbersRequestListValue], False),
        "Phase1EncryptionAlgorithms": (
            [Phase1EncryptionAlgorithmsRequestListValue],
            False,
        ),
        "Phase1IntegrityAlgorithms": (
            [Phase1IntegrityAlgorithmsRequestListValue],
            False,
        ),
        "Phase1LifetimeSeconds": (integer, False),
        "Phase2DHGroupNumbers": ([Phase2DHGroupNumbersRequestListValue], False),
        "Phase2EncryptionAlgorithms": (
            [Phase2EncryptionAlgorithmsRequestListValue],
            False,
        ),
        "Phase2IntegrityAlgorithms": (
            [Phase2IntegrityAlgorithmsRequestListValue],
            False,
        ),
        "Phase2LifetimeSeconds": (integer, False),
        "PreSharedKey": (vpn_pre_shared_key, False),
        "RekeyFuzzPercentage": (integer, False),
        "RekeyMarginTimeSeconds": (integer, False),
        "ReplayWindowSize": (integer, False),
        "StartupAction": (str, False),
        "TunnelInsideCidr": (vpn_tunnel_inside_cidr, False),
        "TunnelInsideIpv6Cidr": (str, False),
    }


class VPNConnection(AWSObject):
    """
    `VPNConnection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-vpnconnection.html>`__
    """

    resource_type = "AWS::EC2::VPNConnection"

    props: PropsDictType = {
        "CustomerGatewayId": (str, True),
        "EnableAcceleration": (boolean, False),
        "LocalIpv4NetworkCidr": (str, False),
        "LocalIpv6NetworkCidr": (str, False),
        "OutsideIpAddressType": (str, False),
        "RemoteIpv4NetworkCidr": (str, False),
        "RemoteIpv6NetworkCidr": (str, False),
        "StaticRoutesOnly": (boolean, False),
        "Tags": (validate_tags_or_list, False),
        "TransitGatewayId": (str, False),
        "TransportTransitGatewayAttachmentId": (str, False),
        "TunnelInsideIpVersion": (str, False),
        "Type": (str, True),
        "VpnGatewayId": (str, False),
        "VpnTunnelOptionsSpecifications": ([VpnTunnelOptionsSpecification], False),
    }

    def validate(self):
        validate_vpn_connection(self)


class VPNConnectionRoute(AWSObject):
    """
    `VPNConnectionRoute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-vpnconnectionroute.html>`__
    """

    resource_type = "AWS::EC2::VPNConnectionRoute"

    props: PropsDictType = {
        "DestinationCidrBlock": (str, True),
        "VpnConnectionId": (str, True),
    }


class VPNGateway(AWSObject):
    """
    `VPNGateway <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-vpngateway.html>`__
    """

    resource_type = "AWS::EC2::VPNGateway"

    props: PropsDictType = {
        "AmazonSideAsn": (integer, False),
        "Tags": (validate_tags_or_list, False),
        "Type": (str, True),
    }


class VPNGatewayRoutePropagation(AWSObject):
    """
    `VPNGatewayRoutePropagation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-vpngatewayroutepropagation.html>`__
    """

    resource_type = "AWS::EC2::VPNGatewayRoutePropagation"

    props: PropsDictType = {
        "RouteTableIds": ([str], True),
        "VpnGatewayId": (str, True),
    }


class CidrOptions(AWSProperty):
    """
    `CidrOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-verifiedaccessendpoint-cidroptions.html>`__
    """

    props: PropsDictType = {
        "Cidr": (str, False),
        "PortRanges": ([PortRange], False),
        "Protocol": (str, False),
        "SubnetIds": ([str], False),
    }


class VerifiedAccessEndpointPortRange(AWSProperty):
    """
    `VerifiedAccessEndpointPortRange <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-verifiedaccessendpoint-portrange.html>`__
    """

    props: PropsDictType = {
        "FromPort": (integer, False),
        "ToPort": (integer, False),
    }


class LoadBalancerOptions(AWSProperty):
    """
    `LoadBalancerOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-verifiedaccessendpoint-loadbalanceroptions.html>`__
    """

    props: PropsDictType = {
        "LoadBalancerArn": (str, False),
        "Port": (integer, False),
        "PortRanges": ([VerifiedAccessEndpointPortRange], False),
        "Protocol": (str, False),
        "SubnetIds": ([str], False),
    }


class NetworkInterfaceOptions(AWSProperty):
    """
    `NetworkInterfaceOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-verifiedaccessendpoint-networkinterfaceoptions.html>`__
    """

    props: PropsDictType = {
        "NetworkInterfaceId": (str, False),
        "Port": (integer, False),
        "PortRanges": ([PortRange], False),
        "Protocol": (str, False),
    }


class RdsOptions(AWSProperty):
    """
    `RdsOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-verifiedaccessendpoint-rdsoptions.html>`__
    """

    props: PropsDictType = {
        "Port": (integer, False),
        "Protocol": (str, False),
        "RdsDbClusterArn": (str, False),
        "RdsDbInstanceArn": (str, False),
        "RdsDbProxyArn": (str, False),
        "RdsEndpoint": (str, False),
        "SubnetIds": ([str], False),
    }


class SseSpecification(AWSProperty):
    """
    `SseSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-verifiedaccesstrustprovider-ssespecification.html>`__
    """

    props: PropsDictType = {
        "CustomerManagedKeyEnabled": (boolean, False),
        "KmsKeyArn": (str, False),
    }


class VerifiedAccessEndpoint(AWSObject):
    """
    `VerifiedAccessEndpoint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-verifiedaccessendpoint.html>`__
    """

    resource_type = "AWS::EC2::VerifiedAccessEndpoint"

    props: PropsDictType = {
        "ApplicationDomain": (str, False),
        "AttachmentType": (str, True),
        "CidrOptions": (CidrOptions, False),
        "Description": (str, False),
        "DomainCertificateArn": (str, False),
        "EndpointDomainPrefix": (str, False),
        "EndpointType": (str, True),
        "LoadBalancerOptions": (LoadBalancerOptions, False),
        "NetworkInterfaceOptions": (NetworkInterfaceOptions, False),
        "PolicyDocument": (str, False),
        "PolicyEnabled": (boolean, False),
        "RdsOptions": (RdsOptions, False),
        "SecurityGroupIds": ([str], False),
        "SseSpecification": (SseSpecification, False),
        "Tags": (Tags, False),
        "VerifiedAccessGroupId": (str, True),
    }


class VerifiedAccessGroup(AWSObject):
    """
    `VerifiedAccessGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-verifiedaccessgroup.html>`__
    """

    resource_type = "AWS::EC2::VerifiedAccessGroup"

    props: PropsDictType = {
        "Description": (str, False),
        "PolicyDocument": (str, False),
        "PolicyEnabled": (boolean, False),
        "SseSpecification": (SseSpecification, False),
        "Tags": (Tags, False),
        "VerifiedAccessInstanceId": (str, True),
    }


class CloudWatchLogs(AWSProperty):
    """
    `CloudWatchLogs <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-verifiedaccessinstance-cloudwatchlogs.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, False),
        "LogGroup": (str, False),
    }


class KinesisDataFirehose(AWSProperty):
    """
    `KinesisDataFirehose <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-verifiedaccessinstance-kinesisdatafirehose.html>`__
    """

    props: PropsDictType = {
        "DeliveryStream": (str, False),
        "Enabled": (boolean, False),
    }


class S3(AWSProperty):
    """
    `S3 <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-verifiedaccessinstance-s3.html>`__
    """

    props: PropsDictType = {
        "BucketName": (str, False),
        "BucketOwner": (str, False),
        "Enabled": (boolean, False),
        "Prefix": (str, False),
    }


class VerifiedAccessLogs(AWSProperty):
    """
    `VerifiedAccessLogs <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-verifiedaccessinstance-verifiedaccesslogs.html>`__
    """

    props: PropsDictType = {
        "CloudWatchLogs": (CloudWatchLogs, False),
        "IncludeTrustContext": (boolean, False),
        "KinesisDataFirehose": (KinesisDataFirehose, False),
        "LogVersion": (str, False),
        "S3": (S3, False),
    }


class VerifiedAccessTrustProviderProperty(AWSProperty):
    """
    `VerifiedAccessTrustProviderProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-verifiedaccessinstance-verifiedaccesstrustprovider.html>`__
    """

    props: PropsDictType = {
        "Description": (str, False),
        "DeviceTrustProviderType": (str, False),
        "TrustProviderType": (str, False),
        "UserTrustProviderType": (str, False),
        "VerifiedAccessTrustProviderId": (str, False),
    }


class VerifiedAccessInstance(AWSObject):
    """
    `VerifiedAccessInstance <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-verifiedaccessinstance.html>`__
    """

    resource_type = "AWS::EC2::VerifiedAccessInstance"

    props: PropsDictType = {
        "CidrEndpointsCustomSubDomain": (str, False),
        "Description": (str, False),
        "FipsEnabled": (boolean, False),
        "LoggingConfigurations": (VerifiedAccessLogs, False),
        "Tags": (Tags, False),
        "VerifiedAccessTrustProviderIds": ([str], False),
        "VerifiedAccessTrustProviders": ([VerifiedAccessTrustProviderProperty], False),
    }


class DeviceOptions(AWSProperty):
    """
    `DeviceOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-verifiedaccesstrustprovider-deviceoptions.html>`__
    """

    props: PropsDictType = {
        "PublicSigningKeyUrl": (str, False),
        "TenantId": (str, False),
    }


class NativeApplicationOidcOptions(AWSProperty):
    """
    `NativeApplicationOidcOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-verifiedaccesstrustprovider-nativeapplicationoidcoptions.html>`__
    """

    props: PropsDictType = {
        "AuthorizationEndpoint": (str, False),
        "ClientId": (str, False),
        "ClientSecret": (str, False),
        "Issuer": (str, False),
        "PublicSigningKeyEndpoint": (str, False),
        "Scope": (str, False),
        "TokenEndpoint": (str, False),
        "UserInfoEndpoint": (str, False),
    }


class OidcOptions(AWSProperty):
    """
    `OidcOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-verifiedaccesstrustprovider-oidcoptions.html>`__
    """

    props: PropsDictType = {
        "AuthorizationEndpoint": (str, False),
        "ClientId": (str, False),
        "ClientSecret": (str, False),
        "Issuer": (str, False),
        "Scope": (str, False),
        "TokenEndpoint": (str, False),
        "UserInfoEndpoint": (str, False),
    }


class VerifiedAccessTrustProvider(AWSObject):
    """
    `VerifiedAccessTrustProvider <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-verifiedaccesstrustprovider.html>`__
    """

    resource_type = "AWS::EC2::VerifiedAccessTrustProvider"

    props: PropsDictType = {
        "Description": (str, False),
        "DeviceOptions": (DeviceOptions, False),
        "DeviceTrustProviderType": (str, False),
        "NativeApplicationOidcOptions": (NativeApplicationOidcOptions, False),
        "OidcOptions": (OidcOptions, False),
        "PolicyReferenceName": (str, True),
        "SseSpecification": (SseSpecification, False),
        "Tags": (Tags, False),
        "TrustProviderType": (str, True),
        "UserTrustProviderType": (str, False),
    }


class Volume(AWSObject):
    """
    `Volume <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-volume.html>`__
    """

    resource_type = "AWS::EC2::Volume"

    props: PropsDictType = {
        "AutoEnableIO": (boolean, False),
        "AvailabilityZone": (str, True),
        "Encrypted": (boolean, False),
        "Iops": (integer, False),
        "KmsKeyId": (str, False),
        "MultiAttachEnabled": (boolean, False),
        "OutpostArn": (str, False),
        "Size": (integer, False),
        "SnapshotId": (str, False),
        "Tags": (validate_tags_or_list, False),
        "Throughput": (integer, False),
        "VolumeType": (str, False),
    }


class VolumeAttachment(AWSObject):
    """
    `VolumeAttachment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ec2-volumeattachment.html>`__
    """

    resource_type = "AWS::EC2::VolumeAttachment"

    props: PropsDictType = {
        "Device": (str, False),
        "InstanceId": (str, True),
        "VolumeId": (str, True),
    }


class CapacityAllocation(AWSProperty):
    """
    `CapacityAllocation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-capacityreservation-capacityallocation.html>`__
    """

    props: PropsDictType = {
        "AllocationType": (str, False),
        "Count": (integer, False),
    }


class CommitmentInfo(AWSProperty):
    """
    `CommitmentInfo <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-capacityreservation-commitmentinfo.html>`__
    """

    props: PropsDictType = {
        "CommitmentEndDate": (str, False),
        "CommittedInstanceCount": (integer, False),
    }


class EbsBlockDevice(AWSProperty):
    """
    `EbsBlockDevice <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-spotfleet-ebsblockdevice.html>`__
    """

    props: PropsDictType = {
        "DeleteOnTermination": (boolean, False),
        "Encrypted": (boolean, False),
        "Iops": (integer, False),
        "SnapshotId": (str, False),
        "VolumeSize": (integer, False),
        "VolumeType": (str, False),
    }


class Egress(AWSProperty):
    """
    `Egress <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-securitygroup-egress.html>`__
    """

    props: PropsDictType = {
        "CidrIp": (str, False),
        "CidrIpv6": (str, False),
        "Description": (str, False),
        "DestinationPrefixListId": (str, False),
        "DestinationSecurityGroupId": (str, False),
        "FromPort": (integer, False),
        "IpProtocol": (str, True),
        "ToPort": (integer, False),
    }


class Ingress(AWSProperty):
    """
    `Ingress <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-securitygroup-ingress.html>`__
    """

    props: PropsDictType = {
        "CidrIp": (str, False),
        "CidrIpv6": (str, False),
        "Description": (str, False),
        "FromPort": (integer, False),
        "IpProtocol": (str, True),
        "SourcePrefixListId": (str, False),
        "SourceSecurityGroupId": (str, False),
        "SourceSecurityGroupName": (str, False),
        "SourceSecurityGroupOwnerId": (str, False),
        "ToPort": (integer, False),
    }


class MountPoint(AWSProperty):
    """
    `MountPoint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-instance-volume.html>`__
    """

    props: PropsDictType = {
        "Device": (str, True),
        "VolumeId": (str, True),
    }


class PeeringAttachmentStatus(AWSProperty):
    """
    `PeeringAttachmentStatus <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-transitgatewaypeeringattachment-peeringattachmentstatus.html>`__
    """

    props: PropsDictType = {
        "Code": (str, False),
        "Message": (str, False),
    }


class SecurityGroupRule(AWSProperty):
    """
    `SecurityGroupRule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-securitygroup-ingress.html>`__
    """

    props: PropsDictType = {
        "CidrIp": (str, False),
        "CidrIpv6": (str, False),
        "Description": (str, False),
        "DestinationPrefixListId": (str, False),
        "DestinationSecurityGroupId": (str, False),
        "FromPort": (validate_network_port, False),
        "IpProtocol": (str, True),
        "SourcePrefixListId": (str, False),
        "SourceSecurityGroupId": (str, False),
        "SourceSecurityGroupName": (str, False),
        "SourceSecurityGroupOwnerId": (str, False),
        "ToPort": (validate_network_port, False),
    }


class State(AWSProperty):
    """
    `State <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-instance-state.html>`__
    """

    props: PropsDictType = {
        "Code": (str, False),
        "Name": (str, False),
    }


class TransitGatewayRouteTableRoute(AWSProperty):
    """
    `TransitGatewayRouteTableRoute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-networkinsightsanalysis-transitgatewayroutetableroute.html>`__
    """

    props: PropsDictType = {
        "AttachmentId": (str, False),
        "DestinationCidr": (str, False),
        "PrefixListId": (str, False),
        "ResourceId": (str, False),
        "ResourceType": (str, False),
        "RouteOrigin": (str, False),
        "State": (str, False),
    }


class VolumeProperty(AWSProperty):
    """
    `VolumeProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-instance-volume.html>`__
    """

    props: PropsDictType = {
        "Device": (str, True),
        "VolumeId": (str, True),
    }
