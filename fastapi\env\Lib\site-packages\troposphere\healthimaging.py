# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, PropsDictType


class Datastore(AWSObject):
    """
    `Datastore <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-healthimaging-datastore.html>`__
    """

    resource_type = "AWS::HealthImaging::Datastore"

    props: PropsDictType = {
        "DatastoreName": (str, False),
        "KmsKeyArn": (str, False),
        "Tags": (dict, False),
    }
