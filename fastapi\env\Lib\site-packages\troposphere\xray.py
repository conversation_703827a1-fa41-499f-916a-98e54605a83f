# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer


class InsightsConfiguration(AWSProperty):
    """
    `InsightsConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-xray-group-insightsconfiguration.html>`__
    """

    props: PropsDictType = {
        "InsightsEnabled": (boolean, False),
        "NotificationsEnabled": (boolean, False),
    }


class Group(AWSObject):
    """
    `Group <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-xray-group.html>`__
    """

    resource_type = "AWS::XRay::Group"

    props: PropsDictType = {
        "FilterExpression": (str, False),
        "GroupName": (str, True),
        "InsightsConfiguration": (InsightsConfiguration, False),
        "Tags": (Tags, False),
    }


class ResourcePolicy(AWSObject):
    """
    `ResourcePolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-xray-resourcepolicy.html>`__
    """

    resource_type = "AWS::XRay::ResourcePolicy"

    props: PropsDictType = {
        "BypassPolicyLockoutCheck": (boolean, False),
        "PolicyDocument": (str, True),
        "PolicyName": (str, True),
    }


class SamplingRuleProperty(AWSProperty):
    """
    `SamplingRuleProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-xray-samplingrule-samplingrule.html>`__
    """

    props: PropsDictType = {
        "Attributes": (dict, False),
        "FixedRate": (double, True),
        "HTTPMethod": (str, True),
        "Host": (str, True),
        "Priority": (integer, True),
        "ReservoirSize": (integer, True),
        "ResourceARN": (str, True),
        "RuleARN": (str, False),
        "RuleName": (str, False),
        "ServiceName": (str, True),
        "ServiceType": (str, True),
        "URLPath": (str, True),
        "Version": (integer, False),
    }


class SamplingRule(AWSObject):
    """
    `SamplingRule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-xray-samplingrule.html>`__
    """

    resource_type = "AWS::XRay::SamplingRule"

    props: PropsDictType = {
        "SamplingRule": (SamplingRuleProperty, False),
        "Tags": (Tags, False),
    }


class TransactionSearchConfig(AWSObject):
    """
    `TransactionSearchConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-xray-transactionsearchconfig.html>`__
    """

    resource_type = "AWS::XRay::TransactionSearchConfig"

    props: PropsDictType = {
        "IndexingPercentage": (double, False),
    }
