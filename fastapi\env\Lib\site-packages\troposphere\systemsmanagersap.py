# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags


class ComponentInfo(AWSProperty):
    """
    `ComponentInfo <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-systemsmanagersap-application-componentinfo.html>`__
    """

    props: PropsDictType = {
        "ComponentType": (str, False),
        "Ec2InstanceId": (str, False),
        "Sid": (str, False),
    }


class Credential(AWSProperty):
    """
    `Credential <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-systemsmanagersap-application-credential.html>`__
    """

    props: PropsDictType = {
        "CredentialType": (str, False),
        "DatabaseName": (str, False),
        "SecretId": (str, False),
    }


class Application(AWSObject):
    """
    `Application <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-systemsmanagersap-application.html>`__
    """

    resource_type = "AWS::SystemsManagerSAP::Application"

    props: PropsDictType = {
        "ApplicationId": (str, True),
        "ApplicationType": (str, True),
        "ComponentsInfo": ([ComponentInfo], False),
        "Credentials": ([Credential], False),
        "DatabaseArn": (str, False),
        "Instances": ([str], False),
        "SapInstanceNumber": (str, False),
        "Sid": (str, False),
        "Tags": (Tags, False),
    }
