# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import double


class AttachmentsConfiguration(AWSProperty):
    """
    `AttachmentsConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-application-attachmentsconfiguration.html>`__
    """

    props: PropsDictType = {
        "AttachmentsControlMode": (str, True),
    }


class AutoSubscriptionConfiguration(AWSProperty):
    """
    `AutoSubscriptionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-application-autosubscriptionconfiguration.html>`__
    """

    props: PropsDictType = {
        "AutoSubscribe": (str, True),
        "DefaultSubscriptionType": (str, False),
    }


class EncryptionConfiguration(AWSProperty):
    """
    `EncryptionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-application-encryptionconfiguration.html>`__
    """

    props: PropsDictType = {
        "KmsKeyId": (str, False),
    }


class PersonalizationConfiguration(AWSProperty):
    """
    `PersonalizationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-application-personalizationconfiguration.html>`__
    """

    props: PropsDictType = {
        "PersonalizationControlMode": (str, True),
    }


class QAppsConfiguration(AWSProperty):
    """
    `QAppsConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-application-qappsconfiguration.html>`__
    """

    props: PropsDictType = {
        "QAppsControlMode": (str, True),
    }


class QuickSightConfiguration(AWSProperty):
    """
    `QuickSightConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-application-quicksightconfiguration.html>`__
    """

    props: PropsDictType = {
        "ClientNamespace": (str, True),
    }


class Application(AWSObject):
    """
    `Application <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-qbusiness-application.html>`__
    """

    resource_type = "AWS::QBusiness::Application"

    props: PropsDictType = {
        "AttachmentsConfiguration": (AttachmentsConfiguration, False),
        "AutoSubscriptionConfiguration": (AutoSubscriptionConfiguration, False),
        "ClientIdsForOIDC": ([str], False),
        "Description": (str, False),
        "DisplayName": (str, True),
        "EncryptionConfiguration": (EncryptionConfiguration, False),
        "IamIdentityProviderArn": (str, False),
        "IdentityCenterInstanceArn": (str, False),
        "IdentityType": (str, False),
        "PersonalizationConfiguration": (PersonalizationConfiguration, False),
        "QAppsConfiguration": (QAppsConfiguration, False),
        "QuickSightConfiguration": (QuickSightConfiguration, False),
        "RoleArn": (str, False),
        "Tags": (Tags, False),
    }


class DocumentAttributeValue(AWSProperty):
    """
    `DocumentAttributeValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-datasource-documentattributevalue.html>`__
    """

    props: PropsDictType = {
        "DateValue": (str, False),
        "LongValue": (double, False),
        "StringListValue": ([str], False),
        "StringValue": (str, False),
    }


class DocumentAttribute(AWSProperty):
    """
    `DocumentAttribute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-dataaccessor-documentattribute.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "Value": (DocumentAttributeValue, True),
    }


class AttributeFilter(AWSProperty):
    """
    `AttributeFilter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-dataaccessor-attributefilter.html>`__
    """

    props: PropsDictType = {
        "AndAllFilters": ([object], False),
        "ContainsAll": (DocumentAttribute, False),
        "ContainsAny": (DocumentAttribute, False),
        "EqualsTo": (DocumentAttribute, False),
        "GreaterThan": (DocumentAttribute, False),
        "GreaterThanOrEquals": (DocumentAttribute, False),
        "LessThan": (DocumentAttribute, False),
        "LessThanOrEquals": (DocumentAttribute, False),
        "NotFilter": (object, False),
        "OrAllFilters": ([object], False),
    }


class ActionFilterConfiguration(AWSProperty):
    """
    `ActionFilterConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-dataaccessor-actionfilterconfiguration.html>`__
    """

    props: PropsDictType = {
        "DocumentAttributeFilter": (AttributeFilter, True),
    }


class ActionConfiguration(AWSProperty):
    """
    `ActionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-dataaccessor-actionconfiguration.html>`__
    """

    props: PropsDictType = {
        "Action": (str, True),
        "FilterConfiguration": (ActionFilterConfiguration, False),
    }


class DataAccessor(AWSObject):
    """
    `DataAccessor <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-qbusiness-dataaccessor.html>`__
    """

    resource_type = "AWS::QBusiness::DataAccessor"

    props: PropsDictType = {
        "ActionConfigurations": ([ActionConfiguration], True),
        "ApplicationId": (str, True),
        "DisplayName": (str, True),
        "Principal": (str, True),
        "Tags": (Tags, False),
    }


class DataSourceVpcConfiguration(AWSProperty):
    """
    `DataSourceVpcConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-datasource-datasourcevpcconfiguration.html>`__
    """

    props: PropsDictType = {
        "SecurityGroupIds": ([str], True),
        "SubnetIds": ([str], True),
    }


class DocumentAttributeCondition(AWSProperty):
    """
    `DocumentAttributeCondition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-datasource-documentattributecondition.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Operator": (str, True),
        "Value": (DocumentAttributeValue, False),
    }


class HookConfiguration(AWSProperty):
    """
    `HookConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-datasource-hookconfiguration.html>`__
    """

    props: PropsDictType = {
        "InvocationCondition": (DocumentAttributeCondition, False),
        "LambdaArn": (str, False),
        "RoleArn": (str, False),
        "S3BucketName": (str, False),
    }


class DocumentAttributeTarget(AWSProperty):
    """
    `DocumentAttributeTarget <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-datasource-documentattributetarget.html>`__
    """

    props: PropsDictType = {
        "AttributeValueOperator": (str, False),
        "Key": (str, True),
        "Value": (DocumentAttributeValue, False),
    }


class InlineDocumentEnrichmentConfiguration(AWSProperty):
    """
    `InlineDocumentEnrichmentConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-datasource-inlinedocumentenrichmentconfiguration.html>`__
    """

    props: PropsDictType = {
        "Condition": (DocumentAttributeCondition, False),
        "DocumentContentOperator": (str, False),
        "Target": (DocumentAttributeTarget, False),
    }


class DocumentEnrichmentConfiguration(AWSProperty):
    """
    `DocumentEnrichmentConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-datasource-documentenrichmentconfiguration.html>`__
    """

    props: PropsDictType = {
        "InlineConfigurations": ([InlineDocumentEnrichmentConfiguration], False),
        "PostExtractionHookConfiguration": (HookConfiguration, False),
        "PreExtractionHookConfiguration": (HookConfiguration, False),
    }


class AudioExtractionConfiguration(AWSProperty):
    """
    `AudioExtractionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-datasource-audioextractionconfiguration.html>`__
    """

    props: PropsDictType = {
        "AudioExtractionStatus": (str, True),
    }


class ImageExtractionConfiguration(AWSProperty):
    """
    `ImageExtractionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-datasource-imageextractionconfiguration.html>`__
    """

    props: PropsDictType = {
        "ImageExtractionStatus": (str, True),
    }


class VideoExtractionConfiguration(AWSProperty):
    """
    `VideoExtractionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-datasource-videoextractionconfiguration.html>`__
    """

    props: PropsDictType = {
        "VideoExtractionStatus": (str, True),
    }


class MediaExtractionConfiguration(AWSProperty):
    """
    `MediaExtractionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-datasource-mediaextractionconfiguration.html>`__
    """

    props: PropsDictType = {
        "AudioExtractionConfiguration": (AudioExtractionConfiguration, False),
        "ImageExtractionConfiguration": (ImageExtractionConfiguration, False),
        "VideoExtractionConfiguration": (VideoExtractionConfiguration, False),
    }


class DataSource(AWSObject):
    """
    `DataSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-qbusiness-datasource.html>`__
    """

    resource_type = "AWS::QBusiness::DataSource"

    props: PropsDictType = {
        "ApplicationId": (str, True),
        "Configuration": (dict, True),
        "Description": (str, False),
        "DisplayName": (str, True),
        "DocumentEnrichmentConfiguration": (DocumentEnrichmentConfiguration, False),
        "IndexId": (str, True),
        "MediaExtractionConfiguration": (MediaExtractionConfiguration, False),
        "RoleArn": (str, False),
        "SyncSchedule": (str, False),
        "Tags": (Tags, False),
        "VpcConfiguration": (DataSourceVpcConfiguration, False),
    }


class DocumentAttributeConfiguration(AWSProperty):
    """
    `DocumentAttributeConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-index-documentattributeconfiguration.html>`__
    """

    props: PropsDictType = {
        "Name": (str, False),
        "Search": (str, False),
        "Type": (str, False),
    }


class IndexCapacityConfiguration(AWSProperty):
    """
    `IndexCapacityConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-index-indexcapacityconfiguration.html>`__
    """

    props: PropsDictType = {
        "Units": (double, False),
    }


class Index(AWSObject):
    """
    `Index <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-qbusiness-index.html>`__
    """

    resource_type = "AWS::QBusiness::Index"

    props: PropsDictType = {
        "ApplicationId": (str, True),
        "CapacityConfiguration": (IndexCapacityConfiguration, False),
        "Description": (str, False),
        "DisplayName": (str, True),
        "DocumentAttributeConfigurations": ([DocumentAttributeConfiguration], False),
        "Tags": (Tags, False),
        "Type": (str, False),
    }


class Permission(AWSObject):
    """
    `Permission <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-qbusiness-permission.html>`__
    """

    resource_type = "AWS::QBusiness::Permission"

    props: PropsDictType = {
        "Actions": ([str], True),
        "ApplicationId": (str, True),
        "Principal": (str, True),
        "StatementId": (str, True),
    }


class S3(AWSProperty):
    """
    `S3 <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-plugin-s3.html>`__
    """

    props: PropsDictType = {
        "Bucket": (str, True),
        "Key": (str, True),
    }


class APISchema(AWSProperty):
    """
    `APISchema <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-plugin-apischema.html>`__
    """

    props: PropsDictType = {
        "Payload": (str, False),
        "S3": (S3, False),
    }


class CustomPluginConfiguration(AWSProperty):
    """
    `CustomPluginConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-plugin-custompluginconfiguration.html>`__
    """

    props: PropsDictType = {
        "ApiSchema": (APISchema, True),
        "ApiSchemaType": (str, True),
        "Description": (str, True),
    }


class BasicAuthConfiguration(AWSProperty):
    """
    `BasicAuthConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-plugin-basicauthconfiguration.html>`__
    """

    props: PropsDictType = {
        "RoleArn": (str, True),
        "SecretArn": (str, True),
    }


class OAuth2ClientCredentialConfiguration(AWSProperty):
    """
    `OAuth2ClientCredentialConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-plugin-oauth2clientcredentialconfiguration.html>`__
    """

    props: PropsDictType = {
        "AuthorizationUrl": (str, False),
        "RoleArn": (str, True),
        "SecretArn": (str, True),
        "TokenUrl": (str, False),
    }


class PluginAuthConfiguration(AWSProperty):
    """
    `PluginAuthConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-plugin-pluginauthconfiguration.html>`__
    """

    props: PropsDictType = {
        "BasicAuthConfiguration": (BasicAuthConfiguration, False),
        "NoAuthConfiguration": (dict, False),
        "OAuth2ClientCredentialConfiguration": (
            OAuth2ClientCredentialConfiguration,
            False,
        ),
    }


class Plugin(AWSObject):
    """
    `Plugin <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-qbusiness-plugin.html>`__
    """

    resource_type = "AWS::QBusiness::Plugin"

    props: PropsDictType = {
        "ApplicationId": (str, False),
        "AuthConfiguration": (PluginAuthConfiguration, True),
        "CustomPluginConfiguration": (CustomPluginConfiguration, False),
        "DisplayName": (str, True),
        "ServerUrl": (str, False),
        "State": (str, False),
        "Tags": (Tags, False),
        "Type": (str, True),
    }


class KendraIndexConfiguration(AWSProperty):
    """
    `KendraIndexConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-retriever-kendraindexconfiguration.html>`__
    """

    props: PropsDictType = {
        "IndexId": (str, True),
    }


class NativeIndexConfiguration(AWSProperty):
    """
    `NativeIndexConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-retriever-nativeindexconfiguration.html>`__
    """

    props: PropsDictType = {
        "IndexId": (str, True),
    }


class RetrieverConfiguration(AWSProperty):
    """
    `RetrieverConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-retriever-retrieverconfiguration.html>`__
    """

    props: PropsDictType = {
        "KendraIndexConfiguration": (KendraIndexConfiguration, False),
        "NativeIndexConfiguration": (NativeIndexConfiguration, False),
    }


class Retriever(AWSObject):
    """
    `Retriever <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-qbusiness-retriever.html>`__
    """

    resource_type = "AWS::QBusiness::Retriever"

    props: PropsDictType = {
        "ApplicationId": (str, True),
        "Configuration": (RetrieverConfiguration, True),
        "DisplayName": (str, True),
        "RoleArn": (str, False),
        "Tags": (Tags, False),
        "Type": (str, True),
    }


class BrowserExtensionConfiguration(AWSProperty):
    """
    `BrowserExtensionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-webexperience-browserextensionconfiguration.html>`__
    """

    props: PropsDictType = {
        "EnabledBrowserExtensions": ([str], True),
    }


class CustomizationConfiguration(AWSProperty):
    """
    `CustomizationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-webexperience-customizationconfiguration.html>`__
    """

    props: PropsDictType = {
        "CustomCSSUrl": (str, False),
        "FaviconUrl": (str, False),
        "FontUrl": (str, False),
        "LogoUrl": (str, False),
    }


class OpenIDConnectProviderConfiguration(AWSProperty):
    """
    `OpenIDConnectProviderConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-webexperience-openidconnectproviderconfiguration.html>`__
    """

    props: PropsDictType = {
        "SecretsArn": (str, True),
        "SecretsRole": (str, True),
    }


class SamlProviderConfiguration(AWSProperty):
    """
    `SamlProviderConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-webexperience-samlproviderconfiguration.html>`__
    """

    props: PropsDictType = {
        "AuthenticationUrl": (str, True),
    }


class IdentityProviderConfiguration(AWSProperty):
    """
    `IdentityProviderConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-webexperience-identityproviderconfiguration.html>`__
    """

    props: PropsDictType = {
        "OpenIDConnectConfiguration": (OpenIDConnectProviderConfiguration, False),
        "SamlConfiguration": (SamlProviderConfiguration, False),
    }


class WebExperience(AWSObject):
    """
    `WebExperience <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-qbusiness-webexperience.html>`__
    """

    resource_type = "AWS::QBusiness::WebExperience"

    props: PropsDictType = {
        "ApplicationId": (str, True),
        "BrowserExtensionConfiguration": (BrowserExtensionConfiguration, False),
        "CustomizationConfiguration": (CustomizationConfiguration, False),
        "IdentityProviderConfiguration": (IdentityProviderConfiguration, False),
        "Origins": ([str], False),
        "RoleArn": (str, False),
        "SamplePromptsControlMode": (str, False),
        "Subtitle": (str, False),
        "Tags": (Tags, False),
        "Title": (str, False),
        "WelcomeMessage": (str, False),
    }


class TextDocumentStatistics(AWSProperty):
    """
    `TextDocumentStatistics <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-index-textdocumentstatistics.html>`__
    """

    props: PropsDictType = {
        "IndexedTextBytes": (double, False),
        "IndexedTextDocumentCount": (double, False),
    }


class IndexStatistics(AWSProperty):
    """
    `IndexStatistics <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-qbusiness-index-indexstatistics.html>`__
    """

    props: PropsDictType = {
        "TextDocumentStatistics": (TextDocumentStatistics, False),
    }
