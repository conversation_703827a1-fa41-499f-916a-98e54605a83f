# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags


class ColumnSchema(AWSProperty):
    """
    `ColumnSchema <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cleanroomsml-trainingdataset-columnschema.html>`__
    """

    props: PropsDictType = {
        "ColumnName": (str, True),
        "ColumnTypes": ([str], True),
    }


class GlueDataSource(AWSProperty):
    """
    `GlueDataSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cleanroomsml-trainingdataset-gluedatasource.html>`__
    """

    props: PropsDictType = {
        "CatalogId": (str, False),
        "DatabaseName": (str, True),
        "TableName": (str, True),
    }


class DataSource(AWSProperty):
    """
    `DataSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cleanroomsml-trainingdataset-datasource.html>`__
    """

    props: PropsDictType = {
        "GlueDataSource": (GlueDataSource, True),
    }


class DatasetInputConfig(AWSProperty):
    """
    `DatasetInputConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cleanroomsml-trainingdataset-datasetinputconfig.html>`__
    """

    props: PropsDictType = {
        "DataSource": (DataSource, True),
        "Schema": ([ColumnSchema], True),
    }


class Dataset(AWSProperty):
    """
    `Dataset <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cleanroomsml-trainingdataset-dataset.html>`__
    """

    props: PropsDictType = {
        "InputConfig": (DatasetInputConfig, True),
        "Type": (str, True),
    }


class TrainingDataset(AWSObject):
    """
    `TrainingDataset <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cleanroomsml-trainingdataset.html>`__
    """

    resource_type = "AWS::CleanRoomsML::TrainingDataset"

    props: PropsDictType = {
        "Description": (str, False),
        "Name": (str, True),
        "RoleArn": (str, True),
        "Tags": (Tags, False),
        "TrainingData": ([Dataset], True),
    }
