# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, integer


class AutoStartConfiguration(AWSProperty):
    """
    `AutoStartConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-autostartconfiguration.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, False),
    }


class AutoStopConfiguration(AWSProperty):
    """
    `AutoStopConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-autostopconfiguration.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, False),
        "IdleTimeoutMinutes": (integer, False),
    }


class ConfigurationObject(AWSProperty):
    """
    `ConfigurationObject <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-configurationobject.html>`__
    """

    props: PropsDictType = {
        "Classification": (str, True),
        "Properties": (dict, False),
    }


class ImageConfigurationInput(AWSProperty):
    """
    `ImageConfigurationInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-imageconfigurationinput.html>`__
    """

    props: PropsDictType = {
        "ImageUri": (str, False),
    }


class WorkerConfiguration(AWSProperty):
    """
    `WorkerConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-workerconfiguration.html>`__
    """

    props: PropsDictType = {
        "Cpu": (str, True),
        "Disk": (str, False),
        "DiskType": (str, False),
        "Memory": (str, True),
    }


class InitialCapacityConfig(AWSProperty):
    """
    `InitialCapacityConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-initialcapacityconfig.html>`__
    """

    props: PropsDictType = {
        "WorkerConfiguration": (WorkerConfiguration, True),
        "WorkerCount": (integer, True),
    }


class InitialCapacityConfigKeyValuePair(AWSProperty):
    """
    `InitialCapacityConfigKeyValuePair <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-initialcapacityconfigkeyvaluepair.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (InitialCapacityConfig, True),
    }


class InteractiveConfiguration(AWSProperty):
    """
    `InteractiveConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-interactiveconfiguration.html>`__
    """

    props: PropsDictType = {
        "LivyEndpointEnabled": (boolean, False),
        "StudioEnabled": (boolean, False),
    }


class MaximumAllowedResources(AWSProperty):
    """
    `MaximumAllowedResources <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-maximumallowedresources.html>`__
    """

    props: PropsDictType = {
        "Cpu": (str, True),
        "Disk": (str, False),
        "Memory": (str, True),
    }


class LogTypeMapKeyValuePair(AWSProperty):
    """
    `LogTypeMapKeyValuePair <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-logtypemapkeyvaluepair.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": ([str], True),
    }


class CloudWatchLoggingConfiguration(AWSProperty):
    """
    `CloudWatchLoggingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-cloudwatchloggingconfiguration.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, False),
        "EncryptionKeyArn": (str, False),
        "LogGroupName": (str, False),
        "LogStreamNamePrefix": (str, False),
        "LogTypeMap": ([LogTypeMapKeyValuePair], False),
    }


class ManagedPersistenceMonitoringConfiguration(AWSProperty):
    """
    `ManagedPersistenceMonitoringConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-managedpersistencemonitoringconfiguration.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, False),
        "EncryptionKeyArn": (str, False),
    }


class PrometheusMonitoringConfiguration(AWSProperty):
    """
    `PrometheusMonitoringConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-prometheusmonitoringconfiguration.html>`__
    """

    props: PropsDictType = {
        "RemoteWriteUrl": (str, False),
    }


class S3MonitoringConfiguration(AWSProperty):
    """
    `S3MonitoringConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-s3monitoringconfiguration.html>`__
    """

    props: PropsDictType = {
        "EncryptionKeyArn": (str, False),
        "LogUri": (str, False),
    }


class MonitoringConfiguration(AWSProperty):
    """
    `MonitoringConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-monitoringconfiguration.html>`__
    """

    props: PropsDictType = {
        "CloudWatchLoggingConfiguration": (CloudWatchLoggingConfiguration, False),
        "ManagedPersistenceMonitoringConfiguration": (
            ManagedPersistenceMonitoringConfiguration,
            False,
        ),
        "PrometheusMonitoringConfiguration": (PrometheusMonitoringConfiguration, False),
        "S3MonitoringConfiguration": (S3MonitoringConfiguration, False),
    }


class NetworkConfiguration(AWSProperty):
    """
    `NetworkConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-networkconfiguration.html>`__
    """

    props: PropsDictType = {
        "SecurityGroupIds": ([str], False),
        "SubnetIds": ([str], False),
    }


class SchedulerConfiguration(AWSProperty):
    """
    `SchedulerConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-schedulerconfiguration.html>`__
    """

    props: PropsDictType = {
        "MaxConcurrentRuns": (integer, False),
        "QueueTimeoutMinutes": (integer, False),
    }


class WorkerTypeSpecificationInput(AWSProperty):
    """
    `WorkerTypeSpecificationInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrserverless-application-workertypespecificationinput.html>`__
    """

    props: PropsDictType = {
        "ImageConfiguration": (ImageConfigurationInput, False),
    }


class Application(AWSObject):
    """
    `Application <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-emrserverless-application.html>`__
    """

    resource_type = "AWS::EMRServerless::Application"

    props: PropsDictType = {
        "Architecture": (str, False),
        "AutoStartConfiguration": (AutoStartConfiguration, False),
        "AutoStopConfiguration": (AutoStopConfiguration, False),
        "ImageConfiguration": (ImageConfigurationInput, False),
        "InitialCapacity": ([InitialCapacityConfigKeyValuePair], False),
        "InteractiveConfiguration": (InteractiveConfiguration, False),
        "MaximumCapacity": (MaximumAllowedResources, False),
        "MonitoringConfiguration": (MonitoringConfiguration, False),
        "Name": (str, False),
        "NetworkConfiguration": (NetworkConfiguration, False),
        "ReleaseLabel": (str, True),
        "RuntimeConfiguration": ([ConfigurationObject], False),
        "SchedulerConfiguration": (SchedulerConfiguration, False),
        "Tags": (Tags, False),
        "Type": (str, True),
        "WorkerTypeSpecifications": (dict, False),
    }
