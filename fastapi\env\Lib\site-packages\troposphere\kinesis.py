# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType
from .validators import integer
from .validators.kinesis import kinesis_stream_mode, validate_tags_or_list


class ResourcePolicy(AWSObject):
    """
    `ResourcePolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-kinesis-resourcepolicy.html>`__
    """

    resource_type = "AWS::Kinesis::ResourcePolicy"

    props: PropsDictType = {
        "ResourceArn": (str, True),
        "ResourcePolicy": (dict, True),
    }


class StreamEncryption(AWSProperty):
    """
    `StreamEncryption <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesis-stream-streamencryption.html>`__
    """

    props: PropsDictType = {
        "EncryptionType": (str, True),
        "KeyId": (str, True),
    }


class StreamModeDetails(AWSProperty):
    """
    `StreamModeDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesis-stream-streammodedetails.html>`__
    """

    props: PropsDictType = {
        "StreamMode": (kinesis_stream_mode, True),
    }


class Stream(AWSObject):
    """
    `Stream <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-kinesis-stream.html>`__
    """

    resource_type = "AWS::Kinesis::Stream"

    props: PropsDictType = {
        "DesiredShardLevelMetrics": ([str], False),
        "Name": (str, False),
        "RetentionPeriodHours": (integer, False),
        "ShardCount": (integer, False),
        "StreamEncryption": (StreamEncryption, False),
        "StreamModeDetails": (StreamModeDetails, False),
        "Tags": (validate_tags_or_list, False),
    }


class StreamConsumer(AWSObject):
    """
    `StreamConsumer <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-kinesis-streamconsumer.html>`__
    """

    resource_type = "AWS::Kinesis::StreamConsumer"

    props: PropsDictType = {
        "ConsumerName": (str, True),
        "StreamARN": (str, True),
    }
