# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, integer


class ScaleInPolicy(AWSProperty):
    """
    `ScaleInPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-scaleinpolicy.html>`__
    """

    props: PropsDictType = {
        "CpuUtilizationPercentage": (integer, True),
    }


class ScaleOutPolicy(AWSProperty):
    """
    `ScaleOutPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-scaleoutpolicy.html>`__
    """

    props: PropsDictType = {
        "CpuUtilizationPercentage": (integer, True),
    }


class AutoScaling(AWSProperty):
    """
    `AutoScaling <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-autoscaling.html>`__
    """

    props: PropsDictType = {
        "MaxWorkerCount": (integer, True),
        "McuCount": (integer, True),
        "MinWorkerCount": (integer, True),
        "ScaleInPolicy": (ScaleInPolicy, True),
        "ScaleOutPolicy": (ScaleOutPolicy, True),
    }


class ProvisionedCapacity(AWSProperty):
    """
    `ProvisionedCapacity <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-provisionedcapacity.html>`__
    """

    props: PropsDictType = {
        "McuCount": (integer, False),
        "WorkerCount": (integer, True),
    }


class Capacity(AWSProperty):
    """
    `Capacity <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-capacity.html>`__
    """

    props: PropsDictType = {
        "AutoScaling": (AutoScaling, False),
        "ProvisionedCapacity": (ProvisionedCapacity, False),
    }


class Vpc(AWSProperty):
    """
    `Vpc <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-vpc.html>`__
    """

    props: PropsDictType = {
        "SecurityGroups": ([str], True),
        "Subnets": ([str], True),
    }


class ApacheKafkaCluster(AWSProperty):
    """
    `ApacheKafkaCluster <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-apachekafkacluster.html>`__
    """

    props: PropsDictType = {
        "BootstrapServers": (str, True),
        "Vpc": (Vpc, True),
    }


class KafkaCluster(AWSProperty):
    """
    `KafkaCluster <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-kafkacluster.html>`__
    """

    props: PropsDictType = {
        "ApacheKafkaCluster": (ApacheKafkaCluster, True),
    }


class KafkaClusterClientAuthentication(AWSProperty):
    """
    `KafkaClusterClientAuthentication <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-kafkaclusterclientauthentication.html>`__
    """

    props: PropsDictType = {
        "AuthenticationType": (str, True),
    }


class KafkaClusterEncryptionInTransit(AWSProperty):
    """
    `KafkaClusterEncryptionInTransit <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-kafkaclusterencryptionintransit.html>`__
    """

    props: PropsDictType = {
        "EncryptionType": (str, True),
    }


class CloudWatchLogsLogDelivery(AWSProperty):
    """
    `CloudWatchLogsLogDelivery <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-cloudwatchlogslogdelivery.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, True),
        "LogGroup": (str, False),
    }


class FirehoseLogDelivery(AWSProperty):
    """
    `FirehoseLogDelivery <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-firehoselogdelivery.html>`__
    """

    props: PropsDictType = {
        "DeliveryStream": (str, False),
        "Enabled": (boolean, True),
    }


class S3LogDelivery(AWSProperty):
    """
    `S3LogDelivery <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-s3logdelivery.html>`__
    """

    props: PropsDictType = {
        "Bucket": (str, False),
        "Enabled": (boolean, True),
        "Prefix": (str, False),
    }


class WorkerLogDelivery(AWSProperty):
    """
    `WorkerLogDelivery <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-workerlogdelivery.html>`__
    """

    props: PropsDictType = {
        "CloudWatchLogs": (CloudWatchLogsLogDelivery, False),
        "Firehose": (FirehoseLogDelivery, False),
        "S3": (S3LogDelivery, False),
    }


class LogDelivery(AWSProperty):
    """
    `LogDelivery <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-logdelivery.html>`__
    """

    props: PropsDictType = {
        "WorkerLogDelivery": (WorkerLogDelivery, True),
    }


class CustomPluginProperty(AWSProperty):
    """
    `CustomPluginProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-customplugin.html>`__
    """

    props: PropsDictType = {
        "CustomPluginArn": (str, True),
        "Revision": (integer, True),
    }


class Plugin(AWSProperty):
    """
    `Plugin <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-plugin.html>`__
    """

    props: PropsDictType = {
        "CustomPlugin": (CustomPluginProperty, True),
    }


class WorkerConfigurationProperty(AWSProperty):
    """
    `WorkerConfigurationProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-connector-workerconfiguration.html>`__
    """

    props: PropsDictType = {
        "Revision": (integer, True),
        "WorkerConfigurationArn": (str, True),
    }


class Connector(AWSObject):
    """
    `Connector <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-kafkaconnect-connector.html>`__
    """

    resource_type = "AWS::KafkaConnect::Connector"

    props: PropsDictType = {
        "Capacity": (Capacity, True),
        "ConnectorConfiguration": (dict, True),
        "ConnectorDescription": (str, False),
        "ConnectorName": (str, True),
        "KafkaCluster": (KafkaCluster, True),
        "KafkaClusterClientAuthentication": (KafkaClusterClientAuthentication, True),
        "KafkaClusterEncryptionInTransit": (KafkaClusterEncryptionInTransit, True),
        "KafkaConnectVersion": (str, True),
        "LogDelivery": (LogDelivery, False),
        "Plugins": ([Plugin], True),
        "ServiceExecutionRoleArn": (str, True),
        "Tags": (Tags, False),
        "WorkerConfiguration": (WorkerConfigurationProperty, False),
    }


class S3Location(AWSProperty):
    """
    `S3Location <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-customplugin-s3location.html>`__
    """

    props: PropsDictType = {
        "BucketArn": (str, True),
        "FileKey": (str, True),
        "ObjectVersion": (str, False),
    }


class CustomPluginLocation(AWSProperty):
    """
    `CustomPluginLocation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-customplugin-custompluginlocation.html>`__
    """

    props: PropsDictType = {
        "S3Location": (S3Location, True),
    }


class CustomPlugin(AWSObject):
    """
    `CustomPlugin <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-kafkaconnect-customplugin.html>`__
    """

    resource_type = "AWS::KafkaConnect::CustomPlugin"

    props: PropsDictType = {
        "ContentType": (str, True),
        "Description": (str, False),
        "Location": (CustomPluginLocation, True),
        "Name": (str, True),
        "Tags": (Tags, False),
    }


class WorkerConfiguration(AWSObject):
    """
    `WorkerConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-kafkaconnect-workerconfiguration.html>`__
    """

    resource_type = "AWS::KafkaConnect::WorkerConfiguration"

    props: PropsDictType = {
        "Description": (str, False),
        "Name": (str, True),
        "PropertiesFileContent": (str, True),
        "Tags": (Tags, False),
    }


class CustomPluginFileDescription(AWSProperty):
    """
    `CustomPluginFileDescription <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kafkaconnect-customplugin-custompluginfiledescription.html>`__
    """

    props: PropsDictType = {
        "FileMd5": (str, False),
        "FileSize": (integer, False),
    }
