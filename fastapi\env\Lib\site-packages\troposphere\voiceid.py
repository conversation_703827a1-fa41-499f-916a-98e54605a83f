# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags


class ServerSideEncryptionConfiguration(AWSProperty):
    """
    `ServerSideEncryptionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-voiceid-domain-serversideencryptionconfiguration.html>`__
    """

    props: PropsDictType = {
        "KmsKeyId": (str, True),
    }


class Domain(AWSObject):
    """
    `Domain <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-voiceid-domain.html>`__
    """

    resource_type = "AWS::VoiceID::Domain"

    props: PropsDictType = {
        "Description": (str, False),
        "Name": (str, True),
        "ServerSideEncryptionConfiguration": (ServerSideEncryptionConfiguration, True),
        "Tags": (Tags, False),
    }
