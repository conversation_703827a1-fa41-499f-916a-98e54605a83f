# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType


class S3Location(AWSProperty):
    """
    `S3Location <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-simspaceweaver-simulation-s3location.html>`__
    """

    props: PropsDictType = {
        "BucketName": (str, True),
        "ObjectKey": (str, True),
    }


class Simulation(AWSObject):
    """
    `Simulation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-simspaceweaver-simulation.html>`__
    """

    resource_type = "AWS::SimSpaceWeaver::Simulation"

    props: PropsDictType = {
        "MaximumDuration": (str, False),
        "Name": (str, True),
        "RoleArn": (str, True),
        "SchemaS3Location": (S3Location, False),
        "SnapshotS3Location": (S3Location, False),
    }
