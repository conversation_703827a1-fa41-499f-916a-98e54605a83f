# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType


class LogAnomalyDetectionIntegration(AWSObject):
    """
    `LogAnomalyDetectionIntegration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-devopsguru-loganomalydetectionintegration.html>`__
    """

    resource_type = "AWS::DevOpsGuru::LogAnomalyDetectionIntegration"

    props: PropsDictType = {}


class NotificationFilterConfig(AWSProperty):
    """
    `NotificationFilterConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-devopsguru-notificationchannel-notificationfilterconfig.html>`__
    """

    props: PropsDictType = {
        "MessageTypes": ([str], False),
        "Severities": ([str], False),
    }


class SnsChannelConfig(AWSProperty):
    """
    `SnsChannelConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-devopsguru-notificationchannel-snschannelconfig.html>`__
    """

    props: PropsDictType = {
        "TopicArn": (str, False),
    }


class NotificationChannelConfig(AWSProperty):
    """
    `NotificationChannelConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-devopsguru-notificationchannel-notificationchannelconfig.html>`__
    """

    props: PropsDictType = {
        "Filters": (NotificationFilterConfig, False),
        "Sns": (SnsChannelConfig, False),
    }


class NotificationChannel(AWSObject):
    """
    `NotificationChannel <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-devopsguru-notificationchannel.html>`__
    """

    resource_type = "AWS::DevOpsGuru::NotificationChannel"

    props: PropsDictType = {
        "Config": (NotificationChannelConfig, True),
    }


class CloudFormationCollectionFilter(AWSProperty):
    """
    `CloudFormationCollectionFilter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-devopsguru-resourcecollection-cloudformationcollectionfilter.html>`__
    """

    props: PropsDictType = {
        "StackNames": ([str], False),
    }


class TagCollection(AWSProperty):
    """
    `TagCollection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-devopsguru-resourcecollection-tagcollection.html>`__
    """

    props: PropsDictType = {
        "AppBoundaryKey": (str, False),
        "TagValues": ([str], False),
    }


class ResourceCollectionFilter(AWSProperty):
    """
    `ResourceCollectionFilter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-devopsguru-resourcecollection-resourcecollectionfilter.html>`__
    """

    props: PropsDictType = {
        "CloudFormation": (CloudFormationCollectionFilter, False),
        "Tags": ([TagCollection], False),
    }


class ResourceCollection(AWSObject):
    """
    `ResourceCollection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-devopsguru-resourcecollection.html>`__
    """

    resource_type = "AWS::DevOpsGuru::ResourceCollection"

    props: PropsDictType = {
        "ResourceCollectionFilter": (ResourceCollectionFilter, True),
    }
