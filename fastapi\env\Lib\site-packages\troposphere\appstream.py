# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, integer
from .validators.appstream import validate_tags_or_list


class S3Location(AWSProperty):
    """
    `S3Location <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appstream-fleet-s3location.html>`__
    """

    props: PropsDictType = {
        "S3Bucket": (str, True),
        "S3Key": (str, True),
    }


class ScriptDetails(AWSProperty):
    """
    `ScriptDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appstream-appblock-scriptdetails.html>`__
    """

    props: PropsDictType = {
        "ExecutableParameters": (str, False),
        "ExecutablePath": (str, True),
        "ScriptS3Location": (S3Location, True),
        "TimeoutInSeconds": (integer, True),
    }


class AppBlock(AWSObject):
    """
    `AppBlock <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appstream-appblock.html>`__
    """

    resource_type = "AWS::AppStream::AppBlock"

    props: PropsDictType = {
        "Description": (str, False),
        "DisplayName": (str, False),
        "Name": (str, True),
        "PackagingType": (str, False),
        "PostSetupScriptDetails": (ScriptDetails, False),
        "SetupScriptDetails": (ScriptDetails, False),
        "SourceS3Location": (S3Location, True),
        "Tags": (validate_tags_or_list, False),
    }


class AccessEndpoint(AWSProperty):
    """
    `AccessEndpoint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appstream-stack-accessendpoint.html>`__
    """

    props: PropsDictType = {
        "EndpointType": (str, True),
        "VpceId": (str, True),
    }


class VpcConfig(AWSProperty):
    """
    `VpcConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appstream-imagebuilder-vpcconfig.html>`__
    """

    props: PropsDictType = {
        "SecurityGroupIds": ([str], False),
        "SubnetIds": ([str], False),
    }


class AppBlockBuilder(AWSObject):
    """
    `AppBlockBuilder <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appstream-appblockbuilder.html>`__
    """

    resource_type = "AWS::AppStream::AppBlockBuilder"

    props: PropsDictType = {
        "AccessEndpoints": ([AccessEndpoint], False),
        "AppBlockArns": ([str], False),
        "Description": (str, False),
        "DisplayName": (str, False),
        "EnableDefaultInternetAccess": (boolean, False),
        "IamRoleArn": (str, False),
        "InstanceType": (str, True),
        "Name": (str, True),
        "Platform": (str, True),
        "Tags": (Tags, False),
        "VpcConfig": (VpcConfig, True),
    }


class Application(AWSObject):
    """
    `Application <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appstream-application.html>`__
    """

    resource_type = "AWS::AppStream::Application"

    props: PropsDictType = {
        "AppBlockArn": (str, True),
        "AttributesToDelete": ([str], False),
        "Description": (str, False),
        "DisplayName": (str, False),
        "IconS3Location": (S3Location, True),
        "InstanceFamilies": ([str], True),
        "LaunchParameters": (str, False),
        "LaunchPath": (str, True),
        "Name": (str, True),
        "Platforms": ([str], True),
        "Tags": (validate_tags_or_list, False),
        "WorkingDirectory": (str, False),
    }


class ApplicationEntitlementAssociation(AWSObject):
    """
    `ApplicationEntitlementAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appstream-applicationentitlementassociation.html>`__
    """

    resource_type = "AWS::AppStream::ApplicationEntitlementAssociation"

    props: PropsDictType = {
        "ApplicationIdentifier": (str, True),
        "EntitlementName": (str, True),
        "StackName": (str, True),
    }


class ApplicationFleetAssociation(AWSObject):
    """
    `ApplicationFleetAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appstream-applicationfleetassociation.html>`__
    """

    resource_type = "AWS::AppStream::ApplicationFleetAssociation"

    props: PropsDictType = {
        "ApplicationArn": (str, True),
        "FleetName": (str, True),
    }


class CertificateBasedAuthProperties(AWSProperty):
    """
    `CertificateBasedAuthProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appstream-directoryconfig-certificatebasedauthproperties.html>`__
    """

    props: PropsDictType = {
        "CertificateAuthorityArn": (str, False),
        "Status": (str, False),
    }


class ServiceAccountCredentials(AWSProperty):
    """
    `ServiceAccountCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appstream-directoryconfig-serviceaccountcredentials.html>`__
    """

    props: PropsDictType = {
        "AccountName": (str, True),
        "AccountPassword": (str, True),
    }


class DirectoryConfig(AWSObject):
    """
    `DirectoryConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appstream-directoryconfig.html>`__
    """

    resource_type = "AWS::AppStream::DirectoryConfig"

    props: PropsDictType = {
        "CertificateBasedAuthProperties": (CertificateBasedAuthProperties, False),
        "DirectoryName": (str, True),
        "OrganizationalUnitDistinguishedNames": ([str], True),
        "ServiceAccountCredentials": (ServiceAccountCredentials, True),
    }


class Attribute(AWSProperty):
    """
    `Attribute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appstream-entitlement-attribute.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "Value": (str, True),
    }


class Entitlement(AWSObject):
    """
    `Entitlement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appstream-entitlement.html>`__
    """

    resource_type = "AWS::AppStream::Entitlement"

    props: PropsDictType = {
        "AppVisibility": (str, True),
        "Attributes": ([Attribute], True),
        "Description": (str, False),
        "Name": (str, True),
        "StackName": (str, True),
    }


class ComputeCapacity(AWSProperty):
    """
    `ComputeCapacity <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appstream-fleet-computecapacity.html>`__
    """

    props: PropsDictType = {
        "DesiredInstances": (integer, False),
        "DesiredSessions": (integer, False),
    }


class DomainJoinInfo(AWSProperty):
    """
    `DomainJoinInfo <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appstream-imagebuilder-domainjoininfo.html>`__
    """

    props: PropsDictType = {
        "DirectoryName": (str, False),
        "OrganizationalUnitDistinguishedName": (str, False),
    }


class Fleet(AWSObject):
    """
    `Fleet <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appstream-fleet.html>`__
    """

    resource_type = "AWS::AppStream::Fleet"

    props: PropsDictType = {
        "ComputeCapacity": (ComputeCapacity, False),
        "Description": (str, False),
        "DisconnectTimeoutInSeconds": (integer, False),
        "DisplayName": (str, False),
        "DomainJoinInfo": (DomainJoinInfo, False),
        "EnableDefaultInternetAccess": (boolean, False),
        "FleetType": (str, False),
        "IamRoleArn": (str, False),
        "IdleDisconnectTimeoutInSeconds": (integer, False),
        "ImageArn": (str, False),
        "ImageName": (str, False),
        "InstanceType": (str, True),
        "MaxConcurrentSessions": (integer, False),
        "MaxSessionsPerInstance": (integer, False),
        "MaxUserDurationInSeconds": (integer, False),
        "Name": (str, True),
        "Platform": (str, False),
        "SessionScriptS3Location": (S3Location, False),
        "StreamView": (str, False),
        "Tags": (validate_tags_or_list, False),
        "UsbDeviceFilterStrings": ([str], False),
        "VpcConfig": (VpcConfig, False),
    }


class ImageBuilder(AWSObject):
    """
    `ImageBuilder <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appstream-imagebuilder.html>`__
    """

    resource_type = "AWS::AppStream::ImageBuilder"

    props: PropsDictType = {
        "AccessEndpoints": ([AccessEndpoint], False),
        "AppstreamAgentVersion": (str, False),
        "Description": (str, False),
        "DisplayName": (str, False),
        "DomainJoinInfo": (DomainJoinInfo, False),
        "EnableDefaultInternetAccess": (boolean, False),
        "IamRoleArn": (str, False),
        "ImageArn": (str, False),
        "ImageName": (str, False),
        "InstanceType": (str, True),
        "Name": (str, True),
        "Tags": (validate_tags_or_list, False),
        "VpcConfig": (VpcConfig, False),
    }


class ApplicationSettings(AWSProperty):
    """
    `ApplicationSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appstream-stack-applicationsettings.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, True),
        "SettingsGroup": (str, False),
    }


class StorageConnector(AWSProperty):
    """
    `StorageConnector <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appstream-stack-storageconnector.html>`__
    """

    props: PropsDictType = {
        "ConnectorType": (str, True),
        "Domains": ([str], False),
        "ResourceIdentifier": (str, False),
    }


class StreamingExperienceSettings(AWSProperty):
    """
    `StreamingExperienceSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appstream-stack-streamingexperiencesettings.html>`__
    """

    props: PropsDictType = {
        "PreferredProtocol": (str, False),
    }


class UserSetting(AWSProperty):
    """
    `UserSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appstream-stack-usersetting.html>`__
    """

    props: PropsDictType = {
        "Action": (str, True),
        "MaximumLength": (integer, False),
        "Permission": (str, True),
    }


class Stack(AWSObject):
    """
    `Stack <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appstream-stack.html>`__
    """

    resource_type = "AWS::AppStream::Stack"

    props: PropsDictType = {
        "AccessEndpoints": ([AccessEndpoint], False),
        "ApplicationSettings": (ApplicationSettings, False),
        "AttributesToDelete": ([str], False),
        "DeleteStorageConnectors": (boolean, False),
        "Description": (str, False),
        "DisplayName": (str, False),
        "EmbedHostDomains": ([str], False),
        "FeedbackURL": (str, False),
        "Name": (str, False),
        "RedirectURL": (str, False),
        "StorageConnectors": ([StorageConnector], False),
        "StreamingExperienceSettings": (StreamingExperienceSettings, False),
        "Tags": (validate_tags_or_list, False),
        "UserSettings": ([UserSetting], False),
    }


class StackFleetAssociation(AWSObject):
    """
    `StackFleetAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appstream-stackfleetassociation.html>`__
    """

    resource_type = "AWS::AppStream::StackFleetAssociation"

    props: PropsDictType = {
        "FleetName": (str, True),
        "StackName": (str, True),
    }


class StackUserAssociation(AWSObject):
    """
    `StackUserAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appstream-stackuserassociation.html>`__
    """

    resource_type = "AWS::AppStream::StackUserAssociation"

    props: PropsDictType = {
        "AuthenticationType": (str, True),
        "SendEmailNotification": (boolean, False),
        "StackName": (str, True),
        "UserName": (str, True),
    }


class User(AWSObject):
    """
    `User <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appstream-user.html>`__
    """

    resource_type = "AWS::AppStream::User"

    props: PropsDictType = {
        "AuthenticationType": (str, True),
        "FirstName": (str, False),
        "LastName": (str, False),
        "MessageAction": (str, False),
        "UserName": (str, True),
    }
