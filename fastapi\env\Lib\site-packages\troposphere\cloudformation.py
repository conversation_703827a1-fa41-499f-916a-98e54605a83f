# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, integer
from .validators.cloudformation import Authentication  # noqa: F401
from .validators.cloudformation import AuthenticationBlock  # noqa: F401
from .validators.cloudformation import AWSCustomObject  # noqa: F401
from .validators.cloudformation import Init  # noqa: F401
from .validators.cloudformation import InitConfig  # noqa: F401
from .validators.cloudformation import InitConfigSets  # noqa: F401
from .validators.cloudformation import InitFile  # noqa: F401
from .validators.cloudformation import InitFileContext  # noqa: F401
from .validators.cloudformation import InitFiles  # noqa: F401
from .validators.cloudformation import InitService  # noqa: F401
from .validators.cloudformation import InitServices  # noqa: F401
from .validators.cloudformation import Metadata  # noqa: F401
from .validators.cloudformation import validate_int_to_str, validate_wait_condition


class CustomResource(AWSObject):
    """
    `CustomResource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cfn-customresource.html>`__
    """

    resource_type = "AWS::CloudFormation::CustomResource"

    props: PropsDictType = {
        "ServiceTimeout": (integer, False),
        "ServiceToken": (str, True),
    }


class S3Location(AWSProperty):
    """
    `S3Location <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cloudformation-guardhook-s3location.html>`__
    """

    props: PropsDictType = {
        "Uri": (str, True),
        "VersionId": (str, False),
    }


class Options(AWSProperty):
    """
    `Options <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cloudformation-guardhook-options.html>`__
    """

    props: PropsDictType = {
        "InputParams": (S3Location, False),
    }


class StackNames(AWSProperty):
    """
    `StackNames <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cloudformation-lambdahook-stacknames.html>`__
    """

    props: PropsDictType = {
        "Exclude": ([str], False),
        "Include": ([str], False),
    }


class StackRoles(AWSProperty):
    """
    `StackRoles <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cloudformation-lambdahook-stackroles.html>`__
    """

    props: PropsDictType = {
        "Exclude": ([str], False),
        "Include": ([str], False),
    }


class StackFilters(AWSProperty):
    """
    `StackFilters <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cloudformation-lambdahook-stackfilters.html>`__
    """

    props: PropsDictType = {
        "FilteringCriteria": (str, True),
        "StackNames": (StackNames, False),
        "StackRoles": (StackRoles, False),
    }


class HookTarget(AWSProperty):
    """
    `HookTarget <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cloudformation-lambdahook-hooktarget.html>`__
    """

    props: PropsDictType = {
        "Action": (str, True),
        "InvocationPoint": (str, True),
        "TargetName": (str, True),
    }


class TargetFilters(AWSProperty):
    """
    `TargetFilters <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cloudformation-lambdahook-targetfilters.html>`__
    """

    props: PropsDictType = {
        "Actions": ([str], False),
        "InvocationPoints": ([str], False),
        "TargetNames": ([str], False),
        "Targets": ([HookTarget], False),
    }


class GuardHook(AWSObject):
    """
    `GuardHook <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cloudformation-guardhook.html>`__
    """

    resource_type = "AWS::CloudFormation::GuardHook"

    props: PropsDictType = {
        "Alias": (str, True),
        "ExecutionRole": (str, True),
        "FailureMode": (str, True),
        "HookStatus": (str, True),
        "LogBucket": (str, False),
        "Options": (Options, False),
        "RuleLocation": (S3Location, True),
        "StackFilters": (StackFilters, False),
        "TargetFilters": (TargetFilters, False),
        "TargetOperations": ([str], True),
    }


class HookDefaultVersion(AWSObject):
    """
    `HookDefaultVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cloudformation-hookdefaultversion.html>`__
    """

    resource_type = "AWS::CloudFormation::HookDefaultVersion"

    props: PropsDictType = {
        "TypeName": (str, False),
        "TypeVersionArn": (str, False),
        "VersionId": (str, False),
    }


class HookTypeConfig(AWSObject):
    """
    `HookTypeConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cloudformation-hooktypeconfig.html>`__
    """

    resource_type = "AWS::CloudFormation::HookTypeConfig"

    props: PropsDictType = {
        "Configuration": (str, True),
        "ConfigurationAlias": (str, False),
        "TypeArn": (str, False),
        "TypeName": (str, False),
    }


class LoggingConfig(AWSProperty):
    """
    `LoggingConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cloudformation-typeactivation-loggingconfig.html>`__
    """

    props: PropsDictType = {
        "LogGroupName": (str, False),
        "LogRoleArn": (str, False),
    }


class HookVersion(AWSObject):
    """
    `HookVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cloudformation-hookversion.html>`__
    """

    resource_type = "AWS::CloudFormation::HookVersion"

    props: PropsDictType = {
        "ExecutionRoleArn": (str, False),
        "LoggingConfig": (LoggingConfig, False),
        "SchemaHandlerPackage": (str, True),
        "TypeName": (str, True),
    }


class LambdaHook(AWSObject):
    """
    `LambdaHook <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cloudformation-lambdahook.html>`__
    """

    resource_type = "AWS::CloudFormation::LambdaHook"

    props: PropsDictType = {
        "Alias": (str, True),
        "ExecutionRole": (str, True),
        "FailureMode": (str, True),
        "HookStatus": (str, True),
        "LambdaFunction": (str, True),
        "StackFilters": (StackFilters, False),
        "TargetFilters": (TargetFilters, False),
        "TargetOperations": ([str], True),
    }


class Macro(AWSObject):
    """
    `Macro <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cloudformation-macro.html>`__
    """

    resource_type = "AWS::CloudFormation::Macro"

    props: PropsDictType = {
        "Description": (str, False),
        "FunctionName": (str, True),
        "LogGroupName": (str, False),
        "LogRoleARN": (str, False),
        "Name": (str, True),
    }


class ModuleDefaultVersion(AWSObject):
    """
    `ModuleDefaultVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cloudformation-moduledefaultversion.html>`__
    """

    resource_type = "AWS::CloudFormation::ModuleDefaultVersion"

    props: PropsDictType = {
        "Arn": (str, False),
        "ModuleName": (str, False),
        "VersionId": (str, False),
    }


class ModuleVersion(AWSObject):
    """
    `ModuleVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cloudformation-moduleversion.html>`__
    """

    resource_type = "AWS::CloudFormation::ModuleVersion"

    props: PropsDictType = {
        "ModuleName": (str, True),
        "ModulePackage": (str, True),
    }


class PublicTypeVersion(AWSObject):
    """
    `PublicTypeVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cloudformation-publictypeversion.html>`__
    """

    resource_type = "AWS::CloudFormation::PublicTypeVersion"

    props: PropsDictType = {
        "Arn": (str, False),
        "LogDeliveryBucket": (str, False),
        "PublicVersionNumber": (str, False),
        "Type": (str, False),
        "TypeName": (str, False),
    }


class Publisher(AWSObject):
    """
    `Publisher <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cloudformation-publisher.html>`__
    """

    resource_type = "AWS::CloudFormation::Publisher"

    props: PropsDictType = {
        "AcceptTermsAndConditions": (boolean, True),
        "ConnectionArn": (str, False),
    }


class ResourceDefaultVersion(AWSObject):
    """
    `ResourceDefaultVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cloudformation-resourcedefaultversion.html>`__
    """

    resource_type = "AWS::CloudFormation::ResourceDefaultVersion"

    props: PropsDictType = {
        "TypeName": (str, False),
        "TypeVersionArn": (str, False),
        "VersionId": (str, False),
    }


class ResourceVersion(AWSObject):
    """
    `ResourceVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cloudformation-resourceversion.html>`__
    """

    resource_type = "AWS::CloudFormation::ResourceVersion"

    props: PropsDictType = {
        "ExecutionRoleArn": (str, False),
        "LoggingConfig": (LoggingConfig, False),
        "SchemaHandlerPackage": (str, True),
        "TypeName": (str, True),
    }


class Stack(AWSObject):
    """
    `Stack <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-stack.html>`__
    """

    resource_type = "AWS::CloudFormation::Stack"

    props: PropsDictType = {
        "NotificationARNs": ([str], False),
        "Parameters": (dict, False),
        "Tags": (Tags, False),
        "TemplateURL": (str, True),
        "TimeoutInMinutes": (integer, False),
    }


class AutoDeployment(AWSProperty):
    """
    `AutoDeployment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cloudformation-stackset-autodeployment.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, False),
        "RetainStacksOnAccountRemoval": (boolean, False),
    }


class ManagedExecution(AWSProperty):
    """
    `ManagedExecution <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cloudformation-stackset-managedexecution.html>`__
    """

    props: PropsDictType = {
        "Active": (boolean, False),
    }


class OperationPreferences(AWSProperty):
    """
    `OperationPreferences <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cloudformation-stackset-operationpreferences.html>`__
    """

    props: PropsDictType = {
        "ConcurrencyMode": (str, False),
        "FailureToleranceCount": (integer, False),
        "FailureTolerancePercentage": (integer, False),
        "MaxConcurrentCount": (integer, False),
        "MaxConcurrentPercentage": (integer, False),
        "RegionConcurrencyType": (str, False),
        "RegionOrder": ([str], False),
    }


class Parameter(AWSProperty):
    """
    `Parameter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cloudformation-stackset-parameter.html>`__
    """

    props: PropsDictType = {
        "ParameterKey": (str, True),
        "ParameterValue": (str, True),
    }


class DeploymentTargets(AWSProperty):
    """
    `DeploymentTargets <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cloudformation-stackset-deploymenttargets.html>`__
    """

    props: PropsDictType = {
        "AccountFilterType": (str, False),
        "Accounts": ([str], False),
        "AccountsUrl": (str, False),
        "OrganizationalUnitIds": ([str], False),
    }


class StackInstances(AWSProperty):
    """
    `StackInstances <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cloudformation-stackset-stackinstances.html>`__
    """

    props: PropsDictType = {
        "DeploymentTargets": (DeploymentTargets, True),
        "ParameterOverrides": ([Parameter], False),
        "Regions": ([str], True),
    }


class StackSet(AWSObject):
    """
    `StackSet <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cloudformation-stackset.html>`__
    """

    resource_type = "AWS::CloudFormation::StackSet"

    props: PropsDictType = {
        "AdministrationRoleARN": (str, False),
        "AutoDeployment": (AutoDeployment, False),
        "CallAs": (str, False),
        "Capabilities": ([str], False),
        "Description": (str, False),
        "ExecutionRoleName": (str, False),
        "ManagedExecution": (ManagedExecution, False),
        "OperationPreferences": (OperationPreferences, False),
        "Parameters": ([Parameter], False),
        "PermissionModel": (str, True),
        "StackInstancesGroup": ([StackInstances], False),
        "StackSetName": (str, True),
        "Tags": (Tags, False),
        "TemplateBody": (str, False),
        "TemplateURL": (str, False),
    }


class TypeActivation(AWSObject):
    """
    `TypeActivation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cloudformation-typeactivation.html>`__
    """

    resource_type = "AWS::CloudFormation::TypeActivation"

    props: PropsDictType = {
        "AutoUpdate": (boolean, False),
        "ExecutionRoleArn": (str, False),
        "LoggingConfig": (LoggingConfig, False),
        "MajorVersion": (str, False),
        "PublicTypeArn": (str, False),
        "PublisherId": (str, False),
        "Type": (str, False),
        "TypeName": (str, False),
        "TypeNameAlias": (str, False),
        "VersionBump": (str, False),
    }


class WaitCondition(AWSObject):
    """
    `WaitCondition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-waitcondition.html>`__
    """

    resource_type = "AWS::CloudFormation::WaitCondition"

    props: PropsDictType = {
        "Count": (integer, False),
        "Handle": (str, False),
        "Timeout": (validate_int_to_str, False),
    }

    def validate(self):
        validate_wait_condition(self)


class WaitConditionHandle(AWSObject):
    """
    `WaitConditionHandle <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-waitconditionhandle.html>`__
    """

    resource_type = "AWS::CloudFormation::WaitConditionHandle"

    props: PropsDictType = {}
