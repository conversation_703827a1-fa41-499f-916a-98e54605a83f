# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, integer


class IdentityProviderConfiguration(AWSProperty):
    """
    `IdentityProviderConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-healthlake-fhirdatastore-identityproviderconfiguration.html>`__
    """

    props: PropsDictType = {
        "AuthorizationStrategy": (str, True),
        "FineGrainedAuthorizationEnabled": (boolean, False),
        "IdpLambdaArn": (str, False),
        "Metadata": (str, False),
    }


class PreloadDataConfig(AWSProperty):
    """
    `PreloadDataConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-healthlake-fhirdatastore-preloaddataconfig.html>`__
    """

    props: PropsDictType = {
        "PreloadDataType": (str, True),
    }


class KmsEncryptionConfig(AWSProperty):
    """
    `KmsEncryptionConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-healthlake-fhirdatastore-kmsencryptionconfig.html>`__
    """

    props: PropsDictType = {
        "CmkType": (str, True),
        "KmsKeyId": (str, False),
    }


class SseConfiguration(AWSProperty):
    """
    `SseConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-healthlake-fhirdatastore-sseconfiguration.html>`__
    """

    props: PropsDictType = {
        "KmsEncryptionConfig": (KmsEncryptionConfig, True),
    }


class FHIRDatastore(AWSObject):
    """
    `FHIRDatastore <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-healthlake-fhirdatastore.html>`__
    """

    resource_type = "AWS::HealthLake::FHIRDatastore"

    props: PropsDictType = {
        "DatastoreName": (str, False),
        "DatastoreTypeVersion": (str, True),
        "IdentityProviderConfiguration": (IdentityProviderConfiguration, False),
        "PreloadDataConfig": (PreloadDataConfig, False),
        "SseConfiguration": (SseConfiguration, False),
        "Tags": (Tags, False),
    }


class CreatedAt(AWSProperty):
    """
    `CreatedAt <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-healthlake-fhirdatastore-createdat.html>`__
    """

    props: PropsDictType = {
        "Nanos": (integer, True),
        "Seconds": (str, True),
    }
