# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import integer


class ProfilePermission(AWSObject):
    """
    `ProfilePermission <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-signer-profilepermission.html>`__
    """

    resource_type = "AWS::Signer::ProfilePermission"

    props: PropsDictType = {
        "Action": (str, True),
        "Principal": (str, True),
        "ProfileName": (str, True),
        "ProfileVersion": (str, False),
        "StatementId": (str, True),
    }


class SignatureValidityPeriod(AWSProperty):
    """
    `SignatureValidityPeriod <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-signer-signingprofile-signaturevalidityperiod.html>`__
    """

    props: PropsDictType = {
        "Type": (str, False),
        "Value": (integer, False),
    }


class SigningProfile(AWSObject):
    """
    `SigningProfile <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-signer-signingprofile.html>`__
    """

    resource_type = "AWS::Signer::SigningProfile"

    props: PropsDictType = {
        "PlatformId": (str, True),
        "SignatureValidityPeriod": (SignatureValidityPeriod, False),
        "Tags": (Tags, False),
    }
