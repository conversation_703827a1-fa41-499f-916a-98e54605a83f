# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags


class EksInfo(AWSProperty):
    """
    `EksInfo <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrcontainers-virtualcluster-eksinfo.html>`__
    """

    props: PropsDictType = {
        "Namespace": (str, True),
    }


class ContainerInfo(AWSProperty):
    """
    `ContainerInfo <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrcontainers-virtualcluster-containerinfo.html>`__
    """

    props: PropsDictType = {
        "EksInfo": (EksInfo, True),
    }


class ContainerProvider(AWSProperty):
    """
    `ContainerProvider <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-emrcontainers-virtualcluster-containerprovider.html>`__
    """

    props: PropsDictType = {
        "Id": (str, True),
        "Info": (ContainerInfo, True),
        "Type": (str, True),
    }


class VirtualCluster(AWSObject):
    """
    `VirtualCluster <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-emrcontainers-virtualcluster.html>`__
    """

    resource_type = "AWS::EMRContainers::VirtualCluster"

    props: PropsDictType = {
        "ContainerProvider": (ContainerProvider, True),
        "Name": (str, True),
        "SecurityConfigurationId": (str, False),
        "Tags": (Tags, False),
    }
