from .type_defs.compat import Final

#
# Regions
#

AF_SOUTH_1: Final = "af-south-1"
AP_EAST_1: Final = "ap-east-1"
AP_NORTHEAST_1: Final = "ap-northeast-1"
AP_NORTHEAST_2: Final = "ap-northeast-2"
AP_NORTHEAST_3: Final = "ap-northeast-3"
AP_SOUTHEAST_1: Final = "ap-southeast-1"
AP_SOUTHEAST_2: Final = "ap-southeast-2"
AP_SOUTHEAST_3: Final = "ap-southeast-3"
AP_SOUTH_1: Final = "ap-south-1"
CA_CENTRAL_1: Final = "ca-central-1"
CN_NORTH_1: Final = "cn-north-1"
CN_NORTHWEST_1: Final = "cn-northwest-1"
EU_WEST_1: Final = "eu-west-1"
EU_WEST_2: Final = "eu-west-2"
EU_WEST_3: Final = "eu-west-3"
EU_CENTRAL_1: Final = "eu-central-1"
EU_NORTH_1: Final = "eu-north-1"
EU_SOUTH_1: Final = "eu-south-1"
ME_CENTRAL_1: Final = "me-central-1"
ME_SOUTH_1: Final = "me-south-1"
SA_EAST_1: Final = "sa-east-1"
US_EAST_1: Final = "us-east-1"
US_EAST_2: Final = "us-east-2"
US_GOV_EAST_1: Final = "us-gov-east-1"
US_GOV_WEST_1: Final = "us-gov-west-1"
US_WEST_1: Final = "us-west-1"
US_WEST_2: Final = "us-west-2"

#
# Availability Zones
#

AF_SOUTH_1A: Final = "af-south-1a"
AF_SOUTH_1B: Final = "af-south-1b"
AF_SOUTH_1C: Final = "af-south-1c"

AP_EAST_1A: Final = "ap-east-1a"
AP_EAST_1B: Final = "ap-east-1b"
AP_EAST_1C: Final = "ap-east-1c"

AP_NORTHEAST_1A: Final = "ap-northeast-1a"
AP_NORTHEAST_1B: Final = "ap-northeast-1b"
AP_NORTHEAST_1C: Final = "ap-northeast-1c"
AP_NORTHEAST_1D: Final = "ap-northeast-1d"

AP_NORTHEAST_2A: Final = "ap-northeast-2a"
AP_NORTHEAST_2B: Final = "ap-northeast-2b"
AP_NORTHEAST_2C: Final = "ap-northeast-2c"
AP_NORTHEAST_2D: Final = "ap-northeast-2d"

AP_NORTHEAST_3A: Final = "ap-northeast-3a"
AP_NORTHEAST_3B: Final = "ap-northeast-3b"
AP_NORTHEAST_3C: Final = "ap-northeast-3c"

AP_SOUTHEAST_1A: Final = "ap-southeast-1a"
AP_SOUTHEAST_1B: Final = "ap-southeast-1b"
AP_SOUTHEAST_1C: Final = "ap-southeast-1c"

AP_SOUTHEAST_2A: Final = "ap-southeast-2a"
AP_SOUTHEAST_2B: Final = "ap-southeast-2b"
AP_SOUTHEAST_2C: Final = "ap-southeast-2c"

AP_SOUTHEAST_3A: Final = "ap-southeast-3a"
AP_SOUTHEAST_3B: Final = "ap-southeast-3b"
AP_SOUTHEAST_3C: Final = "ap-southeast-3c"

AP_SOUTH_1A: Final = "ap-south-1a"
AP_SOUTH_1B: Final = "ap-south-1b"
AP_SOUTH_1C: Final = "ap-south-1c"

CA_CENTRAL_1A: Final = "ca-central-1a"
CA_CENTRAL_1B: Final = "ca-central-1b"
CA_CENTRAL_1D: Final = "ca-central-1d"

CN_NORTH_1A: Final = "cn-north-1a"
CN_NORTH_1B: Final = "cn-north-1b"
CN_NORTH_1C: Final = "cn-north-1c"

CN_NORTHWEST_1A: Final = "cn-northwest-1a"
CN_NORTHWEST_1B: Final = "cn-northwest-1b"
CN_NORTHWEST_1C: Final = "cn-northwest-1c"

EU_WEST_1A: Final = "eu-west-1a"
EU_WEST_1B: Final = "eu-west-1b"
EU_WEST_1C: Final = "eu-west-1c"

EU_WEST_2A: Final = "eu-west-2a"
EU_WEST_2B: Final = "eu-west-2b"
EU_WEST_2C: Final = "eu-west-2c"

EU_WEST_3A: Final = "eu-west-3a"
EU_WEST_3B: Final = "eu-west-3b"
EU_WEST_3C: Final = "eu-west-3c"

EU_CENTRAL_1A: Final = "eu-central-1a"
EU_CENTRAL_1B: Final = "eu-central-1b"
EU_CENTRAL_1C: Final = "eu-central-1c"

EU_NORTH_1A: Final = "eu-north-1a"
EU_NORTH_1B: Final = "eu-north-1b"
EU_NORTH_1C: Final = "eu-north-1c"

EU_SOUTH_1A: Final = "eu-south-1a"
EU_SOUTH_1B: Final = "eu-south-1b"
EU_SOUTH_1C: Final = "eu-south-1c"

ME_CENTRAL_1A: Final = "me-central-1a"
ME_CENTRAL_1B: Final = "me-central-1b"
ME_CENTRAL_1C: Final = "me-central-1c"

ME_SOUTH_1A: Final = "me-south-1a"
ME_SOUTH_1B: Final = "me-south-1b"
ME_SOUTH_1C: Final = "me-south-1c"

SA_EAST_1A: Final = "sa-east-1a"
SA_EAST_1B: Final = "sa-east-1b"
SA_EAST_1C: Final = "sa-east-1c"

US_EAST_1A: Final = "us-east-1a"
US_EAST_1B: Final = "us-east-1b"
US_EAST_1C: Final = "us-east-1c"
US_EAST_1D: Final = "us-east-1d"
US_EAST_1E: Final = "us-east-1e"
US_EAST_1F: Final = "us-east-1f"

US_EAST_2A: Final = "us-east-2a"
US_EAST_2B: Final = "us-east-2b"
US_EAST_2C: Final = "us-east-2c"

US_WEST_1A: Final = "us-west-1a"
US_WEST_1B: Final = "us-west-1b"
US_WEST_1C: Final = "us-west-1c"

US_GOV_EAST_1A: Final = "us-gov-east-1a"
US_GOV_EAST_1B: Final = "us-gov-east-1b"
US_GOV_EAST_1C: Final = "us-gov-east-1c"

US_GOV_WEST_1A: Final = "us-gov-west-1a"
US_GOV_WEST_1B: Final = "us-gov-west-1b"
US_GOV_WEST_1C: Final = "us-gov-west-1c"

US_WEST_2A: Final = "us-west-2a"
US_WEST_2B: Final = "us-west-2b"
US_WEST_2C: Final = "us-west-2c"
US_WEST_2D: Final = "us-west-2d"

#
# Networking
#

QUAD_ZERO: Final = "0.0.0.0/0"
VPC_CIDR_16: Final = "10.0.0.0/16"

SSH_PORT: Final = 22
MONGODB_PORT: Final = 27017
NTP_PORT: Final = 123
SMTP_PORT_25: Final = 25
SMTP_PORT_587: Final = 587
HTTP_PORT: Final = 80
HTTPS_PORT: Final = 443
REDIS_PORT: Final = 6379
MEMCACHED_PORT: Final = 11211
POSTGRESQL_PORT: Final = 5432

TCP_PROTOCOL: Final = 6
UDP_PROTOCOL: Final = 17
ICMP_PROTOCOL: Final = 1
ALL_PROTOCOL: Final = -1

#
# EC2 instance types
#

T2_NANO: Final = "t2.nano"
T2_MICRO: Final = "t2.micro"
T2_SMALL: Final = "t2.small"
T2_MEDIUM: Final = "t2.medium"
T2_LARGE: Final = "t2.large"
T2_XLARGE: Final = "t2.xlarge"
T2_2XLARGE: Final = "t2.2xlarge"

T3_NANO: Final = "t3.nano"
T3_MICRO: Final = "t3.micro"
T3_SMALL: Final = "t3.small"
T3_MEDIUM: Final = "t3.medium"
T3_LARGE: Final = "t3.large"
T3_XLARGE: Final = "t3.xlarge"
T3_2XLARGE: Final = "t3.2xlarge"

T3A_NANO: Final = "t3a.nano"
T3A_MICRO: Final = "t3a.micro"
T3A_SMALL: Final = "t3a.small"
T3A_MEDIUM: Final = "t3a.medium"
T3A_LARGE: Final = "t3a.large"
T3A_XLARGE: Final = "t3a.xlarge"
T3A_2XLARGE: Final = "t3a.2xlarge"

T4G_NANO: Final = "t4g.nano"
T4G_MICRO: Final = "t4g.micro"
T4G_SMALL: Final = "t4g.small"
T4G_MEDIUM: Final = "t4g.medium"
T4G_LARGE: Final = "t4g.large"
T4G_XLARGE: Final = "t4g.xlarge"
T4G_2XLARGE: Final = "t4g.2xlarge"

M6A_LARGE: Final = "m6a.large"
M6A_XLARGE: Final = "m6a.xlarge"
M6A_2XLARGE: Final = "m6a.2xlarge"
M6A_4XLARGE: Final = "m6a.4xlarge"
M6A_8XLARGE: Final = "m6a.8xlarge"
M6A_12XLARGE: Final = "m6a.12xlarge"
M6A_16XLARGE: Final = "m6a.16xlarge"
M6A_24XLARGE: Final = "m6a.24xlarge"
M6A_32XLARGE: Final = "m6a.32xlarge"
M6A_48XLARGE: Final = "m6a.48xlarge"
M6A_METAL: Final = "m6a.metal"

M6IN_LARGE: Final = "m6in.large"
M6IN_XLARGE: Final = "m6in.xlarge"
M6IN_2XLARGE: Final = "m6in.2xlarge"
M6IN_4XLARGE: Final = "m6in.4xlarge"
M6IN_8XLARGE: Final = "m6in.8xlarge"
M6IN_12XLARGE: Final = "m6in.12xlarge"
M6IN_16XLARGE: Final = "m6in.16xlarge"
M6IN_24XLARGE: Final = "m6in.24xlarge"
M6IN_32XLARGE: Final = "m6in.32xlarge"

M6IDN_LARGE: Final = "m6idn.large"
M6IDN_XLARGE: Final = "m6idn.xlarge"
M6IDN_2XLARGE: Final = "m6idn.2xlarge"
M6IDN_4XLARGE: Final = "m6idn.4xlarge"
M6IDN_8XLARGE: Final = "m6idn.8xlarge"
M6IDN_12XLARGE: Final = "m6idn.12xlarge"
M6IDN_16XLARGE: Final = "m6idn.16xlarge"
M6IDN_24XLARGE: Final = "m6idn.24xlarge"
M6IDN_32XLARGE: Final = "m6idn.32xlarge"

M6I_LARGE: Final = "m6i.large"
M6I_XLARGE: Final = "m6i.xlarge"
M6I_2XLARGE: Final = "m6i.2xlarge"
M6I_4XLARGE: Final = "m6i.4xlarge"
M6I_8XLARGE: Final = "m6i.8xlarge"
M6I_12XLARGE: Final = "m6i.12xlarge"
M6I_16XLARGE: Final = "m6i.16xlarge"
M6I_24XLARGE: Final = "m6i.24xlarge"
M6I_32XLARGE: Final = "m6i.32xlarge"
M6I_METAL: Final = "m6i.metal"

M6ID_LARGE: Final = "m6id.large"
M6ID_XLARGE: Final = "m6id.xlarge"
M6ID_2XLARGE: Final = "m6id.2xlarge"
M6ID_4XLARGE: Final = "m6id.4xlarge"
M6ID_8XLARGE: Final = "m6id.8xlarge"
M6ID_12XLARGE: Final = "m6id.12xlarge"
M6ID_16XLARGE: Final = "m6id.16xlarge"
M6ID_24XLARGE: Final = "m6id.24xlarge"
M6ID_32XLARGE: Final = "m6id.32xlarge"
M6ID_METAL: Final = "m6id.metal"

M6G_MEDIUM: Final = "m6g.medium"
M6G_LARGE: Final = "m6g.large"
M6G_XLARGE: Final = "m6g.xlarge"
M6G_2XLARGE: Final = "m6g.2xlarge"
M6G_4XLARGE: Final = "m6g.4xlarge"
M6G_8XLARGE: Final = "m6g.8xlarge"
M6G_12XLARGE: Final = "m6g.12xlarge"
M6G_16XLARGE: Final = "m6g.16xlarge"
M6G_METAL: Final = "m6g.metal"

M6GD_MEDIUM: Final = "m6gd.medium"
M6GD_LARGE: Final = "m6gd.large"
M6GD_XLARGE: Final = "m6gd.xlarge"
M6GD_2XLARGE: Final = "m6gd.2xlarge"
M6GD_4XLARGE: Final = "m6gd.4xlarge"
M6GD_8XLARGE: Final = "m6gd.8xlarge"
M6GD_12XLARGE: Final = "m6gd.12xlarge"
M6GD_16XLARGE: Final = "m6gd.16xlarge"
M6GD_METAL: Final = "m6gd.metal"

M7G_MEDIUM: Final = "m7g.medium"
M7G_LARGE: Final = "m7g.large"
M7G_XLARGE: Final = "m7g.xlarge"
M7G_2XLARGE: Final = "m7g.2xlarge"
M7G_4XLARGE: Final = "m7g.4xlarge"
M7G_8XLARGE: Final = "m7g.8xlarge"
M7G_12XLARGE: Final = "m7g.12xlarge"
M7G_16XLARGE: Final = "m7g.16xlarge"
M7G_METAL: Final = "m7g.metal"

M5_LARGE: Final = "m5.large"
M5_XLARGE: Final = "m5.xlarge"
M5_2XLARGE: Final = "m5.2xlarge"
M5_4XLARGE: Final = "m5.4xlarge"
M5_8XLARGE: Final = "m5.8xlarge"
M5_12XLARGE: Final = "m5.12xlarge"
M5_16XLARGE: Final = "m5.16xlarge"
M5_24XLARGE: Final = "m5.24xlarge"
M5_METAL: Final = "m5.metal"

M5D_LARGE: Final = "m5d.large"
M5D_XLARGE: Final = "m5d.xlarge"
M5D_2XLARGE: Final = "m5d.2xlarge"
M5D_4XLARGE: Final = "m5d.4xlarge"
M5D_8XLARGE: Final = "m5d.8xlarge"
M5D_12XLARGE: Final = "m5d.12xlarge"
M5D_16XLARGE: Final = "m5d.16xlarge"
M5D_24XLARGE: Final = "m5d.24xlarge"
M5D_METAL: Final = "m5d.metal"

M5A_LARGE: Final = "m5a.large"
M5A_XLARGE: Final = "m5a.xlarge"
M5A_2XLARGE: Final = "m5a.2xlarge"
M5A_4XLARGE: Final = "m5a.4xlarge"
M5A_12XLARGE: Final = "m5a.12xlarge"
M5A_24XLARGE: Final = "m5a.24xlarge"

M5AD_LARGE: Final = "m5ad.large"
M5AD_XLARGE: Final = "m5ad.xlarge"
M5AD_2XLARGE: Final = "m5ad.2xlarge"
M5AD_4XLARGE: Final = "m5ad.4xlarge"
M5AD_12XLARGE: Final = "m5ad.12xlarge"
M5AD_24XLARGE: Final = "m5ad.24xlarge"

M5ZN_LARGE: Final = "m5zn.large"
M5ZN_XLARGE: Final = "m5zn.xlarge"
M5ZN_2XLARGE: Final = "m5zn.2xlarge"
M5ZN_3XLARGE: Final = "m5zn.3xlarge"
M5ZN_6XLARGE: Final = "m5zn.6xlarge"
M5ZN_12XLARGE: Final = "m5zn.12xlarge"
M5ZN_METAL: Final = "m5zn.metal"

M5N_LARGE: Final = "m5n.large"
M5N_XLARGE: Final = "m5n.xlarge"
M5N_2XLARGE: Final = "m5n.2xlarge"
M5N_4XLARGE: Final = "m5n.4xlarge"
M5N_8XLARGE: Final = "m5n.8xlarge"
M5N_12XLARGE: Final = "m5n.12xlarge"
M5N_16XLARGE: Final = "m5n.16xlarge"
M5N_24XLARGE: Final = "m5n.24xlarge"
M5N_METAL: Final = "m5n.metal"
M5DN_LARGE: Final = "m5dn.large"
M5DN_XLARGE: Final = "m5dn.xlarge"
M5DN_2XLARGE: Final = "m5dn.2xlarge"
M5DN_4XLARGE: Final = "m5dn.4xlarge"
M5DN_8XLARGE: Final = "m5dn.8xlarge"
M5DN_12XLARGE: Final = "m5dn.12xlarge"
M5DN_16XLARGE: Final = "m5dn.16xlarge"
M5DN_24XLARGE: Final = "m5dn.24xlarge"
M5DN_METAL: Final = "m5dn.metal"

M4_LARGE: Final = "m4.large"
M4_XLARGE: Final = "m4.xlarge"
M4_2XLARGE: Final = "m4.2xlarge"
M4_4XLARGE: Final = "m4.4xlarge"
M4_10XLARGE: Final = "m4.10xlarge"
M4_16XLARGE: Final = "m4.16xlarge"

M3_MEDIUM: Final = "m3.medium"
M3_LARGE: Final = "m3.large"
M3_XLARGE: Final = "m3.xlarge"
M3_2XLARGE: Final = "m3.2xlarge"

A1_MEDIUM: Final = "a1.medium"
A1_LARGE: Final = "a1.large"
A1_XLARGE: Final = "a1.xlarge"
A1_2XLARGE: Final = "a1.2xlarge"
A1_4XLARGE: Final = "a1.4xlarge"
A1_METAL: Final = "a1.metal"

C3_LARGE: Final = "c3.large"
C3_XLARGE: Final = "c3.xlarge"
C3_2XLARGE: Final = "c3.2xlarge"
C3_4XLARGE: Final = "c3.4xlarge"
C3_8XLARGE: Final = "c3.8xlarge"

C4_LARGE: Final = "c4.large"
C4_XLARGE: Final = "c4.xlarge"
C4_2XLARGE: Final = "c4.2xlarge"
C4_4XLARGE: Final = "c4.4xlarge"
C4_8XLARGE: Final = "c4.8xlarge"

C5_LARGE: Final = "c5.large"
C5_XLARGE: Final = "c5.xlarge"
C5_2XLARGE: Final = "c5.2xlarge"
C5_4XLARGE: Final = "c5.4xlarge"
C5_9XLARGE: Final = "c5.9xlarge"
C5_12XLARGE: Final = "c5.12xlarge"
C5_18XLARGE: Final = "c5.18xlarge"
C5_24XLARGE: Final = "c5.24xlarge"
C5_METAL: Final = "c5.metal"

C5A_LARGE: Final = "c5a.large"
C5A_XLARGE: Final = "c5a.xlarge"
C5A_2XLARGE: Final = "c5a.2xlarge"
C5A_4XLARGE: Final = "c5a.4xlarge"
C5A_8XLARGE: Final = "c5a.8xlarge"
C5A_12XLARGE: Final = "c5a.12xlarge"
C5A_16XLARGE: Final = "c5a.16xlarge"
C5A_24XLARGE: Final = "c5a.24xlarge"

C5AD_LARGE: Final = "c5ad.large"
C5AD_XLARGE: Final = "c5ad.xlarge"
C5AD_2XLARGE: Final = "c5ad.2xlarge"
C5AD_4XLARGE: Final = "c5ad.4xlarge"
C5AD_8XLARGE: Final = "c5ad.8xlarge"
C5AD_12XLARGE: Final = "c5ad.12xlarge"
C5AD_16XLARGE: Final = "c5ad.16xlarge"
C5AD_24XLARGE: Final = "c5ad.24xlarge"

C5D_LARGE: Final = "c5d.large"
C5D_XLARGE: Final = "c5d.xlarge"
C5D_2XLARGE: Final = "c5d.2xlarge"
C5D_4XLARGE: Final = "c5d.4xlarge"
C5D_9XLARGE: Final = "c5d.9xlarge"
C5D_12XLARGE: Final = "c5d.12xlarge"
C5D_18XLARGE: Final = "c5d.18xlarge"
C5D_24XLARGE: Final = "c5d.24xlarge"
C5D_METAL: Final = "c5d.metal"

C5N_LARGE: Final = "c5n.large"
C5N_XLARGE: Final = "c5n.xlarge"
C5N_2XLARGE: Final = "c5n.2xlarge"
C5N_4XLARGE: Final = "c5n.4xlarge"
C5N_9XLARGE: Final = "c5n.9xlarge"
C5N_18XLARGE: Final = "c5n.18xlarge"
C5N_METAL: Final = "c5n.metal"

C6G_MEDIUM: Final = "c6g.medium"
C6G_LARGE: Final = "c6g.large"
C6G_XLARGE: Final = "c6g.xlarge"
C6G_2XLARGE: Final = "c6g.2xlarge"
C6G_4XLARGE: Final = "c6g.4xlarge"
C6G_8XLARGE: Final = "c6g.8xlarge"
C6G_12XLARGE: Final = "c6g.12xlarge"
C6G_16XLARGE: Final = "c6g.16xlarge"
C6G_METAL: Final = "c6g.metal"

C6GD_MEDIUM: Final = "c6gd.medium"
C6GD_LARGE: Final = "c6gd.large"
C6GD_XLARGE: Final = "c6gd.xlarge"
C6GD_2XLARGE: Final = "c6gd.2xlarge"
C6GD_4XLARGE: Final = "c6gd.4xlarge"
C6GD_8XLARGE: Final = "c6gd.8xlarge"
C6GD_12XLARGE: Final = "c6gd.12xlarge"
C6GD_16XLARGE: Final = "c6gd.16xlarge"
C6GD_METAL: Final = "c6gd.metal"

C6GN_MEDIUM: Final = "c6gn.medium"
C6GN_LARGE: Final = "c6gn.large"
C6GN_XLARGE: Final = "c6gn.xlarge"
C6GN_2XLARGE: Final = "c6gn.2xlarge"
C6GN_4XLARGE: Final = "c6gn.4xlarge"
C6GN_8XLARGE: Final = "c6gn.8xlarge"
C6GN_12XLARGE: Final = "c6gn.12xlarge"
C6GN_16XLARGE: Final = "c6gn.16xlarge"

C6A_LARGE: Final = "c6a.large"
C6A_XLARGE: Final = "c6a.xlarge"
C6A_2XLARGE: Final = "c6a.2xlarge"
C6A_4XLARGE: Final = "c6a.4xlarge"
C6A_8XLARGE: Final = "c6a.8xlarge"
C6A_12XLARGE: Final = "c6a.12xlarge"
C6A_16XLARGE: Final = "c6a.16xlarge"
C6A_24XLARGE: Final = "c6a.24xlarge"
C6A_32XLARGE: Final = "c6a.32xlarge"
C6A_48XLARGE: Final = "c6a.48xlarge"
C6A_METAL: Final = "c6a.metal"

C6IN_LARGE: Final = "c6in.large"
C6IN_XLARGE: Final = "c6in.xlarge"
C6IN_2XLARGE: Final = "c6in.2xlarge"
C6IN_4XLARGE: Final = "c6in.4xlarge"
C6IN_8XLARGE: Final = "c6in.8xlarge"
C6IN_12XLARGE: Final = "c6in.12xlarge"
C6IN_16XLARGE: Final = "c6in.16xlarge"
C6IN_24XLARGE: Final = "c6in.24xlarge"
C6IN_32XLARGE: Final = "c6in.32xlarge"

C6I_LARGE: Final = "c6i.large"
C6I_XLARGE: Final = "c6i.xlarge"
C6I_2XLARGE: Final = "c6i.2xlarge"
C6I_4XLARGE: Final = "c6i.4xlarge"
C6I_8XLARGE: Final = "c6i.8xlarge"
C6I_12XLARGE: Final = "c6i.12xlarge"
C6I_16XLARGE: Final = "c6i.16xlarge"
C6I_24XLARGE: Final = "c6i.24xlarge"
C6I_32XLARGE: Final = "c6i.32xlarge"
C6I_METAL: Final = "c6i.metal"

C6ID_LARGE: Final = "c6id.large"
C6ID_XLARGE: Final = "c6id.xlarge"
C6ID_2XLARGE: Final = "c6id.2xlarge"
C6ID_4XLARGE: Final = "c6id.4xlarge"
C6ID_8XLARGE: Final = "c6id.8xlarge"
C6ID_12XLARGE: Final = "c6id.12xlarge"
C6ID_16XLARGE: Final = "c6id.16xlarge"
C6ID_24XLARGE: Final = "c6id.24xlarge"
C6ID_32XLARGE: Final = "c6id.32xlarge"
C6ID_METAL: Final = "c6id.metal"

C7G_MEDIUM: Final = "c7g.medium"
C7G_LARGE: Final = "c7g.large"
C7G_XLARGE: Final = "c7g.xlarge"
C7G_2XLARGE: Final = "c7g.2xlarge"
C7G_4XLARGE: Final = "c7g.4xlarge"
C7G_8XLARGE: Final = "c7g.8xlarge"
C7G_12XLARGE: Final = "c7g.12xlarge"
C7G_16XLARGE: Final = "c7g.16xlarge"
C7G_METAL: Final = "c7g.metal"

R3_LARGE: Final = "r3.large"
R3_XLARGE: Final = "r3.xlarge"
R3_2XLARGE: Final = "r3.2xlarge"
R3_4XLARGE: Final = "r3.4xlarge"
R3_8XLARGE: Final = "r3.8xlarge"

G2_2XLARGE: Final = "g2.2xlarge"
G2_8XLARGE: Final = "g2.8xlarge"

G3S_XLARGE: Final = "g3s.xlarge"
G3_4XLARGE: Final = "g3.4xlarge"
G3_8XLARGE: Final = "g3.8xlarge"
G3_16XLARGE: Final = "g3.16xlarge"

G4AD_4XLARGE: Final = "g4ad.4xlarge"
G4AD_8XLARGE: Final = "g4ad.8xlarge"
G4AD_16XLARGE: Final = "g4ad.16xlarge"

G4DN_XLARGE: Final = "g4dn.xlarge"
G4DN_2XLARGE: Final = "g4dn.2xlarge"
G4DN_4XLARGE: Final = "g4dn.4xlarge"
G4DN_8XLARGE: Final = "g4dn.8xlarge"
G4DN_12XLARGE: Final = "g4dn.12xlarge"
G4DN_16XLARGE: Final = "g4dn.16xlarge"
G4DN_METAL: Final = "g4dn.metal"

I2_XLARGE: Final = "i2.xlarge"
I2_2XLARGE: Final = "i2.2xlarge"
I2_4XLARGE: Final = "i2.4xlarge"
I2_8XLARGE: Final = "i2.8xlarge"

H1_2XLARGE: Final = "h1.2xlarge"
H1_4XLARGE: Final = "h1.4xlarge"
H1_8XLARGE: Final = "h1.8xlarge"
H1_16XLARGE: Final = "h1.16xlarge"

I3_LARGE: Final = "i3.large"
I3_XLARGE: Final = "i3.xlarge"
I3_2XLARGE: Final = "i3.2xlarge"
I3_4XLARGE: Final = "i3.4xlarge"
I3_8XLARGE: Final = "i3.8xlarge"
I3_16XLARGE: Final = "i3.16xlarge"
I3_METAL: Final = "i3.metal"

I3EN_LARGE: Final = "i3en.large"
I3EN_XLARGE: Final = "i3en.xlarge"
I3EN_2XLARGE: Final = "i3en.2xlarge"
I3EN_3XLARGE: Final = "i3en.3xlarge"
I3EN_6XLARGE: Final = "i3en.6xlarge"
I3EN_12XLARGE: Final = "i3en.12xlarge"
I3EN_24XLARGE: Final = "i3en.24xlarge"
I3EN_METAL: Final = "i3en.metal"

D2_XLARGE: Final = "d2.xlarge"
D2_2XLARGE: Final = "d2.2xlarge"
D2_4XLARGE: Final = "d2.4xlarge"
D2_8XLARGE: Final = "d2.8xlarge"

D3_XLARGE: Final = "d3.xlarge"
D3_2XLARGE: Final = "d3.2xlarge"
D3_4XLARGE: Final = "d3.4xlarge"
D3_8XLARGE: Final = "d3.8xlarge"

D3EN_XLARGE: Final = "d3en.xlarge"
D3EN_2XLARGE: Final = "d3en.2xlarge"
D3EN_4XLARGE: Final = "d3en.4xlarge"
D3EN_6XLARGE: Final = "d3en.6xlarge"
D3EN_8XLARGE: Final = "d3en.8xlarge"
D3EN_12XLARGE: Final = "d3en.12xlarge"

HS1_8XLARGE: Final = "hs1.8xlarge"

M1_SMALL: Final = "m1.small"
M1_MEDIUM: Final = "m1.medium"
M1_LARGE: Final = "m1.large"
M1_XLARGE: Final = "m1.xlarge"

C1_MEDIUM: Final = "c1.medium"
C1_XLARGE: Final = "c1.xlarge"
CC2_8XLARGE: Final = "cc2.8xlarge"

CG1_4XLARGE: Final = "cg1.4xlarge"

M2_XLARGE: Final = "m2.xlarge"
M2_2XLARGE: Final = "m2.2xlarge"
M2_4XLARGE: Final = "m2.4xlarge"
CR1_8XLARGE: Final = "cr1.8xlarge"

HI1_4XLARGE: Final = "hi1.4xlarge"

T1_MICRO: Final = "t1.micro"

X1_32XLARGE: Final = "x1.32xlarge"
X1_16XLARGE: Final = "x1.16xlarge"

X1E_XLARGE: Final = "x1e.xlarge"
X1E_2XLARGE: Final = "x1e.2xlarge"
X1E_4XLARGE: Final = "x1e.4xlarge"
X1E_8XLARGE: Final = "x1e.8xlarge"
X1E_16XLARGE: Final = "x1e.16xlarge"
X1E_32XLARGE: Final = "x1e.32xlarge"

U_6TB1_METAL: Final = "u-6tb1.metal"
U_9TB1_METAL: Final = "u-9tb1.metal"
U_12TB1_METAL: Final = "u-12tb1.metal"
U_18TB1_METAL: Final = "u-18tb1.metal"
U_24TB1_METAL: Final = "u-24tb1.metal"

Z1D_LARGE: Final = "z1d.large"
Z1D_XLARGE: Final = "z1d.xlarge"
Z1D_2XLARGE: Final = "z1d.2xlarge"
Z1D_3XLARGE: Final = "z1d.3xlarge"
Z1D_6XLARGE: Final = "z1d.6xlarge"
Z1D_12XLARGE: Final = "z1d.12xlarge"
Z1D_METAL: Final = "z1d.metal"

R4_LARGE: Final = "r4.large"
R4_XLARGE: Final = "r4.xlarge"
R4_2XLARGE: Final = "r4.2xlarge"
R4_4XLARGE: Final = "r4.4xlarge"
R4_8XLARGE: Final = "r4.8xlarge"
R4_16XLARGE: Final = "r4.16xlarge"

R5_LARGE: Final = "r5.large"
R5_XLARGE: Final = "r5.xlarge"
R5_2XLARGE: Final = "r5.2xlarge"
R5_4XLARGE: Final = "r5.4xlarge"
R5_8XLARGE: Final = "r5.8xlarge"
R5_12XLARGE: Final = "r5.12xlarge"
R5_16XLARGE: Final = "r5.16xlarge"
R5_24XLARGE: Final = "r5.24xlarge"
R5_METAL: Final = "r5.metal"

R5D_LARGE: Final = "r5d.large"
R5D_XLARGE: Final = "r5d.xlarge"
R5D_2XLARGE: Final = "r5d.2xlarge"
R5D_4XLARGE: Final = "r5d.4xlarge"
R5D_8XLARGE: Final = "r5d.8xlarge"
R5D_12XLARGE: Final = "r5d.12xlarge"
R5D_16XLARGE: Final = "r5d.16xlarge"
R5D_24XLARGE: Final = "r5d.24xlarge"
R5D_METAL: Final = "r5d.metal"

R5A_LARGE: Final = "r5a.large"
R5A_XLARGE: Final = "r5a.xlarge"
R5A_2XLARGE: Final = "r5a.2xlarge"
R5A_4XLARGE: Final = "r5a.4xlarge"
R5A_8XLARGE: Final = "r5a.8xlarge"
R5A_12XLARGE: Final = "r5a.12xlarge"
R5A_16XLARGE: Final = "r5a.16xlarge"
R5A_24XLARGE: Final = "r5a.24xlarge"

R5AD_LARGE: Final = "r5ad.large"
R5AD_XLARGE: Final = "r5ad.xlarge"
R5AD_2XLARGE: Final = "r5ad.2xlarge"
R5AD_4XLARGE: Final = "r5ad.4xlarge"
R5AD_8XLARGE: Final = "r5ad.8xlarge"
R5AD_12XLARGE: Final = "r5ad.12xlarge"
R5AD_16XLARGE: Final = "r5ad.16xlarge"
R5AD_24XLARGE: Final = "r5ad.24xlarge"

R5N_LARGE: Final = "r5n.large"
R5N_XLARGE: Final = "r5n.xlarge"
R5N_2XLARGE: Final = "r5n.2xlarge"
R5N_4XLARGE: Final = "r5n.4xlarge"
R5N_8XLARGE: Final = "r5n.8xlarge"
R5N_12XLARGE: Final = "r5n.12xlarge"
R5N_16XLARGE: Final = "r5n.16xlarge"
R5N_24XLARGE: Final = "r5n.24xlarge"
R5N_METAL: Final = "r5n.metal"

R5DN_LARGE: Final = "r5dn.large"
R5DN_XLARGE: Final = "r5dn.xlarge"
R5DN_2XLARGE: Final = "r5dn.2xlarge"
R5DN_4XLARGE: Final = "r5dn.4xlarge"
R5DN_8XLARGE: Final = "r5dn.8xlarge"
R5DN_12XLARGE: Final = "r5dn.12xlarge"
R5DN_16XLARGE: Final = "r5dn.16xlarge"
R5DN_24XLARGE: Final = "r5dn.24xlarge"
R5DN_METAL: Final = "r5dn.metal"

R5B_LARGE: Final = "r5b.large"
R5B_XLARGE: Final = "r5b.xlarge"
R5B_2XLARGE: Final = "r5b.2xlarge"
R5B_4XLARGE: Final = "r5b.4xlarge"
R5B_8XLARGE: Final = "r5b.8xlarge"
R5B_12XLARGE: Final = "r5b.12xlarge"
R5B_16XLARGE: Final = "r5b.16xlarge"
R5B_24XLARGE: Final = "r5b.24xlarge"
R5B_METAL: Final = "r5b.metal"

R6G_MEDIUM: Final = "r6g.medium"
R6G_LARGE: Final = "r6g.large"
R6G_XLARGE: Final = "r6g.xlarge"
R6G_2XLARGE: Final = "r6g.2xlarge"
R6G_4XLARGE: Final = "r6g.4xlarge"
R6G_8XLARGE: Final = "r6g.8xlarge"
R6G_12XLARGE: Final = "r6g.12xlarge"
R6G_16XLARGE: Final = "r6g.16xlarge"
R6G_METAL: Final = "r6g.metal"

R6GD_MEDIUM: Final = "r6gd.medium"
R6GD_LARGE: Final = "r6gd.large"
R6GD_XLARGE: Final = "r6gd.xlarge"
R6GD_2XLARGE: Final = "r6gd.2xlarge"
R6GD_4XLARGE: Final = "r6gd.4xlarge"
R6GD_8XLARGE: Final = "r6gd.8xlarge"
R6GD_12XLARGE: Final = "r6gd.12xlarge"
R6GD_16XLARGE: Final = "r6gd.16xlarge"
R6GD_METAL: Final = "r6gd.metal"

R6I_LARGE: Final = "r6i.large"
R6I_XLARGE: Final = "r6i.xlarge"
R6I_2XLARGE: Final = "r6i.2xlarge"
R6I_4XLARGE: Final = "r6i.4xlarge"
R6I_8XLARGE: Final = "r6i.8xlarge"
R6I_12XLARGE: Final = "r6i.12xlarge"
R6I_16XLARGE: Final = "r6i.16xlarge"
R6I_24XLARGE: Final = "r6i.24xlarge"
R6I_32XLARGE: Final = "r6i.32xlarge"
R6I_METAL: Final = "r6i.metal"

R6ID_LARGE: Final = "r6id.large"
R6ID_XLARGE: Final = "r6id.xlarge"
R6ID_2XLARGE: Final = "r6id.2xlarge"
R6ID_4XLARGE: Final = "r6id.4xlarge"
R6ID_8XLARGE: Final = "r6id.8xlarge"
R6ID_12XLARGE: Final = "r6id.12xlarge"
R6ID_16XLARGE: Final = "r6id.16xlarge"
R6ID_24XLARGE: Final = "r6id.24xlarge"
R6ID_32XLARGE: Final = "r6id.32xlarge"
R6ID_METAL: Final = "r6id.metal"

R6IN_LARGE: Final = "r6in.large"
R6IN_XLARGE: Final = "r6in.xlarge"
R6IN_2XLARGE: Final = "r6in.2xlarge"
R6IN_4XLARGE: Final = "r6in.4xlarge"
R6IN_8XLARGE: Final = "r6in.8xlarge"
R6IN_12XLARGE: Final = "r6in.12xlarge"
R6IN_16XLARGE: Final = "r6in.16xlarge"
R6IN_24XLARGE: Final = "r6in.24xlarge"
R6IN_32XLARGE: Final = "r6in.32xlarge"

R6IDN_LARGE: Final = "r6idn.large"
R6IDN_XLARGE: Final = "r6idn.xlarge"
R6IDN_2XLARGE: Final = "r6idn.2xlarge"
R6IDN_4XLARGE: Final = "r6idn.4xlarge"
R6IDN_8XLARGE: Final = "r6idn.8xlarge"
R6IDN_12XLARGE: Final = "r6idn.12xlarge"
R6IDN_16XLARGE: Final = "r6idn.16xlarge"
R6IDN_24XLARGE: Final = "r6idn.24xlarge"
R6IDN_32XLARGE: Final = "r6idn.32xlarge"

R6A_LARGE: Final = "r6a.large"
R6A_XLARGE: Final = "r6a.xlarge"
R6A_2XLARGE: Final = "r6a.2xlarge"
R6A_4XLARGE: Final = "r6a.4xlarge"
R6A_8XLARGE: Final = "r6a.8xlarge"
R6A_12XLARGE: Final = "r6a.12xlarge"
R6A_16XLARGE: Final = "r6a.16xlarge"
R6A_24XLARGE: Final = "r6a.24xlarge"
R6A_32XLARGE: Final = "r6a.32xlarge"
R6A_48XLARGE: Final = "r6a.48xlarge"
R6A_METAL: Final = "r6a.metal"

R7G_MEDIUM: Final = "r7g.medium"
R7G_LARGE: Final = "r7g.large"
R7G_XLARGE: Final = "r7g.xlarge"
R7G_2XLARGE: Final = "r7g.2xlarge"
R7G_4XLARGE: Final = "r7g.4xlarge"
R7G_8XLARGE: Final = "r7g.8xlarge"
R7G_12XLARGE: Final = "r7g.12xlarge"
R7G_16XLARGE: Final = "r7g.16xlarge"
R7G_METAL: Final = "r7g.metal"

P2_XLARGE: Final = "p2.xlarge"
P2_8XLARGE: Final = "p2.8xlarge"
P2_16XLARGE: Final = "p2.16xlarge"

P3_2XLARGE: Final = "p3.2xlarge"
P3_8XLARGE: Final = "p3.8xlarge"
P3_16XLARGE: Final = "p3.16xlarge"
P3DN_24XLARGE: Final = "p3dn.24xlarge"

F1_2XLARGE: Final = "f1.2xlarge"
F1_4XLARGE: Final = "f1.4xlarge"
F1_16XLARGE: Final = "f1.16xlarge"

INF1_XLARGE: Final = "inf1.xlarge"
INF1_2XLARGE: Final = "inf1.2xlarge"
INF1_6XLARGE: Final = "inf1.6xlarge"
INF1_24XLARGE: Final = "inf1.24xlarge"

#
# RDS DB instance classes
#

DB_T1_MICRO: Final = "db.t1.micro"

DB_T2_MICRO: Final = "db.t2.micro"
DB_T2_SMALL: Final = "db.t2.small"
DB_T2_MEDIUM: Final = "db.t2.medium"
DB_T2_LARGE: Final = "db.t2.large"
DB_T2_XLARGE: Final = "db.t2.xlarge"
DB_T2_2XLARGE: Final = "db.t2.2xlarge"

DB_T3_MICRO: Final = "db.t3.micro"
DB_T3_SMALL: Final = "db.t3.small"
DB_T3_MEDIUM: Final = "db.t3.medium"
DB_T3_LARGE: Final = "db.t3.large"
DB_T3_XLARGE: Final = "db.t3.xlarge"
DB_T3_2XLARGE: Final = "db.t3.2xlarge"

DB_T4G_MICRO: Final = "db.t4g.micro"
DB_T4G_SMALL: Final = "db.t4g.small"
DB_T4G_MEDIUM: Final = "db.t4g.medium"
DB_T4G_LARGE: Final = "db.t4g.large"
DB_T4G_XLARGE: Final = "db.t4g.xlarge"
DB_T4G_2XLARGE: Final = "db.t4g.2xlarge"

DB_M1_SMALL: Final = "db.m1.small"
DB_M1_MEDIUM: Final = "db.m1.medium"
DB_M1_LARGE: Final = "db.m1.large"
DB_M1_XLARGE: Final = "db.m1.xlarge"

DB_M2_XLARGE: Final = "db.m2.xlarge"
DB_M2_2XLARGE: Final = "db.m2.2xlarge"
DB_M2_4XLARGE: Final = "db.m2.4xlarge"

DB_M3_MEDIUM: Final = "db.m3.medium"
DB_M3_LARGE: Final = "db.m3.large"
DB_M3_XLARGE: Final = "db.m3.xlarge"
DB_M3_2XLARGE: Final = "db.m3.2xlarge"

DB_M4_LARGE: Final = "db.m4.large"
DB_M4_XLARGE: Final = "db.m4.xlarge"
DB_M4_2XLARGE: Final = "db.m4.2xlarge"
DB_M4_4XLARGE: Final = "db.m4.4xlarge"
DB_M4_10XLARGE: Final = "db.m4.10xlarge"
DB_M4_16XLARGE: Final = "db.m4.16xlarge"

DB_M5_LARGE: Final = "db.m5.large"
DB_M5_XLARGE: Final = "db.m5.xlarge"
DB_M5_2XLARGE: Final = "db.m5.2xlarge"
DB_M5_4XLARGE: Final = "db.m5.4xlarge"
DB_M5_8XLARGE: Final = "db.m5.8xlarge"
DB_M5_12XLARGE: Final = "db.m5.12xlarge"
DB_M5_16XLARGE: Final = "db.m5.16xlarge"
DB_M5_24XLARGE: Final = "db.m5.24xlarge"

DB_M5D_LARGE: Final = "db.m5d.large"
DB_M5D_XLARGE: Final = "db.m5d.xlarge"
DB_M5D_2XLARGE: Final = "db.m5d.2xlarge"
DB_M5D_4XLARGE: Final = "db.m5d.4xlarge"
DB_M5D_8XLARGE: Final = "db.m5d.8xlarge"
DB_M5D_12XLARGE: Final = "db.m5d.12xlarge"
DB_M5D_16XLARGE: Final = "db.m5d.16xlarge"
DB_M5D_24XLARGE: Final = "db.m5d.24xlarge"

DB_M6G_LARGE: Final = "db.m6g.large"
DB_M6G_XLARGE: Final = "db.m6g.xlarge"
DB_M6G_2XLARGE: Final = "db.m6g.2xlarge"
DB_M6G_4XLARGE: Final = "db.m6g.4xlarge"
DB_M6G_8XLARGE: Final = "db.m6g.8xlarge"
DB_M6G_12XLARGE: Final = "db.m6g.12xlarge"
DB_M6G_16XLARGE: Final = "db.m6g.16xlarge"

DB_R3_LARGE: Final = "db.r3.large"
DB_R3_XLARGE: Final = "db.r3.xlarge"
DB_R3_2XLARGE: Final = "db.r3.2xlarge"
DB_R3_4XLARGE: Final = "db.r3.4xlarge"
DB_R3_8XLARGE: Final = "db.r3.8xlarge"

DB_R4_LARGE: Final = "db.r4.large"
DB_R4_XLARGE: Final = "db.r4.xlarge"
DB_R4_2XLARGE: Final = "db.r4.2xlarge"
DB_R4_4XLARGE: Final = "db.r4.4xlarge"
DB_R4_8XLARGE: Final = "db.r4.8xlarge"
DB_R4_16XLARGE: Final = "db.r4.16xlarge"

DB_R5_LARGE: Final = "db.r5.large"
DB_R5_XLARGE: Final = "db.r5.xlarge"
DB_R5_2XLARGE: Final = "db.r5.2xlarge"
DB_R5_4XLARGE: Final = "db.r5.4xlarge"
DB_R5_8XLARGE: Final = "db.r5.8xlarge"
DB_R5_12XLARGE: Final = "db.r5.12xlarge"
DB_R5_16XLARGE: Final = "db.r5.16xlarge"
DB_R5_24XLARGE: Final = "db.r5.24xlarge"

DB_R5_LARGE_TPC1_MEM2X: Final = "db.r5.large.tpc1.mem2x"
DB_R5_XLARGE_TPC2_MEM2X: Final = "db.r5.xlarge.tpc2.mem2x"
DB_R5_XLARGE_TPC2_MEM4X: Final = "db.r5.xlarge.tpc2.mem4x"
DB_R5_2XLARGE_TPC1_MEM2X: Final = "db.r5.2xlarge.tpc1.mem2x"
DB_R5_2XLARGE_TPC2_MEM4X: Final = "db.r5.2xlarge.tpc2.mem4x"
DB_R5_2XLARGE_TPC2_MEM8X: Final = "db.r5.2xlarge.tpc2.mem8x"
DB_R5_4XLARGE_TPC2_MEM2X: Final = "db.r5.4xlarge.tpc2.mem2x"
DB_R5_4XLARGE_TPC2_MEM3X: Final = "db.r5.4xlarge.tpc2.mem3x"
DB_R5_4XLARGE_TPC2_MEM4X: Final = "db.r5.4xlarge.tpc2.mem4x"
DB_R5_6XLARGE_TPC2_MEM4X: Final = "db.r5.6xlarge.tpc2.mem4x"
DB_R5_8XLARGE_TPC2_MEM3X: Final = "db.r5.8xlarge.tpc2.mem3x"
DB_R5_12XLARGE_TPC2_MEM2X: Final = "db.r5.12xlarge.tpc2.mem2x"

DB_R5B_LARGE: Final = "db.r5b.large"
DB_R5B_XLARGE: Final = "db.r5b.xlarge"
DB_R5B_2XLARGE: Final = "db.r5b.2xlarge"
DB_R5B_4XLARGE: Final = "db.r5b.4xlarge"
DB_R5B_8XLARGE: Final = "db.r5b.8xlarge"
DB_R5B_12XLARGE: Final = "db.r5b.12xlarge"
DB_R5B_16XLARGE: Final = "db.r5b.16xlarge"
DB_R5B_24XLARGE: Final = "db.r5b.24xlarge"

DB_R5D_LARGE: Final = "db.r5d.large"
DB_R5D_XLARGE: Final = "db.r5d.xlarge"
DB_R5D_2XLARGE: Final = "db.r5d.2xlarge"
DB_R5D_4XLARGE: Final = "db.r5d.4xlarge"
DB_R5D_8XLARGE: Final = "db.r5d.8xlarge"
DB_R5D_12XLARGE: Final = "db.r5d.12xlarge"
DB_R5D_16XLARGE: Final = "db.r5d.16xlarge"
DB_R5D_24XLARGE: Final = "db.r5d.24xlarge"

DB_R6G_LARGE: Final = "db.r6g.large"
DB_R6G_XLARGE: Final = "db.r6g.xlarge"
DB_R6G_2XLARGE: Final = "db.r6g.2xlarge"
DB_R6G_4XLARGE: Final = "db.r6g.4xlarge"
DB_R6G_8XLARGE: Final = "db.r6g.8xlarge"
DB_R6G_12XLARGE: Final = "db.r6g.12xlarge"
DB_R6G_16XLARGE: Final = "db.r6g.16xlarge"

DB_X1_16XLARGE: Final = "db.x1.16xlarge"
DB_X1_32XLARGE: Final = "db.x1.32xlarge"

DB_X1E_XLARGE: Final = "db.x1e.xlarge"
DB_X1E_2XLARGE: Final = "db.x1e.2xlarge"
DB_X1E_4XLARGE: Final = "db.x1e.4xlarge"
DB_X1E_8XLARGE: Final = "db.x1e.8xlarge"
DB_X1E_16XLARGE: Final = "db.x1e.16xlarge"
DB_X1E_32XLARGE: Final = "db.x1e.32xlarge"

DB_X2G_MEDIUM: Final = "db.x2g.medium"
DB_X2G_LARGE: Final = "db.x2g.large"
DB_X2G_XLARGE: Final = "db.x2g.xlarge"
DB_X2G_2XLARGE: Final = "db.x2g.2xlarge"
DB_X2G_4XLARGE: Final = "db.x2g.4xlarge"
DB_X2G_8XLARGE: Final = "db.x2g.8xlarge"
DB_X2G_12XLARGE: Final = "db.x2g.12xlarge"
DB_X2G_16XLARGE: Final = "db.x2g.16xlarge"

DB_Z1D_LARGE: Final = "db.z1d.large"
DB_Z1D_XLARGE: Final = "db.z1d.xlarge"
DB_Z1D_2XLARGE: Final = "db.z1d.2xlarge"
DB_Z1D_3XLARGE: Final = "db.z1d.3xlarge"
DB_Z1D_6XLARGE: Final = "db.z1d.6xlarge"
DB_Z1D_12XLARGE: Final = "db.z1d.12xlarge"

DB_CR1_8XLARGE: Final = "db.cr1.8xlarge"

#
# ElastiCache node types
#

# v T1, M1, M2, M3, R3 will be final EOL 2024-10-15
# Likely C1 is EOL at the same time
CACHE_C1_XLARGE: Final = "cache.c1.xlarge"

CACHE_T1_MICRO: Final = "cache.t1.micro"

CACHE_M1_SMALL: Final = "cache.m1.small"
CACHE_M1_MEDIUM: Final = "cache.m1.medium"
CACHE_M1_LARGE: Final = "cache.m1.large"
CACHE_M1_XLARGE: Final = "cache.m1.xlarge"

CACHE_M2_XLARGE: Final = "cache.m2.xlarge"
CACHE_M2_2XLARGE: Final = "cache.m2.2xlarge"
CACHE_M2_4XLARGE: Final = "cache.m2.4xlarge"

CACHE_M3_MEDIUM: Final = "cache.m3.medium"
CACHE_M3_LARGE: Final = "cache.m3.large"
CACHE_M3_XLARGE: Final = "cache.m3.xlarge"
CACHE_M3_2XLARGE: Final = "cache.m3.2xlarge"

CACHE_R3_LARGE: Final = "cache.r3.large"
CACHE_R3_XLARGE: Final = "cache.r3.xlarge"
CACHE_R3_2XLARGE: Final = "cache.r3.2xlarge"
CACHE_R3_4XLARGE: Final = "cache.r3.4xlarge"
CACHE_R3_8XLARGE: Final = "cache.r3.8xlarge"
# ^ EOL 2024-10-15

CACHE_C7GN_LARGE: Final = "cache.c7gn.large"
CACHE_C7GN_XLARGE: Final = "cache.c7gn.xlarge"
CACHE_C7GN_2XLARGE: Final = "cache.c7gn.2xlarge"
CACHE_C7GN_4XLARGE: Final = "cache.c7gn.4xlarge"
CACHE_C7GN_8XLARGE: Final = "cache.c7gn.8xlarge"
CACHE_C7GN_12XLARGE: Final = "cache.c7gn.12xlarge"
CACHE_C7GN_16XLARGE: Final = "cache.c7gn.16xlarge"

CACHE_M4_LARGE: Final = "cache.m4.large"
CACHE_M4_XLARGE: Final = "cache.m4.xlarge"
CACHE_M4_2XLARGE: Final = "cache.m4.2xlarge"
CACHE_M4_4XLARGE: Final = "cache.m4.4xlarge"
CACHE_M4_10XLARGE: Final = "cache.m4.10xlarge"

CACHE_M5_LARGE: Final = "cache.m5.large"
CACHE_M5_XLARGE: Final = "cache.m5.xlarge"
CACHE_M5_2XLARGE: Final = "cache.m5.2xlarge"
CACHE_M5_4XLARGE: Final = "cache.m5.4xlarge"
CACHE_M5_12XLARGE: Final = "cache.m5.12xlarge"
CACHE_M5_24XLARGE: Final = "cache.m5.24xlarge"

CACHE_M6G_LARGE: Final = "cache.m6g.large"
CACHE_M6G_XLARGE: Final = "cache.m6g.xlarge"
CACHE_M6G_2XLARGE: Final = "cache.m6g.2xlarge"
CACHE_M6G_4XLARGE: Final = "cache.m6g.4xlarge"
CACHE_M6G_8XLARGE: Final = "cache.m6g.8xlarge"
CACHE_M6G_12XLARGE: Final = "cache.m6g.12xlarge"
CACHE_M6G_16XLARGE: Final = "cache.m6g.16xlarge"

CACHE_M7G_LARGE: Final = "cache.m7g.large"
CACHE_M7G_XLARGE: Final = "cache.m7g.xlarge"
CACHE_M7G_2XLARGE: Final = "cache.m7g.2xlarge"
CACHE_M7G_4XLARGE: Final = "cache.m7g.4xlarge"
CACHE_M7G_8XLARGE: Final = "cache.m7g.8xlarge"
CACHE_M7G_12XLARGE: Final = "cache.m7g.12xlarge"
CACHE_M7G_16XLARGE: Final = "cache.m7g.16xlarge"

CACHE_R4_LARGE: Final = "cache.r4.large"
CACHE_R4_XLARGE: Final = "cache.r4.xlarge"
CACHE_R4_2XLARGE: Final = "cache.r4.2xlarge"
CACHE_R4_4XLARGE: Final = "cache.r4.4xlarge"
CACHE_R4_8XLARGE: Final = "cache.r4.8xlarge"
CACHE_R4_16XLARGE: Final = "cache.r4.16xlarge"

CACHE_R5_LARGE: Final = "cache.r5.large"
CACHE_R5_XLARGE: Final = "cache.r5.xlarge"
CACHE_R5_2XLARGE: Final = "cache.r5.2xlarge"
CACHE_R5_4XLARGE: Final = "cache.r5.4xlarge"
CACHE_R5_12XLARGE: Final = "cache.r5.12xlarge"
CACHE_R5_24XLARGE: Final = "cache.r5.24xlarge"

CACHE_R6G_LARGE: Final = "cache.r6g.large"
CACHE_R6G_XLARGE: Final = "cache.r6g.xlarge"
CACHE_R6G_2XLARGE: Final = "cache.r6g.2xlarge"
CACHE_R6G_4XLARGE: Final = "cache.r6g.4xlarge"
CACHE_R6G_8XLARGE: Final = "cache.r6g.8xlarge"
CACHE_R6G_12XLARGE: Final = "cache.r6g.12xlarge"
CACHE_R6G_16XLARGE: Final = "cache.r6g.16xlarge"

CACHE_R6GD_XLARGE: Final = "cache.r6gd.xlarge"
CACHE_R6GD_2XLARGE: Final = "cache.r6gd.2xlarge"
CACHE_R6GD_4XLARGE: Final = "cache.r6gd.4xlarge"
CACHE_R6GD_8XLARGE: Final = "cache.r6gd.8xlarge"
CACHE_R6GD_12XLARGE: Final = "cache.r6gd.12xlarge"
CACHE_R6GD_16XLARGE: Final = "cache.r6gd.16xlarge"

CACHE_R7G_LARGE: Final = "cache.r7g.large"
CACHE_R7G_XLARGE: Final = "cache.r7g.xlarge"
CACHE_R7G_2XLARGE: Final = "cache.r7g.2xlarge"
CACHE_R7G_4XLARGE: Final = "cache.r7g.4xlarge"
CACHE_R7G_8XLARGE: Final = "cache.r7g.8xlarge"
CACHE_R7G_12XLARGE: Final = "cache.r7g.12xlarge"
CACHE_R7G_16XLARGE: Final = "cache.r7g.16xlarge"

CACHE_T2_MICRO: Final = "cache.t2.micro"
CACHE_T2_SMALL: Final = "cache.t2.small"
CACHE_T2_MEDIUM: Final = "cache.t2.medium"

CACHE_T3_MICRO: Final = "cache.t3.micro"
CACHE_T3_SMALL: Final = "cache.t3.small"
CACHE_T3_MEDIUM: Final = "cache.t3.medium"

CACHE_T4G_MICRO: Final = "cache.t4g.micro"
CACHE_T4G_SMALL: Final = "cache.t4g.small"
CACHE_T4G_MEDIUM: Final = "cache.t4g.medium"

#
# OpenSearch instance types
#

SEARCH_C4_LARGE: Final = "c4.large.search"
SEARCH_C4_XLARGE: Final = "c4.xlarge.search"
SEARCH_C4_2XLARGE: Final = "c4.2xlarge.search"
SEARCH_C4_4XLARGE: Final = "c4.4xlarge.search"
SEARCH_C4_8XLARGE: Final = "c4.8xlarge.search"

SEARCH_C5_LARGE: Final = "c5.large.search"
SEARCH_C5_XLARGE: Final = "c5.xlarge.search"
SEARCH_C5_2XLARGE: Final = "c5.2xlarge.search"
SEARCH_C5_4XLARGE: Final = "c5.4xlarge.search"
SEARCH_C5_9XLARGE: Final = "c5.9xlarge.search"
SEARCH_C5_18XLARGE: Final = "c5.18xlarge.search"

SEARCH_C6G_LARGE: Final = "c6g.large.search"
SEARCH_C6G_XLARGE: Final = "c6g.xlarge.search"
SEARCH_C6G_2XLARGE: Final = "c6g.2xlarge.search"
SEARCH_C6G_4XLARGE: Final = "c6g.4xlarge.search"
SEARCH_C6G_8XLARGE: Final = "c6g.8xlarge.search"
SEARCH_C6G_12XLARGE: Final = "c6g.12xlarge.search"

SEARCH_I2_XLARGE: Final = "i2.xlarge.search"
SEARCH_I2_2XLARGE: Final = "i2.2xlarge.search"

SEARCH_I3_LARGE: Final = "i3.large.search"
SEARCH_I3_XLARGE: Final = "i3.xlarge.search"
SEARCH_I3_2XLARGE: Final = "i3.2xlarge.search"
SEARCH_I3_4XLARGE: Final = "i3.4xlarge.search"
SEARCH_I3_8XLARGE: Final = "i3.8xlarge.search"
SEARCH_I3_16XLARGE: Final = "i3.16xlarge.search"

SEARCH_IM4GN_LARGE: Final = "im4gn.large.search"
SEARCH_IM4GN_XLARGE: Final = "im4gn.xlarge.search"
SEARCH_IM4GN_2XLARGE: Final = "im4gn.2xlarge.search"
SEARCH_IM4GN_4XLARGE: Final = "im4gn.4xlarge.search"
SEARCH_IM4GN_8XLARGE: Final = "im4gn.8xlarge.search"
SEARCH_IM4GN_16XLARGE: Final = "im4gn.16xlarge.search"

SEARCH_M3_MEDIUM: Final = "m3.medium.search"
SEARCH_M3_LARGE: Final = "m3.large.search"
SEARCH_M3_XLARGE: Final = "m3.xlarge.search"
SEARCH_M3_2XLARGE: Final = "m3.2xlarge.search"

SEARCH_M4_LARGE: Final = "m4.large.search"
SEARCH_M4_XLARGE: Final = "m4.xlarge.search"
SEARCH_M4_2XLARGE: Final = "m4.2xlarge.search"
SEARCH_M4_4XLARGE: Final = "m4.4xlarge.search"
SEARCH_M4_10XLARGE: Final = "m4.10xlarge.search"

SEARCH_M5_LARGE: Final = "m5.large.search"
SEARCH_M5_XLARGE: Final = "m5.xlarge.search"
SEARCH_M5_2XLARGE: Final = "m5.2xlarge.search"
SEARCH_M5_4XLARGE: Final = "m5.4xlarge.search"
SEARCH_M5_12XLARGE: Final = "m5.12xlarge.search"

SEARCH_M6G_LARGE: Final = "m6g.large.search"
SEARCH_M6G_XLARGE: Final = "m6g.xlarge.search"
SEARCH_M6G_2XLARGE: Final = "m6g.2xlarge.search"
SEARCH_M6G_4XLARGE: Final = "m6g.4xlarge.search"
SEARCH_M6G_8XLARGE: Final = "m6g.8xlarge.search"
SEARCH_M6G_12XLARGE: Final = "m6g.12xlarge.search"

SEARCH_R3_LARGE: Final = "r3.large.search"
SEARCH_R3_XLARGE: Final = "r3.xlarge.search"
SEARCH_R3_2XLARGE: Final = "r3.2xlarge.search"
SEARCH_R3_4XLARGE: Final = "r3.4xlarge.search"
SEARCH_R3_8XLARGE: Final = "r3.8xlarge.search"

SEARCH_R4_LARGE: Final = "r4.large.search"
SEARCH_R4_XLARGE: Final = "r4.xlarge.search"
SEARCH_R4_2XLARGE: Final = "r4.2xlarge.search"
SEARCH_R4_4XLARGE: Final = "r4.4xlarge.search"
SEARCH_R4_8XLARGE: Final = "r4.8xlarge.search"
SEARCH_R4_16XLARGE: Final = "r4.16xlarge.search"

SEARCH_R5_LARGE: Final = "r5.large.search"
SEARCH_R5_XLARGE: Final = "r5.xlarge.search"
SEARCH_R5_2XLARGE: Final = "r5.2xlarge.search"
SEARCH_R5_4XLARGE: Final = "r5.4xlarge.search"
SEARCH_R5_12XLARGE: Final = "r5.12xlarge.search"

SEARCH_R6G_LARGE: Final = "r6g.large.search"
SEARCH_R6G_XLARGE: Final = "r6g.xlarge.search"
SEARCH_R6G_2XLARGE: Final = "r6g.2xlarge.search"
SEARCH_R6G_4XLARGE: Final = "r6g.4xlarge.search"
SEARCH_R6G_8XLARGE: Final = "r6g.8xlarge.search"
SEARCH_R6G_12XLARGE: Final = "r6g.12xlarge.search"

SEARCH_R6GD_LARGE: Final = "r6gd.large.search"
SEARCH_R6GD_XLARGE: Final = "r6gd.xlarge.search"
SEARCH_R6GD_2XLARGE: Final = "r6gd.2xlarge.search"
SEARCH_R6GD_4XLARGE: Final = "r6gd.4xlarge.search"
SEARCH_R6GD_8XLARGE: Final = "r6gd.8xlarge.search"
SEARCH_R6GD_12XLARGE: Final = "r6gd.12xlarge.search"
SEARCH_R6GD_16XLARGE: Final = "r6gd.16xlarge.search"

SEARCH_T2_SMALL: Final = "t2.small.search"
SEARCH_T2_MEDIUM: Final = "t2.medium.search"

SEARCH_T3_SMALL: Final = "t3.small.search"
SEARCH_T3_MEDIUM: Final = "t3.medium.search"

SEARCH_ULTRAWARM1_MEDIUM: Final = "ultrawarm1.medium.search"
SEARCH_ULTRAWARM1_LARGE: Final = "ultrawarm1.large.search"

#
# Deprecated Elasticsearch instance types
#

# Note: t2.micro.elasticsearch is deprecated.
ELASTICSEARCH_T2_MICRO: Final = "t2.micro.elasticsearch"
ELASTICSEARCH_T2_SMALL: Final = "t2.small.elasticsearch"
ELASTICSEARCH_T2_MEDIUM: Final = "t2.medium.elasticsearch"

ELASTICSEARCH_T3_SMALL: Final = "t3.small.elasticsearch"
ELASTICSEARCH_T3_MEDIUM: Final = "t3.medium.elasticsearch"

ELASTICSEARCH_M3_MEDIUM: Final = "m3.medium.elasticsearch"
ELASTICSEARCH_M3_LARGE: Final = "m3.large.elasticsearch"
ELASTICSEARCH_M3_XLARGE: Final = "m3.xlarge.elasticsearch"
ELASTICSEARCH_M3_2XLARGE: Final = "m3.2xlarge.elasticsearch"

ELASTICSEARCH_M4_LARGE: Final = "m4.large.elasticsearch"
ELASTICSEARCH_M4_XLARGE: Final = "m4.xlarge.elasticsearch"
ELASTICSEARCH_M4_2XLARGE: Final = "m4.2xlarge.elasticsearch"
ELASTICSEARCH_M4_4XLARGE: Final = "m4.4xlarge.elasticsearch"
ELASTICSEARCH_M4_10XLARGE: Final = "m4.10xlarge.elasticsearch"

ELASTICSEARCH_M5_LARGE: Final = "m5.large.elasticsearch"
ELASTICSEARCH_M5_XLARGE: Final = "m5.xlarge.elasticsearch"
ELASTICSEARCH_M5_2XLARGE: Final = "m5.2xlarge.elasticsearch"
ELASTICSEARCH_M5_4XLARGE: Final = "m5.4xlarge.elasticsearch"
ELASTICSEARCH_M5_12XLARGE: Final = "m5.12xlarge.elasticsearch"

ELASTICSEARCH_M6G_LARGE: Final = "m6g.large.elasticsearch"
ELASTICSEARCH_M6G_XLARGE: Final = "m6g.xlarge.elasticsearch"
ELASTICSEARCH_M6G_2XLARGE: Final = "m6g.2xlarge.elasticsearch"
ELASTICSEARCH_M6G_4XLARGE: Final = "m6g.4xlarge.elasticsearch"
ELASTICSEARCH_M6G_8XLARGE: Final = "m6g.8xlarge.elasticsearch"
ELASTICSEARCH_M6G_12XLARGE: Final = "m6g.12xlarge.elasticsearch"

ELASTICSEARCH_C4_LARGE: Final = "c4.large.elasticsearch"
ELASTICSEARCH_C4_XLARGE: Final = "c4.xlarge.elasticsearch"
ELASTICSEARCH_C4_2XLARGE: Final = "c4.2xlarge.elasticsearch"
ELASTICSEARCH_C4_4XLARGE: Final = "c4.4xlarge.elasticsearch"
ELASTICSEARCH_C4_8XLARGE: Final = "c4.8xlarge.elasticsearch"

ELASTICSEARCH_C5_LARGE: Final = "c5.large.elasticsearch"
ELASTICSEARCH_C5_XLARGE: Final = "c5.xlarge.elasticsearch"
ELASTICSEARCH_C5_2XLARGE: Final = "c5.2xlarge.elasticsearch"
ELASTICSEARCH_C5_4XLARGE: Final = "c5.4xlarge.elasticsearch"
ELASTICSEARCH_C5_9XLARGE: Final = "c5.9xlarge.elasticsearch"
ELASTICSEARCH_C5_18XLARGE: Final = "c5.18xlarge.elasticsearch"

ELASTICSEARCH_C6G_LARGE: Final = "c6g.large.elasticsearch"
ELASTICSEARCH_C6G_XLARGE: Final = "c6g.xlarge.elasticsearch"
ELASTICSEARCH_C6G_2XLARGE: Final = "c6g.2xlarge.elasticsearch"
ELASTICSEARCH_C6G_4XLARGE: Final = "c6g.4xlarge.elasticsearch"
ELASTICSEARCH_C6G_8XLARGE: Final = "c6g.8xlarge.elasticsearch"
ELASTICSEARCH_C6G_12XLARGE: Final = "c6g.12xlarge.elasticsearch"

ELASTICSEARCH_R3_LARGE: Final = "r3.large.elasticsearch"
ELASTICSEARCH_R3_XLARGE: Final = "r3.xlarge.elasticsearch"
ELASTICSEARCH_R3_2XLARGE: Final = "r3.2xlarge.elasticsearch"
ELASTICSEARCH_R3_4XLARGE: Final = "r3.4xlarge.elasticsearch"
ELASTICSEARCH_R3_8XLARGE: Final = "r3.8xlarge.elasticsearch"

ELASTICSEARCH_R4_LARGE: Final = "r4.large.elasticsearch"
ELASTICSEARCH_R4_XLARGE: Final = "r4.xlarge.elasticsearch"
ELASTICSEARCH_R4_2XLARGE: Final = "r4.2xlarge.elasticsearch"
ELASTICSEARCH_R4_4XLARGE: Final = "r4.4xlarge.elasticsearch"
ELASTICSEARCH_R4_8XLARGE: Final = "r4.8xlarge.elasticsearch"
ELASTICSEARCH_R4_16XLARGE: Final = "r4.16xlarge.elasticsearch"

ELASTICSEARCH_R5_LARGE: Final = "r5.large.elasticsearch"
ELASTICSEARCH_R5_XLARGE: Final = "r5.xlarge.elasticsearch"
ELASTICSEARCH_R5_2XLARGE: Final = "r5.2xlarge.elasticsearch"
ELASTICSEARCH_R5_4XLARGE: Final = "r5.4xlarge.elasticsearch"
ELASTICSEARCH_R5_12XLARGE: Final = "r5.12xlarge.elasticsearch"

ELASTICSEARCH_R6G_LARGE: Final = "r6g.large.elasticsearch"
ELASTICSEARCH_R6G_XLARGE: Final = "r6g.xlarge.elasticsearch"
ELASTICSEARCH_R6G_2XLARGE: Final = "r6g.2xlarge.elasticsearch"
ELASTICSEARCH_R6G_4XLARGE: Final = "r6g.4xlarge.elasticsearch"
ELASTICSEARCH_R6G_8XLARGE: Final = "r6g.8xlarge.elasticsearch"
ELASTICSEARCH_R6G_12XLARGE: Final = "r6g.12xlarge.elasticsearch"

ELASTICSEARCH_R6GD_LARGE: Final = "r6gd.large.elasticsearch"
ELASTICSEARCH_R6GD_XLARGE: Final = "r6gd.xlarge.elasticsearch"
ELASTICSEARCH_R6GD_2XLARGE: Final = "r6gd.2xlarge.elasticsearch"
ELASTICSEARCH_R6GD_4XLARGE: Final = "r6gd.4xlarge.elasticsearch"
ELASTICSEARCH_R6GD_8XLARGE: Final = "r6gd.8xlarge.elasticsearch"
ELASTICSEARCH_R6GD_12XLARGE: Final = "r6gd.12xlarge.elasticsearch"
ELASTICSEARCH_R6GD_16XLARGE: Final = "r6gd.16xlarge.elasticsearch"

ELASTICSEARCH_I2_XLARGE: Final = "i2.xlarge.elasticsearch"
ELASTICSEARCH_I2_2XLARGE: Final = "i2.2xlarge.elasticsearch"

ELASTICSEARCH_I3_LARGE: Final = "i3.large.elasticsearch"
ELASTICSEARCH_I3_XLARGE: Final = "i3.xlarge.elasticsearch"
ELASTICSEARCH_I3_2XLARGE: Final = "i3.2xlarge.elasticsearch"
ELASTICSEARCH_I3_4XLARGE: Final = "i3.4xlarge.elasticsearch"
ELASTICSEARCH_I3_8XLARGE: Final = "i3.8xlarge.elasticsearch"
ELASTICSEARCH_I3_16XLARGE: Final = "i3.16xlarge.elasticsearch"

ELASTICSEARCH_ULTRAWARM1_MEDIUM: Final = "ultrawarm1.medium.elasticsearch"
ELASTICSEARCH_ULTRAWARM1_LARGE: Final = "ultrawarm1.large.elasticsearch"

#
# Kafka brokers instance types
#

KAFKA_M5_LARGE: Final = "kafka.m5.large"
KAFKA_M5_XLARGE: Final = "kafka.m5.xlarge"
KAFKA_M5_2XLARGE: Final = "kafka.m5.2xlarge"
KAFKA_M5_4XLARGE: Final = "kafka.m5.4xlarge"
KAFKA_M5_12XLARGE: Final = "kafka.m5.12xlarge"
KAFKA_M5_24XLARGE: Final = "kafka.m5.24xlarge"

#
# Parameter types
#

STRING: Final = "String"
NUMBER: Final = "Number"
LIST_OF_NUMBERS: Final = "List<Number>"
COMMA_DELIMITED_LIST: Final = "CommaDelimitedList"

AVAILABILITY_ZONE_NAME: Final = "AWS::EC2::AvailabilityZone::Name"
IMAGE_ID: Final = "AWS::EC2::Image::Id"
INSTANCE_ID: Final = "AWS::EC2::Instance::Id"
KEY_PAIR_NAME: Final = "AWS::EC2::KeyPair::KeyName"
SECURITY_GROUP_NAME: Final = "AWS::EC2::SecurityGroup::GroupName"
SECURITY_GROUP_ID: Final = "AWS::EC2::SecurityGroup::Id"
SUBNET_ID: Final = "AWS::EC2::Subnet::Id"
VOLUME_ID: Final = "AWS::EC2::Volume::Id"
VPC_ID: Final = "AWS::EC2::VPC::Id"
HOSTED_ZONE_ID: Final = "AWS::Route53::HostedZone::Id"

LIST_OF_AVAILABILITY_ZONE_NAMES: Final = "List<AWS::EC2::AvailabilityZone::Name>"
LIST_OF_IMAGE_ID: Final = "List<AWS::EC2::Image::Id>"
LIST_OF_INSTANCE_IDS: Final = "List<AWS::EC2::Instance::Id>"
LIST_OF_SECURITY_GROUP_NAMES: Final = "List<AWS::EC2::SecurityGroup::GroupName>"
LIST_OF_SECURITY_GROUP_IDS: Final = "List<AWS::EC2::SecurityGroup::Id>"
LIST_OF_SUBNET_IDS: Final = "List<AWS::EC2::Subnet::Id>"
LIST_OF_VOLUME_IDS: Final = "List<AWS::EC2::Volume::Id>"
LIST_OF_VPC_IDS: Final = "List<AWS::EC2::VPC::Id>"
LIST_OF_HOSTED_ZONE_IDS: Final = "List<AWS::Route53::HostedZone::Id>"

#
# Logs
#
LOGS_ALLOWED_RETENTION_DAYS: Final = [
    1,
    3,
    5,
    7,
    14,
    30,
    60,
    90,
    120,
    150,
    180,
    365,
    400,
    545,
    731,
    1096,
    1827,
    2192,
    2557,
    2922,
    3288,
    3653,
]

#
# Route53
#

CLOUDFRONT_HOSTEDZONEID: Final = "Z2FDTNDATAQYW2"
