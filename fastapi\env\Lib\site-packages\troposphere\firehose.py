# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer
from .validators.firehose import (
    delivery_stream_type_validator,
    index_rotation_period_validator,
    processor_type_validator,
    s3_backup_mode_elastic_search_validator,
    s3_backup_mode_extended_s3_validator,
)


class AmazonOpenSearchServerlessBufferingHints(AWSProperty):
    """
    `AmazonOpenSearchServerlessBufferingHints <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-amazonopensearchserverlessbufferinghints.html>`__
    """

    props: PropsDictType = {
        "IntervalInSeconds": (integer, False),
        "SizeInMBs": (integer, False),
    }


class AmazonOpenSearchServerlessRetryOptions(AWSProperty):
    """
    `AmazonOpenSearchServerlessRetryOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-amazonopensearchserverlessretryoptions.html>`__
    """

    props: PropsDictType = {
        "DurationInSeconds": (integer, False),
    }


class CloudWatchLoggingOptions(AWSProperty):
    """
    `CloudWatchLoggingOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-cloudwatchloggingoptions.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, False),
        "LogGroupName": (str, False),
        "LogStreamName": (str, False),
    }


class ProcessorParameter(AWSProperty):
    """
    `ProcessorParameter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-processorparameter.html>`__
    """

    props: PropsDictType = {
        "ParameterName": (str, True),
        "ParameterValue": (str, True),
    }


class Processor(AWSProperty):
    """
    `Processor <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-processor.html>`__
    """

    props: PropsDictType = {
        "Parameters": ([ProcessorParameter], False),
        "Type": (processor_type_validator, True),
    }


class ProcessingConfiguration(AWSProperty):
    """
    `ProcessingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-processingconfiguration.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, False),
        "Processors": ([Processor], False),
    }


class BufferingHints(AWSProperty):
    """
    `BufferingHints <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-bufferinghints.html>`__
    """

    props: PropsDictType = {
        "IntervalInSeconds": (integer, False),
        "SizeInMBs": (integer, False),
    }


class KMSEncryptionConfig(AWSProperty):
    """
    `KMSEncryptionConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-kmsencryptionconfig.html>`__
    """

    props: PropsDictType = {
        "AWSKMSKeyARN": (str, True),
    }


class EncryptionConfiguration(AWSProperty):
    """
    `EncryptionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-encryptionconfiguration.html>`__
    """

    props: PropsDictType = {
        "KMSEncryptionConfig": (KMSEncryptionConfig, False),
        "NoEncryptionConfig": (str, False),
    }


class S3DestinationConfiguration(AWSProperty):
    """
    `S3DestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-s3destinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "BucketARN": (str, True),
        "BufferingHints": (BufferingHints, False),
        "CloudWatchLoggingOptions": (CloudWatchLoggingOptions, False),
        "CompressionFormat": (str, False),
        "EncryptionConfiguration": (EncryptionConfiguration, False),
        "ErrorOutputPrefix": (str, False),
        "Prefix": (str, False),
        "RoleARN": (str, True),
    }


class VpcConfiguration(AWSProperty):
    """
    `VpcConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-vpcconfiguration.html>`__
    """

    props: PropsDictType = {
        "RoleARN": (str, True),
        "SecurityGroupIds": ([str], True),
        "SubnetIds": ([str], True),
    }


class AmazonOpenSearchServerlessDestinationConfiguration(AWSProperty):
    """
    `AmazonOpenSearchServerlessDestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-amazonopensearchserverlessdestinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "BufferingHints": (AmazonOpenSearchServerlessBufferingHints, False),
        "CloudWatchLoggingOptions": (CloudWatchLoggingOptions, False),
        "CollectionEndpoint": (str, False),
        "IndexName": (str, True),
        "ProcessingConfiguration": (ProcessingConfiguration, False),
        "RetryOptions": (AmazonOpenSearchServerlessRetryOptions, False),
        "RoleARN": (str, True),
        "S3BackupMode": (str, False),
        "S3Configuration": (S3DestinationConfiguration, True),
        "VpcConfiguration": (VpcConfiguration, False),
    }


class AmazonopensearchserviceBufferingHints(AWSProperty):
    """
    `AmazonopensearchserviceBufferingHints <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-amazonopensearchservicebufferinghints.html>`__
    """

    props: PropsDictType = {
        "IntervalInSeconds": (integer, False),
        "SizeInMBs": (integer, False),
    }


class AmazonopensearchserviceRetryOptions(AWSProperty):
    """
    `AmazonopensearchserviceRetryOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-amazonopensearchserviceretryoptions.html>`__
    """

    props: PropsDictType = {
        "DurationInSeconds": (integer, False),
    }


class DocumentIdOptions(AWSProperty):
    """
    `DocumentIdOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-documentidoptions.html>`__
    """

    props: PropsDictType = {
        "DefaultDocumentIdFormat": (str, True),
    }


class AmazonopensearchserviceDestinationConfiguration(AWSProperty):
    """
    `AmazonopensearchserviceDestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-amazonopensearchservicedestinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "BufferingHints": (AmazonopensearchserviceBufferingHints, False),
        "CloudWatchLoggingOptions": (CloudWatchLoggingOptions, False),
        "ClusterEndpoint": (str, False),
        "DocumentIdOptions": (DocumentIdOptions, False),
        "DomainARN": (str, False),
        "IndexName": (str, True),
        "IndexRotationPeriod": (str, False),
        "ProcessingConfiguration": (ProcessingConfiguration, False),
        "RetryOptions": (AmazonopensearchserviceRetryOptions, False),
        "RoleARN": (str, True),
        "S3BackupMode": (str, False),
        "S3Configuration": (S3DestinationConfiguration, True),
        "TypeName": (str, False),
        "VpcConfiguration": (VpcConfiguration, False),
    }


class DatabaseColumns(AWSProperty):
    """
    `DatabaseColumns <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-databasecolumns.html>`__
    """

    props: PropsDictType = {
        "Exclude": ([str], False),
        "Include": ([str], False),
    }


class SecretsManagerConfiguration(AWSProperty):
    """
    `SecretsManagerConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-secretsmanagerconfiguration.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, True),
        "RoleARN": (str, False),
        "SecretARN": (str, False),
    }


class DatabaseSourceAuthenticationConfiguration(AWSProperty):
    """
    `DatabaseSourceAuthenticationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-databasesourceauthenticationconfiguration.html>`__
    """

    props: PropsDictType = {
        "SecretsManagerConfiguration": (SecretsManagerConfiguration, True),
    }


class DatabaseSourceVPCConfiguration(AWSProperty):
    """
    `DatabaseSourceVPCConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-databasesourcevpcconfiguration.html>`__
    """

    props: PropsDictType = {
        "VpcEndpointServiceName": (str, True),
    }


class DatabaseTables(AWSProperty):
    """
    `DatabaseTables <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-databasetables.html>`__
    """

    props: PropsDictType = {
        "Exclude": ([str], False),
        "Include": ([str], False),
    }


class Databases(AWSProperty):
    """
    `Databases <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-databases.html>`__
    """

    props: PropsDictType = {
        "Exclude": ([str], False),
        "Include": ([str], False),
    }


class DatabaseSourceConfiguration(AWSProperty):
    """
    `DatabaseSourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-databasesourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "Columns": (DatabaseColumns, False),
        "DatabaseSourceAuthenticationConfiguration": (
            DatabaseSourceAuthenticationConfiguration,
            True,
        ),
        "DatabaseSourceVPCConfiguration": (DatabaseSourceVPCConfiguration, True),
        "Databases": (Databases, True),
        "Digest": (str, False),
        "Endpoint": (str, True),
        "Port": (integer, True),
        "PublicCertificate": (str, False),
        "SSLMode": (str, False),
        "SnapshotWatermarkTable": (str, True),
        "SurrogateKeys": ([str], False),
        "Tables": (DatabaseTables, True),
        "Type": (str, True),
    }


class DeliveryStreamEncryptionConfigurationInput(AWSProperty):
    """
    `DeliveryStreamEncryptionConfigurationInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-deliverystreamencryptionconfigurationinput.html>`__
    """

    props: PropsDictType = {
        "KeyARN": (str, False),
        "KeyType": (str, True),
    }


class DirectPutSourceConfiguration(AWSProperty):
    """
    `DirectPutSourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-directputsourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "ThroughputHintInMBs": (integer, False),
    }


class RetryOptions(AWSProperty):
    """
    `RetryOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-retryoptions.html>`__
    """

    props: PropsDictType = {
        "DurationInSeconds": (integer, False),
    }


class S3Configuration(AWSProperty):
    """
    `S3Configuration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-s3destinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "BucketARN": (str, True),
        "BufferingHints": (BufferingHints, False),
        "CloudWatchLoggingOptions": (CloudWatchLoggingOptions, False),
        "CompressionFormat": (str, False),
        "EncryptionConfiguration": (EncryptionConfiguration, False),
        "ErrorOutputPrefix": (str, False),
        "Prefix": (str, False),
        "RoleARN": (str, True),
    }


class ElasticsearchDestinationConfiguration(AWSProperty):
    """
    `ElasticsearchDestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-elasticsearchdestinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "BufferingHints": (BufferingHints, False),
        "CloudWatchLoggingOptions": (CloudWatchLoggingOptions, False),
        "ClusterEndpoint": (str, False),
        "DocumentIdOptions": (DocumentIdOptions, False),
        "DomainARN": (str, False),
        "IndexName": (str, True),
        "IndexRotationPeriod": (index_rotation_period_validator, False),
        "ProcessingConfiguration": (ProcessingConfiguration, False),
        "RetryOptions": (RetryOptions, False),
        "RoleARN": (str, True),
        "S3BackupMode": (s3_backup_mode_elastic_search_validator, False),
        "S3Configuration": (S3Configuration, True),
        "TypeName": (str, False),
        "VpcConfiguration": (VpcConfiguration, False),
    }


class HiveJsonSerDe(AWSProperty):
    """
    `HiveJsonSerDe <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-hivejsonserde.html>`__
    """

    props: PropsDictType = {
        "TimestampFormats": ([str], False),
    }


class OpenXJsonSerDe(AWSProperty):
    """
    `OpenXJsonSerDe <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-openxjsonserde.html>`__
    """

    props: PropsDictType = {
        "CaseInsensitive": (boolean, False),
        "ColumnToJsonKeyMappings": (dict, False),
        "ConvertDotsInJsonKeysToUnderscores": (boolean, False),
    }


class Deserializer(AWSProperty):
    """
    `Deserializer <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-deserializer.html>`__
    """

    props: PropsDictType = {
        "HiveJsonSerDe": (HiveJsonSerDe, False),
        "OpenXJsonSerDe": (OpenXJsonSerDe, False),
    }


class InputFormatConfiguration(AWSProperty):
    """
    `InputFormatConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-inputformatconfiguration.html>`__
    """

    props: PropsDictType = {
        "Deserializer": (Deserializer, False),
    }


class OrcSerDe(AWSProperty):
    """
    `OrcSerDe <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-orcserde.html>`__
    """

    props: PropsDictType = {
        "BlockSizeBytes": (integer, False),
        "BloomFilterColumns": ([str], False),
        "BloomFilterFalsePositiveProbability": (double, False),
        "Compression": (str, False),
        "DictionaryKeyThreshold": (double, False),
        "EnablePadding": (boolean, False),
        "FormatVersion": (str, False),
        "PaddingTolerance": (double, False),
        "RowIndexStride": (integer, False),
        "StripeSizeBytes": (integer, False),
    }


class ParquetSerDe(AWSProperty):
    """
    `ParquetSerDe <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-parquetserde.html>`__
    """

    props: PropsDictType = {
        "BlockSizeBytes": (integer, False),
        "Compression": (str, False),
        "EnableDictionaryCompression": (boolean, False),
        "MaxPaddingBytes": (integer, False),
        "PageSizeBytes": (integer, False),
        "WriterVersion": (str, False),
    }


class Serializer(AWSProperty):
    """
    `Serializer <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-serializer.html>`__
    """

    props: PropsDictType = {
        "OrcSerDe": (OrcSerDe, False),
        "ParquetSerDe": (ParquetSerDe, False),
    }


class OutputFormatConfiguration(AWSProperty):
    """
    `OutputFormatConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-outputformatconfiguration.html>`__
    """

    props: PropsDictType = {
        "Serializer": (Serializer, False),
    }


class SchemaConfiguration(AWSProperty):
    """
    `SchemaConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-schemaconfiguration.html>`__
    """

    props: PropsDictType = {
        "CatalogId": (str, False),
        "DatabaseName": (str, False),
        "Region": (str, False),
        "RoleARN": (str, False),
        "TableName": (str, False),
        "VersionId": (str, False),
    }


class DataFormatConversionConfiguration(AWSProperty):
    """
    `DataFormatConversionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-dataformatconversionconfiguration.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, False),
        "InputFormatConfiguration": (InputFormatConfiguration, False),
        "OutputFormatConfiguration": (OutputFormatConfiguration, False),
        "SchemaConfiguration": (SchemaConfiguration, False),
    }


class DynamicPartitioningConfiguration(AWSProperty):
    """
    `DynamicPartitioningConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-dynamicpartitioningconfiguration.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, False),
        "RetryOptions": (RetryOptions, False),
    }


class ExtendedS3DestinationConfiguration(AWSProperty):
    """
    `ExtendedS3DestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-extendeds3destinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "BucketARN": (str, True),
        "BufferingHints": (BufferingHints, False),
        "CloudWatchLoggingOptions": (CloudWatchLoggingOptions, False),
        "CompressionFormat": (str, False),
        "CustomTimeZone": (str, False),
        "DataFormatConversionConfiguration": (DataFormatConversionConfiguration, False),
        "DynamicPartitioningConfiguration": (DynamicPartitioningConfiguration, False),
        "EncryptionConfiguration": (EncryptionConfiguration, False),
        "ErrorOutputPrefix": (str, False),
        "FileExtension": (str, False),
        "Prefix": (str, False),
        "ProcessingConfiguration": (ProcessingConfiguration, False),
        "RoleARN": (str, True),
        "S3BackupConfiguration": (S3DestinationConfiguration, False),
        "S3BackupMode": (s3_backup_mode_extended_s3_validator, False),
    }


class HttpEndpointConfiguration(AWSProperty):
    """
    `HttpEndpointConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-httpendpointconfiguration.html>`__
    """

    props: PropsDictType = {
        "AccessKey": (str, False),
        "Name": (str, False),
        "Url": (str, True),
    }


class HttpEndpointCommonAttribute(AWSProperty):
    """
    `HttpEndpointCommonAttribute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-httpendpointcommonattribute.html>`__
    """

    props: PropsDictType = {
        "AttributeName": (str, True),
        "AttributeValue": (str, True),
    }


class HttpEndpointRequestConfiguration(AWSProperty):
    """
    `HttpEndpointRequestConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-httpendpointrequestconfiguration.html>`__
    """

    props: PropsDictType = {
        "CommonAttributes": ([HttpEndpointCommonAttribute], False),
        "ContentEncoding": (str, False),
    }


class HttpEndpointDestinationConfiguration(AWSProperty):
    """
    `HttpEndpointDestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-httpendpointdestinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "BufferingHints": (BufferingHints, False),
        "CloudWatchLoggingOptions": (CloudWatchLoggingOptions, False),
        "EndpointConfiguration": (HttpEndpointConfiguration, True),
        "ProcessingConfiguration": (ProcessingConfiguration, False),
        "RequestConfiguration": (HttpEndpointRequestConfiguration, False),
        "RetryOptions": (RetryOptions, False),
        "RoleARN": (str, False),
        "S3BackupMode": (str, False),
        "S3Configuration": (S3DestinationConfiguration, True),
        "SecretsManagerConfiguration": (SecretsManagerConfiguration, False),
    }


class CatalogConfiguration(AWSProperty):
    """
    `CatalogConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-catalogconfiguration.html>`__
    """

    props: PropsDictType = {
        "CatalogArn": (str, False),
    }


class DestinationTableConfiguration(AWSProperty):
    """
    `DestinationTableConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-destinationtableconfiguration.html>`__
    """

    props: PropsDictType = {
        "DestinationDatabaseName": (str, True),
        "DestinationTableName": (str, True),
        "S3ErrorOutputPrefix": (str, False),
        "UniqueKeys": ([str], False),
    }


class IcebergDestinationConfiguration(AWSProperty):
    """
    `IcebergDestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-icebergdestinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "AppendOnly": (boolean, False),
        "BufferingHints": (BufferingHints, False),
        "CatalogConfiguration": (CatalogConfiguration, True),
        "CloudWatchLoggingOptions": (CloudWatchLoggingOptions, False),
        "DestinationTableConfigurationList": ([DestinationTableConfiguration], False),
        "ProcessingConfiguration": (ProcessingConfiguration, False),
        "RetryOptions": (RetryOptions, False),
        "RoleARN": (str, True),
        "S3Configuration": (S3DestinationConfiguration, True),
        "s3BackupMode": (str, False),
    }


class KinesisStreamSourceConfiguration(AWSProperty):
    """
    `KinesisStreamSourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-kinesisstreamsourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "KinesisStreamARN": (str, True),
        "RoleARN": (str, True),
    }


class AuthenticationConfiguration(AWSProperty):
    """
    `AuthenticationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-authenticationconfiguration.html>`__
    """

    props: PropsDictType = {
        "Connectivity": (str, True),
        "RoleARN": (str, True),
    }


class MSKSourceConfiguration(AWSProperty):
    """
    `MSKSourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-msksourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "AuthenticationConfiguration": (AuthenticationConfiguration, True),
        "MSKClusterARN": (str, True),
        "ReadFromTimestamp": (str, False),
        "TopicName": (str, True),
    }


class CopyCommand(AWSProperty):
    """
    `CopyCommand <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-copycommand.html>`__
    """

    props: PropsDictType = {
        "CopyOptions": (str, False),
        "DataTableColumns": (str, False),
        "DataTableName": (str, True),
    }


class RedshiftRetryOptions(AWSProperty):
    """
    `RedshiftRetryOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-redshiftretryoptions.html>`__
    """

    props: PropsDictType = {
        "DurationInSeconds": (integer, False),
    }


class RedshiftDestinationConfiguration(AWSProperty):
    """
    `RedshiftDestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-redshiftdestinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "CloudWatchLoggingOptions": (CloudWatchLoggingOptions, False),
        "ClusterJDBCURL": (str, True),
        "CopyCommand": (CopyCommand, True),
        "Password": (str, False),
        "ProcessingConfiguration": (ProcessingConfiguration, False),
        "RetryOptions": (RedshiftRetryOptions, False),
        "RoleARN": (str, True),
        "S3BackupConfiguration": (S3DestinationConfiguration, False),
        "S3BackupMode": (str, False),
        "S3Configuration": (S3Configuration, True),
        "SecretsManagerConfiguration": (SecretsManagerConfiguration, False),
        "Username": (str, False),
    }


class SnowflakeBufferingHints(AWSProperty):
    """
    `SnowflakeBufferingHints <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-snowflakebufferinghints.html>`__
    """

    props: PropsDictType = {
        "IntervalInSeconds": (integer, False),
        "SizeInMBs": (integer, False),
    }


class SnowflakeRetryOptions(AWSProperty):
    """
    `SnowflakeRetryOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-snowflakeretryoptions.html>`__
    """

    props: PropsDictType = {
        "DurationInSeconds": (integer, False),
    }


class SnowflakeRoleConfiguration(AWSProperty):
    """
    `SnowflakeRoleConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-snowflakeroleconfiguration.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, False),
        "SnowflakeRole": (str, False),
    }


class SnowflakeVpcConfiguration(AWSProperty):
    """
    `SnowflakeVpcConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-snowflakevpcconfiguration.html>`__
    """

    props: PropsDictType = {
        "PrivateLinkVpceId": (str, True),
    }


class SnowflakeDestinationConfiguration(AWSProperty):
    """
    `SnowflakeDestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-snowflakedestinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "AccountUrl": (str, True),
        "BufferingHints": (SnowflakeBufferingHints, False),
        "CloudWatchLoggingOptions": (CloudWatchLoggingOptions, False),
        "ContentColumnName": (str, False),
        "DataLoadingOption": (str, False),
        "Database": (str, True),
        "KeyPassphrase": (str, False),
        "MetaDataColumnName": (str, False),
        "PrivateKey": (str, False),
        "ProcessingConfiguration": (ProcessingConfiguration, False),
        "RetryOptions": (SnowflakeRetryOptions, False),
        "RoleARN": (str, True),
        "S3BackupMode": (str, False),
        "S3Configuration": (S3DestinationConfiguration, True),
        "Schema": (str, True),
        "SecretsManagerConfiguration": (SecretsManagerConfiguration, False),
        "SnowflakeRoleConfiguration": (SnowflakeRoleConfiguration, False),
        "SnowflakeVpcConfiguration": (SnowflakeVpcConfiguration, False),
        "Table": (str, True),
        "User": (str, False),
    }


class SplunkBufferingHints(AWSProperty):
    """
    `SplunkBufferingHints <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-splunkbufferinghints.html>`__
    """

    props: PropsDictType = {
        "IntervalInSeconds": (integer, False),
        "SizeInMBs": (integer, False),
    }


class SplunkRetryOptions(AWSProperty):
    """
    `SplunkRetryOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-splunkretryoptions.html>`__
    """

    props: PropsDictType = {
        "DurationInSeconds": (integer, False),
    }


class SplunkDestinationConfiguration(AWSProperty):
    """
    `SplunkDestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisfirehose-deliverystream-splunkdestinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "BufferingHints": (SplunkBufferingHints, False),
        "CloudWatchLoggingOptions": (CloudWatchLoggingOptions, False),
        "HECAcknowledgmentTimeoutInSeconds": (integer, False),
        "HECEndpoint": (str, True),
        "HECEndpointType": (str, True),
        "HECToken": (str, False),
        "ProcessingConfiguration": (ProcessingConfiguration, False),
        "RetryOptions": (SplunkRetryOptions, False),
        "S3BackupMode": (str, False),
        "S3Configuration": (S3DestinationConfiguration, True),
        "SecretsManagerConfiguration": (SecretsManagerConfiguration, False),
    }


class DeliveryStream(AWSObject):
    """
    `DeliveryStream <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-kinesisfirehose-deliverystream.html>`__
    """

    resource_type = "AWS::KinesisFirehose::DeliveryStream"

    props: PropsDictType = {
        "AmazonOpenSearchServerlessDestinationConfiguration": (
            AmazonOpenSearchServerlessDestinationConfiguration,
            False,
        ),
        "AmazonopensearchserviceDestinationConfiguration": (
            AmazonopensearchserviceDestinationConfiguration,
            False,
        ),
        "DatabaseSourceConfiguration": (DatabaseSourceConfiguration, False),
        "DeliveryStreamEncryptionConfigurationInput": (
            DeliveryStreamEncryptionConfigurationInput,
            False,
        ),
        "DeliveryStreamName": (str, False),
        "DeliveryStreamType": (delivery_stream_type_validator, False),
        "DirectPutSourceConfiguration": (DirectPutSourceConfiguration, False),
        "ElasticsearchDestinationConfiguration": (
            ElasticsearchDestinationConfiguration,
            False,
        ),
        "ExtendedS3DestinationConfiguration": (
            ExtendedS3DestinationConfiguration,
            False,
        ),
        "HttpEndpointDestinationConfiguration": (
            HttpEndpointDestinationConfiguration,
            False,
        ),
        "IcebergDestinationConfiguration": (IcebergDestinationConfiguration, False),
        "KinesisStreamSourceConfiguration": (KinesisStreamSourceConfiguration, False),
        "MSKSourceConfiguration": (MSKSourceConfiguration, False),
        "RedshiftDestinationConfiguration": (RedshiftDestinationConfiguration, False),
        "S3DestinationConfiguration": (S3DestinationConfiguration, False),
        "SnowflakeDestinationConfiguration": (SnowflakeDestinationConfiguration, False),
        "SplunkDestinationConfiguration": (SplunkDestinationConfiguration, False),
        "Tags": (Tags, False),
    }
