# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType
from .validators import boolean, integer


class Grant(AWSObject):
    """
    `Grant <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-licensemanager-grant.html>`__
    """

    resource_type = "AWS::LicenseManager::Grant"

    props: PropsDictType = {
        "AllowedOperations": ([str], False),
        "GrantName": (str, False),
        "HomeRegion": (str, False),
        "LicenseArn": (str, False),
        "Principals": ([str], False),
        "Status": (str, False),
    }


class BorrowConfiguration(AWSProperty):
    """
    `BorrowConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-licensemanager-license-borrowconfiguration.html>`__
    """

    props: PropsDictType = {
        "AllowEarlyCheckIn": (boolean, True),
        "MaxTimeToLiveInMinutes": (integer, True),
    }


class ProvisionalConfiguration(AWSProperty):
    """
    `ProvisionalConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-licensemanager-license-provisionalconfiguration.html>`__
    """

    props: PropsDictType = {
        "MaxTimeToLiveInMinutes": (integer, True),
    }


class ConsumptionConfiguration(AWSProperty):
    """
    `ConsumptionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-licensemanager-license-consumptionconfiguration.html>`__
    """

    props: PropsDictType = {
        "BorrowConfiguration": (BorrowConfiguration, False),
        "ProvisionalConfiguration": (ProvisionalConfiguration, False),
        "RenewType": (str, False),
    }


class Entitlement(AWSProperty):
    """
    `Entitlement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-licensemanager-license-entitlement.html>`__
    """

    props: PropsDictType = {
        "AllowCheckIn": (boolean, False),
        "MaxCount": (integer, False),
        "Name": (str, True),
        "Overage": (boolean, False),
        "Unit": (str, True),
        "Value": (str, False),
    }


class IssuerData(AWSProperty):
    """
    `IssuerData <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-licensemanager-license-issuerdata.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "SignKey": (str, False),
    }


class Metadata(AWSProperty):
    """
    `Metadata <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-licensemanager-license-metadata.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "Value": (str, True),
    }


class ValidityDateFormat(AWSProperty):
    """
    `ValidityDateFormat <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-licensemanager-license-validitydateformat.html>`__
    """

    props: PropsDictType = {
        "Begin": (str, True),
        "End": (str, True),
    }


class License(AWSObject):
    """
    `License <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-licensemanager-license.html>`__
    """

    resource_type = "AWS::LicenseManager::License"

    props: PropsDictType = {
        "Beneficiary": (str, False),
        "ConsumptionConfiguration": (ConsumptionConfiguration, True),
        "Entitlements": ([Entitlement], True),
        "HomeRegion": (str, True),
        "Issuer": (IssuerData, True),
        "LicenseMetadata": ([Metadata], False),
        "LicenseName": (str, True),
        "ProductName": (str, True),
        "ProductSKU": (str, False),
        "Status": (str, False),
        "Validity": (ValidityDateFormat, True),
    }
