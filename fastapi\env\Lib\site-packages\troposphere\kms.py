# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, PropsDictType, Tags
from .validators import boolean, integer
from .validators.kms import (
    key_usage_type,
    policytypes,
    validate_pending_window_in_days,
    validate_tags_or_list,
)


class Alias(AWSObject):
    """
    `Alias <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-kms-alias.html>`__
    """

    resource_type = "AWS::KMS::Alias"

    props: PropsDictType = {
        "AliasName": (str, True),
        "TargetKeyId": (str, True),
    }


class Key(AWSObject):
    """
    `Key <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-kms-key.html>`__
    """

    resource_type = "AWS::KMS::Key"

    props: PropsDictType = {
        "BypassPolicyLockoutSafetyCheck": (boolean, False),
        "Description": (str, False),
        "EnableKeyRotation": (boolean, False),
        "Enabled": (boolean, False),
        "KeyPolicy": (policytypes, False),
        "KeySpec": (str, False),
        "KeyUsage": (key_usage_type, False),
        "MultiRegion": (boolean, False),
        "Origin": (str, False),
        "PendingWindowInDays": (validate_pending_window_in_days, False),
        "RotationPeriodInDays": (integer, False),
        "Tags": (validate_tags_or_list, False),
    }


class ReplicaKey(AWSObject):
    """
    `ReplicaKey <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-kms-replicakey.html>`__
    """

    resource_type = "AWS::KMS::ReplicaKey"

    props: PropsDictType = {
        "Description": (str, False),
        "Enabled": (boolean, False),
        "KeyPolicy": (dict, True),
        "PendingWindowInDays": (integer, False),
        "PrimaryKeyArn": (str, True),
        "Tags": (Tags, False),
    }
