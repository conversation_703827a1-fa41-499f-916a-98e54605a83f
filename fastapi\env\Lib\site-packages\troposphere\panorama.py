# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean


class ManifestOverridesPayload(AWSProperty):
    """
    `ManifestOverridesPayload <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-panorama-applicationinstance-manifestoverridespayload.html>`__
    """

    props: PropsDictType = {
        "PayloadData": (str, False),
    }


class ManifestPayload(AWSProperty):
    """
    `ManifestPayload <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-panorama-applicationinstance-manifestpayload.html>`__
    """

    props: PropsDictType = {
        "PayloadData": (str, False),
    }


class ApplicationInstance(AWSObject):
    """
    `ApplicationInstance <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-panorama-applicationinstance.html>`__
    """

    resource_type = "AWS::Panorama::ApplicationInstance"

    props: PropsDictType = {
        "ApplicationInstanceIdToReplace": (str, False),
        "DefaultRuntimeContextDevice": (str, True),
        "Description": (str, False),
        "ManifestOverridesPayload": (ManifestOverridesPayload, False),
        "ManifestPayload": (ManifestPayload, True),
        "Name": (str, False),
        "RuntimeRoleArn": (str, False),
        "Tags": (Tags, False),
    }


class StorageLocation(AWSProperty):
    """
    `StorageLocation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-panorama-package-storagelocation.html>`__
    """

    props: PropsDictType = {
        "BinaryPrefixLocation": (str, False),
        "Bucket": (str, False),
        "GeneratedPrefixLocation": (str, False),
        "ManifestPrefixLocation": (str, False),
        "RepoPrefixLocation": (str, False),
    }


class Package(AWSObject):
    """
    `Package <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-panorama-package.html>`__
    """

    resource_type = "AWS::Panorama::Package"

    props: PropsDictType = {
        "PackageName": (str, True),
        "StorageLocation": (StorageLocation, False),
        "Tags": (Tags, False),
    }


class PackageVersion(AWSObject):
    """
    `PackageVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-panorama-packageversion.html>`__
    """

    resource_type = "AWS::Panorama::PackageVersion"

    props: PropsDictType = {
        "MarkLatest": (boolean, False),
        "OwnerAccount": (str, False),
        "PackageId": (str, True),
        "PackageVersion": (str, True),
        "PatchVersion": (str, True),
        "UpdatedLatestPatchVersion": (str, False),
    }
