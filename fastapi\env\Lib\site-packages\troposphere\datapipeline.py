# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType
from .validators import boolean


class ParameterObjectAttribute(AWSProperty):
    """
    `ParameterObjectAttribute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datapipeline-pipeline-parameterattribute.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "StringValue": (str, True),
    }


class ParameterObject(AWSProperty):
    """
    `ParameterObject <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datapipeline-pipeline-parameterobject.html>`__
    """

    props: PropsDictType = {
        "Attributes": ([ParameterObjectAttribute], True),
        "Id": (str, True),
    }


class ParameterValue(AWSProperty):
    """
    `ParameterValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datapipeline-pipeline-parametervalue.html>`__
    """

    props: PropsDictType = {
        "Id": (str, True),
        "StringValue": (str, True),
    }


class ObjectField(AWSProperty):
    """
    `ObjectField <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datapipeline-pipeline-field.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "RefValue": (str, False),
        "StringValue": (str, False),
    }


class PipelineObject(AWSProperty):
    """
    `PipelineObject <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datapipeline-pipeline-pipelineobject.html>`__
    """

    props: PropsDictType = {
        "Fields": ([ObjectField], True),
        "Id": (str, True),
        "Name": (str, True),
    }


class PipelineTag(AWSProperty):
    """
    `PipelineTag <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-datapipeline-pipeline-pipelinetag.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (str, True),
    }


class Pipeline(AWSObject):
    """
    `Pipeline <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-datapipeline-pipeline.html>`__
    """

    resource_type = "AWS::DataPipeline::Pipeline"

    props: PropsDictType = {
        "Activate": (boolean, False),
        "Description": (str, False),
        "Name": (str, True),
        "ParameterObjects": ([ParameterObject], False),
        "ParameterValues": ([ParameterValue], False),
        "PipelineObjects": ([PipelineObject], False),
        "PipelineTags": ([PipelineTag], False),
    }
