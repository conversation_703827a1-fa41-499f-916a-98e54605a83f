# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType
from .validators import boolean


class S3(AWSProperty):
    """
    `S3 <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codestar-githubrepository-s3.html>`__
    """

    props: PropsDictType = {
        "Bucket": (str, True),
        "Key": (str, True),
        "ObjectVersion": (str, False),
    }


class Code(AWSProperty):
    """
    `Code <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codestar-githubrepository-code.html>`__
    """

    props: PropsDictType = {
        "S3": (S3, True),
    }


class GitHubRepository(AWSObject):
    """
    `GitHubRepository <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-codestar-githubrepository.html>`__
    """

    resource_type = "AWS::CodeStar::GitHubRepository"

    props: PropsDictType = {
        "Code": (Code, False),
        "ConnectionArn": (str, False),
        "EnableIssues": (boolean, False),
        "IsPrivate": (boolean, False),
        "RepositoryAccessToken": (str, False),
        "RepositoryDescription": (str, False),
        "RepositoryName": (str, True),
        "RepositoryOwner": (str, True),
    }
