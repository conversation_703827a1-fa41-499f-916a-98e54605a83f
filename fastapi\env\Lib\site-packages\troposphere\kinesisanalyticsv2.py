# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, integer
from .validators.kinesisanalyticsv2 import validate_runtime_environment


class S3ContentLocation(AWSProperty):
    """
    `S3ContentLocation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-s3contentlocation.html>`__
    """

    props: PropsDictType = {
        "BucketARN": (str, True),
        "FileKey": (str, True),
        "ObjectVersion": (str, False),
    }


class CodeContent(AWSProperty):
    """
    `CodeContent <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-codecontent.html>`__
    """

    props: PropsDictType = {
        "S3ContentLocation": (S3ContentLocation, False),
        "TextContent": (str, False),
        "ZipFileContent": (str, False),
    }


class ApplicationCodeConfiguration(AWSProperty):
    """
    `ApplicationCodeConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-applicationcodeconfiguration.html>`__
    """

    props: PropsDictType = {
        "CodeContent": (CodeContent, True),
        "CodeContentType": (str, True),
    }


class ApplicationSnapshotConfiguration(AWSProperty):
    """
    `ApplicationSnapshotConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-applicationsnapshotconfiguration.html>`__
    """

    props: PropsDictType = {
        "SnapshotsEnabled": (boolean, True),
    }


class ApplicationSystemRollbackConfiguration(AWSProperty):
    """
    `ApplicationSystemRollbackConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-applicationsystemrollbackconfiguration.html>`__
    """

    props: PropsDictType = {
        "RollbackEnabled": (boolean, True),
    }


class PropertyGroup(AWSProperty):
    """
    `PropertyGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-propertygroup.html>`__
    """

    props: PropsDictType = {
        "PropertyGroupId": (str, False),
        "PropertyMap": (dict, False),
    }


class EnvironmentProperties(AWSProperty):
    """
    `EnvironmentProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-environmentproperties.html>`__
    """

    props: PropsDictType = {
        "PropertyGroups": ([PropertyGroup], False),
    }


class CheckpointConfiguration(AWSProperty):
    """
    `CheckpointConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-checkpointconfiguration.html>`__
    """

    props: PropsDictType = {
        "CheckpointInterval": (integer, False),
        "CheckpointingEnabled": (boolean, False),
        "ConfigurationType": (str, True),
        "MinPauseBetweenCheckpoints": (integer, False),
    }


class MonitoringConfiguration(AWSProperty):
    """
    `MonitoringConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-monitoringconfiguration.html>`__
    """

    props: PropsDictType = {
        "ConfigurationType": (str, True),
        "LogLevel": (str, False),
        "MetricsLevel": (str, False),
    }


class ParallelismConfiguration(AWSProperty):
    """
    `ParallelismConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-parallelismconfiguration.html>`__
    """

    props: PropsDictType = {
        "AutoScalingEnabled": (boolean, False),
        "ConfigurationType": (str, True),
        "Parallelism": (integer, False),
        "ParallelismPerKPU": (integer, False),
    }


class FlinkApplicationConfiguration(AWSProperty):
    """
    `FlinkApplicationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-flinkapplicationconfiguration.html>`__
    """

    props: PropsDictType = {
        "CheckpointConfiguration": (CheckpointConfiguration, False),
        "MonitoringConfiguration": (MonitoringConfiguration, False),
        "ParallelismConfiguration": (ParallelismConfiguration, False),
    }


class InputParallelism(AWSProperty):
    """
    `InputParallelism <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-inputparallelism.html>`__
    """

    props: PropsDictType = {
        "Count": (integer, False),
    }


class InputLambdaProcessor(AWSProperty):
    """
    `InputLambdaProcessor <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-inputlambdaprocessor.html>`__
    """

    props: PropsDictType = {
        "ResourceARN": (str, True),
    }


class InputProcessingConfiguration(AWSProperty):
    """
    `InputProcessingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-inputprocessingconfiguration.html>`__
    """

    props: PropsDictType = {
        "InputLambdaProcessor": (InputLambdaProcessor, False),
    }


class RecordColumn(AWSProperty):
    """
    `RecordColumn <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-applicationreferencedatasource-recordcolumn.html>`__
    """

    props: PropsDictType = {
        "Mapping": (str, False),
        "Name": (str, True),
        "SqlType": (str, True),
    }


class CSVMappingParameters(AWSProperty):
    """
    `CSVMappingParameters <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-applicationreferencedatasource-csvmappingparameters.html>`__
    """

    props: PropsDictType = {
        "RecordColumnDelimiter": (str, True),
        "RecordRowDelimiter": (str, True),
    }


class JSONMappingParameters(AWSProperty):
    """
    `JSONMappingParameters <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-applicationreferencedatasource-jsonmappingparameters.html>`__
    """

    props: PropsDictType = {
        "RecordRowPath": (str, True),
    }


class MappingParameters(AWSProperty):
    """
    `MappingParameters <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-applicationreferencedatasource-mappingparameters.html>`__
    """

    props: PropsDictType = {
        "CSVMappingParameters": (CSVMappingParameters, False),
        "JSONMappingParameters": (JSONMappingParameters, False),
    }


class RecordFormat(AWSProperty):
    """
    `RecordFormat <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-applicationreferencedatasource-recordformat.html>`__
    """

    props: PropsDictType = {
        "MappingParameters": (MappingParameters, False),
        "RecordFormatType": (str, True),
    }


class InputSchema(AWSProperty):
    """
    `InputSchema <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-inputschema.html>`__
    """

    props: PropsDictType = {
        "RecordColumns": ([RecordColumn], True),
        "RecordEncoding": (str, False),
        "RecordFormat": (RecordFormat, True),
    }


class KinesisFirehoseInput(AWSProperty):
    """
    `KinesisFirehoseInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-kinesisfirehoseinput.html>`__
    """

    props: PropsDictType = {
        "ResourceARN": (str, True),
    }


class KinesisStreamsInput(AWSProperty):
    """
    `KinesisStreamsInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-kinesisstreamsinput.html>`__
    """

    props: PropsDictType = {
        "ResourceARN": (str, True),
    }


class Input(AWSProperty):
    """
    `Input <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-input.html>`__
    """

    props: PropsDictType = {
        "InputParallelism": (InputParallelism, False),
        "InputProcessingConfiguration": (InputProcessingConfiguration, False),
        "InputSchema": (InputSchema, True),
        "KinesisFirehoseInput": (KinesisFirehoseInput, False),
        "KinesisStreamsInput": (KinesisStreamsInput, False),
        "NamePrefix": (str, True),
    }


class SqlApplicationConfiguration(AWSProperty):
    """
    `SqlApplicationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-sqlapplicationconfiguration.html>`__
    """

    props: PropsDictType = {
        "Inputs": ([Input], False),
    }


class VpcConfiguration(AWSProperty):
    """
    `VpcConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-vpcconfiguration.html>`__
    """

    props: PropsDictType = {
        "SecurityGroupIds": ([str], True),
        "SubnetIds": ([str], True),
    }


class GlueDataCatalogConfiguration(AWSProperty):
    """
    `GlueDataCatalogConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-gluedatacatalogconfiguration.html>`__
    """

    props: PropsDictType = {
        "DatabaseARN": (str, False),
    }


class CatalogConfiguration(AWSProperty):
    """
    `CatalogConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-catalogconfiguration.html>`__
    """

    props: PropsDictType = {
        "GlueDataCatalogConfiguration": (GlueDataCatalogConfiguration, False),
    }


class MavenReference(AWSProperty):
    """
    `MavenReference <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-mavenreference.html>`__
    """

    props: PropsDictType = {
        "ArtifactId": (str, True),
        "GroupId": (str, True),
        "Version": (str, True),
    }


class CustomArtifactConfiguration(AWSProperty):
    """
    `CustomArtifactConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-customartifactconfiguration.html>`__
    """

    props: PropsDictType = {
        "ArtifactType": (str, True),
        "MavenReference": (MavenReference, False),
        "S3ContentLocation": (S3ContentLocation, False),
    }


class S3ContentBaseLocation(AWSProperty):
    """
    `S3ContentBaseLocation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-s3contentbaselocation.html>`__
    """

    props: PropsDictType = {
        "BasePath": (str, False),
        "BucketARN": (str, True),
    }


class DeployAsApplicationConfiguration(AWSProperty):
    """
    `DeployAsApplicationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-deployasapplicationconfiguration.html>`__
    """

    props: PropsDictType = {
        "S3ContentLocation": (S3ContentBaseLocation, True),
    }


class ZeppelinMonitoringConfiguration(AWSProperty):
    """
    `ZeppelinMonitoringConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-zeppelinmonitoringconfiguration.html>`__
    """

    props: PropsDictType = {
        "LogLevel": (str, False),
    }


class ZeppelinApplicationConfiguration(AWSProperty):
    """
    `ZeppelinApplicationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-zeppelinapplicationconfiguration.html>`__
    """

    props: PropsDictType = {
        "CatalogConfiguration": (CatalogConfiguration, False),
        "CustomArtifactsConfiguration": ([CustomArtifactConfiguration], False),
        "DeployAsApplicationConfiguration": (DeployAsApplicationConfiguration, False),
        "MonitoringConfiguration": (ZeppelinMonitoringConfiguration, False),
    }


class ApplicationConfiguration(AWSProperty):
    """
    `ApplicationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-applicationconfiguration.html>`__
    """

    props: PropsDictType = {
        "ApplicationCodeConfiguration": (ApplicationCodeConfiguration, False),
        "ApplicationSnapshotConfiguration": (ApplicationSnapshotConfiguration, False),
        "ApplicationSystemRollbackConfiguration": (
            ApplicationSystemRollbackConfiguration,
            False,
        ),
        "EnvironmentProperties": (EnvironmentProperties, False),
        "FlinkApplicationConfiguration": (FlinkApplicationConfiguration, False),
        "SqlApplicationConfiguration": (SqlApplicationConfiguration, False),
        "VpcConfigurations": ([VpcConfiguration], False),
        "ZeppelinApplicationConfiguration": (ZeppelinApplicationConfiguration, False),
    }


class ApplicationMaintenanceConfiguration(AWSProperty):
    """
    `ApplicationMaintenanceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-applicationmaintenanceconfiguration.html>`__
    """

    props: PropsDictType = {
        "ApplicationMaintenanceWindowStartTime": (str, True),
    }


class ApplicationRestoreConfiguration(AWSProperty):
    """
    `ApplicationRestoreConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-applicationrestoreconfiguration.html>`__
    """

    props: PropsDictType = {
        "ApplicationRestoreType": (str, True),
        "SnapshotName": (str, False),
    }


class FlinkRunConfiguration(AWSProperty):
    """
    `FlinkRunConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-flinkrunconfiguration.html>`__
    """

    props: PropsDictType = {
        "AllowNonRestoredState": (boolean, False),
    }


class RunConfiguration(AWSProperty):
    """
    `RunConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-application-runconfiguration.html>`__
    """

    props: PropsDictType = {
        "ApplicationRestoreConfiguration": (ApplicationRestoreConfiguration, False),
        "FlinkRunConfiguration": (FlinkRunConfiguration, False),
    }


class Application(AWSObject):
    """
    `Application <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-kinesisanalyticsv2-application.html>`__
    """

    resource_type = "AWS::KinesisAnalyticsV2::Application"

    props: PropsDictType = {
        "ApplicationConfiguration": (ApplicationConfiguration, False),
        "ApplicationDescription": (str, False),
        "ApplicationMaintenanceConfiguration": (
            ApplicationMaintenanceConfiguration,
            False,
        ),
        "ApplicationMode": (str, False),
        "ApplicationName": (str, False),
        "RunConfiguration": (RunConfiguration, False),
        "RuntimeEnvironment": (validate_runtime_environment, True),
        "ServiceExecutionRole": (str, True),
        "Tags": (Tags, False),
    }


class CloudWatchLoggingOption(AWSProperty):
    """
    `CloudWatchLoggingOption <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-applicationcloudwatchloggingoption-cloudwatchloggingoption.html>`__
    """

    props: PropsDictType = {
        "LogStreamARN": (str, True),
    }


class ApplicationCloudWatchLoggingOption(AWSObject):
    """
    `ApplicationCloudWatchLoggingOption <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-kinesisanalyticsv2-applicationcloudwatchloggingoption.html>`__
    """

    resource_type = "AWS::KinesisAnalyticsV2::ApplicationCloudWatchLoggingOption"

    props: PropsDictType = {
        "ApplicationName": (str, True),
        "CloudWatchLoggingOption": (CloudWatchLoggingOption, True),
    }


class DestinationSchema(AWSProperty):
    """
    `DestinationSchema <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-applicationoutput-destinationschema.html>`__
    """

    props: PropsDictType = {
        "RecordFormatType": (str, False),
    }


class KinesisFirehoseOutput(AWSProperty):
    """
    `KinesisFirehoseOutput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-applicationoutput-kinesisfirehoseoutput.html>`__
    """

    props: PropsDictType = {
        "ResourceARN": (str, True),
    }


class KinesisStreamsOutput(AWSProperty):
    """
    `KinesisStreamsOutput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-applicationoutput-kinesisstreamsoutput.html>`__
    """

    props: PropsDictType = {
        "ResourceARN": (str, True),
    }


class LambdaOutput(AWSProperty):
    """
    `LambdaOutput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-applicationoutput-lambdaoutput.html>`__
    """

    props: PropsDictType = {
        "ResourceARN": (str, True),
    }


class Output(AWSProperty):
    """
    `Output <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-applicationoutput-output.html>`__
    """

    props: PropsDictType = {
        "DestinationSchema": (DestinationSchema, True),
        "KinesisFirehoseOutput": (KinesisFirehoseOutput, False),
        "KinesisStreamsOutput": (KinesisStreamsOutput, False),
        "LambdaOutput": (LambdaOutput, False),
        "Name": (str, False),
    }


class ApplicationOutput(AWSObject):
    """
    `ApplicationOutput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-kinesisanalyticsv2-applicationoutput.html>`__
    """

    resource_type = "AWS::KinesisAnalyticsV2::ApplicationOutput"

    props: PropsDictType = {
        "ApplicationName": (str, True),
        "Output": (Output, True),
    }


class ReferenceSchema(AWSProperty):
    """
    `ReferenceSchema <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-applicationreferencedatasource-referenceschema.html>`__
    """

    props: PropsDictType = {
        "RecordColumns": ([RecordColumn], True),
        "RecordEncoding": (str, False),
        "RecordFormat": (RecordFormat, True),
    }


class S3ReferenceDataSource(AWSProperty):
    """
    `S3ReferenceDataSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-applicationreferencedatasource-s3referencedatasource.html>`__
    """

    props: PropsDictType = {
        "BucketARN": (str, True),
        "FileKey": (str, True),
    }


class ReferenceDataSource(AWSProperty):
    """
    `ReferenceDataSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-kinesisanalyticsv2-applicationreferencedatasource-referencedatasource.html>`__
    """

    props: PropsDictType = {
        "ReferenceSchema": (ReferenceSchema, True),
        "S3ReferenceDataSource": (S3ReferenceDataSource, False),
        "TableName": (str, False),
    }


class ApplicationReferenceDataSource(AWSObject):
    """
    `ApplicationReferenceDataSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-kinesisanalyticsv2-applicationreferencedatasource.html>`__
    """

    resource_type = "AWS::KinesisAnalyticsV2::ApplicationReferenceDataSource"

    props: PropsDictType = {
        "ApplicationName": (str, True),
        "ReferenceDataSource": (ReferenceDataSource, True),
    }
