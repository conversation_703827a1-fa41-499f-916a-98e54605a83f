# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType
from .validators import double, integer


class BridgeNetworkOutput(AWSProperty):
    """
    `BridgeNetworkOutput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-bridgeoutput-bridgenetworkoutput.html>`__
    """

    props: PropsDictType = {
        "IpAddress": (str, True),
        "NetworkName": (str, True),
        "Port": (integer, True),
        "Protocol": (str, True),
        "Ttl": (integer, True),
    }


class BridgeOutputProperty(AWSProperty):
    """
    `BridgeOutputProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-bridge-bridgeoutput.html>`__
    """

    props: PropsDictType = {
        "NetworkOutput": (BridgeNetworkOutput, False),
    }


class VpcInterfaceAttachment(AWSProperty):
    """
    `VpcInterfaceAttachment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flowsource-vpcinterfaceattachment.html>`__
    """

    props: PropsDictType = {
        "VpcInterfaceName": (str, False),
    }


class BridgeFlowSource(AWSProperty):
    """
    `BridgeFlowSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-bridgesource-bridgeflowsource.html>`__
    """

    props: PropsDictType = {
        "FlowArn": (str, True),
        "FlowVpcInterfaceAttachment": (VpcInterfaceAttachment, False),
    }


class MulticastSourceSettings(AWSProperty):
    """
    `MulticastSourceSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-bridgesource-multicastsourcesettings.html>`__
    """

    props: PropsDictType = {
        "MulticastSourceIp": (str, False),
    }


class BridgeNetworkSource(AWSProperty):
    """
    `BridgeNetworkSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-bridgesource-bridgenetworksource.html>`__
    """

    props: PropsDictType = {
        "MulticastIp": (str, True),
        "MulticastSourceSettings": (MulticastSourceSettings, False),
        "NetworkName": (str, True),
        "Port": (integer, True),
        "Protocol": (str, True),
    }


class BridgeSourceProperty(AWSProperty):
    """
    `BridgeSourceProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-bridge-bridgesource.html>`__
    """

    props: PropsDictType = {
        "FlowSource": (BridgeFlowSource, False),
        "NetworkSource": (BridgeNetworkSource, False),
    }


class EgressGatewayBridge(AWSProperty):
    """
    `EgressGatewayBridge <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-bridge-egressgatewaybridge.html>`__
    """

    props: PropsDictType = {
        "MaxBitrate": (integer, True),
    }


class SourcePriority(AWSProperty):
    """
    `SourcePriority <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-sourcepriority.html>`__
    """

    props: PropsDictType = {
        "PrimarySource": (str, True),
    }


class FailoverConfig(AWSProperty):
    """
    `FailoverConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-failoverconfig.html>`__
    """

    props: PropsDictType = {
        "FailoverMode": (str, False),
        "RecoveryWindow": (integer, False),
        "SourcePriority": (SourcePriority, False),
        "State": (str, False),
    }


class IngressGatewayBridge(AWSProperty):
    """
    `IngressGatewayBridge <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-bridge-ingressgatewaybridge.html>`__
    """

    props: PropsDictType = {
        "MaxBitrate": (integer, True),
        "MaxOutputs": (integer, True),
    }


class Bridge(AWSObject):
    """
    `Bridge <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-mediaconnect-bridge.html>`__
    """

    resource_type = "AWS::MediaConnect::Bridge"

    props: PropsDictType = {
        "EgressGatewayBridge": (EgressGatewayBridge, False),
        "IngressGatewayBridge": (IngressGatewayBridge, False),
        "Name": (str, True),
        "Outputs": ([BridgeOutputProperty], False),
        "PlacementArn": (str, True),
        "SourceFailoverConfig": (FailoverConfig, False),
        "Sources": ([BridgeSourceProperty], True),
    }


class BridgeOutput(AWSObject):
    """
    `BridgeOutput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-mediaconnect-bridgeoutput.html>`__
    """

    resource_type = "AWS::MediaConnect::BridgeOutput"

    props: PropsDictType = {
        "BridgeArn": (str, True),
        "Name": (str, True),
        "NetworkOutput": (BridgeNetworkOutput, True),
    }


class BridgeSource(AWSObject):
    """
    `BridgeSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-mediaconnect-bridgesource.html>`__
    """

    resource_type = "AWS::MediaConnect::BridgeSource"

    props: PropsDictType = {
        "BridgeArn": (str, True),
        "FlowSource": (BridgeFlowSource, False),
        "Name": (str, True),
        "NetworkSource": (BridgeNetworkSource, False),
    }


class Maintenance(AWSProperty):
    """
    `Maintenance <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-maintenance.html>`__
    """

    props: PropsDictType = {
        "MaintenanceDay": (str, True),
        "MaintenanceStartHour": (str, True),
    }


class Fmtp(AWSProperty):
    """
    `Fmtp <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-fmtp.html>`__
    """

    props: PropsDictType = {
        "ChannelOrder": (str, False),
        "Colorimetry": (str, False),
        "ExactFramerate": (str, False),
        "Par": (str, False),
        "Range": (str, False),
        "ScanMode": (str, False),
        "Tcs": (str, False),
    }


class MediaStreamAttributes(AWSProperty):
    """
    `MediaStreamAttributes <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-mediastreamattributes.html>`__
    """

    props: PropsDictType = {
        "Fmtp": (Fmtp, False),
        "Lang": (str, False),
    }


class MediaStream(AWSProperty):
    """
    `MediaStream <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-mediastream.html>`__
    """

    props: PropsDictType = {
        "Attributes": (MediaStreamAttributes, False),
        "ClockRate": (integer, False),
        "Description": (str, False),
        "Fmt": (integer, False),
        "MediaStreamId": (integer, True),
        "MediaStreamName": (str, True),
        "MediaStreamType": (str, True),
        "VideoFormat": (str, False),
    }


class NdiDiscoveryServerConfig(AWSProperty):
    """
    `NdiDiscoveryServerConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-ndidiscoveryserverconfig.html>`__
    """

    props: PropsDictType = {
        "DiscoveryServerAddress": (str, True),
        "DiscoveryServerPort": (integer, False),
        "VpcInterfaceAdapter": (str, True),
    }


class NdiConfig(AWSProperty):
    """
    `NdiConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-ndiconfig.html>`__
    """

    props: PropsDictType = {
        "MachineName": (str, False),
        "NdiDiscoveryServers": ([NdiDiscoveryServerConfig], False),
        "NdiState": (str, False),
    }


class Encryption(AWSProperty):
    """
    `Encryption <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flowsource-encryption.html>`__
    """

    props: PropsDictType = {
        "Algorithm": (str, False),
        "ConstantInitializationVector": (str, False),
        "DeviceId": (str, False),
        "KeyType": (str, False),
        "Region": (str, False),
        "ResourceId": (str, False),
        "RoleArn": (str, True),
        "SecretArn": (str, False),
        "Url": (str, False),
    }


class GatewayBridgeSource(AWSProperty):
    """
    `GatewayBridgeSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flowsource-gatewaybridgesource.html>`__
    """

    props: PropsDictType = {
        "BridgeArn": (str, True),
        "VpcInterfaceAttachment": (VpcInterfaceAttachment, False),
    }


class Interface(AWSProperty):
    """
    `Interface <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flowoutput-interface.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
    }


class InputConfiguration(AWSProperty):
    """
    `InputConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-inputconfiguration.html>`__
    """

    props: PropsDictType = {
        "InputPort": (integer, True),
        "Interface": (Interface, True),
    }


class MediaStreamSourceConfiguration(AWSProperty):
    """
    `MediaStreamSourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-mediastreamsourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "EncodingName": (str, True),
        "InputConfigurations": ([InputConfiguration], False),
        "MediaStreamName": (str, True),
    }


class Source(AWSProperty):
    """
    `Source <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-source.html>`__
    """

    props: PropsDictType = {
        "Decryption": (Encryption, False),
        "Description": (str, False),
        "EntitlementArn": (str, False),
        "GatewayBridgeSource": (GatewayBridgeSource, False),
        "IngestIp": (str, False),
        "IngestPort": (integer, False),
        "MaxBitrate": (integer, False),
        "MaxLatency": (integer, False),
        "MaxSyncBuffer": (integer, False),
        "MediaStreamSourceConfigurations": ([MediaStreamSourceConfiguration], False),
        "MinLatency": (integer, False),
        "Name": (str, False),
        "Protocol": (str, False),
        "SenderControlPort": (integer, False),
        "SenderIpAddress": (str, False),
        "SourceArn": (str, False),
        "SourceIngestPort": (str, False),
        "SourceListenerAddress": (str, False),
        "SourceListenerPort": (integer, False),
        "StreamId": (str, False),
        "VpcInterfaceName": (str, False),
        "WhitelistCidr": (str, False),
    }


class SilentAudio(AWSProperty):
    """
    `SilentAudio <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-silentaudio.html>`__
    """

    props: PropsDictType = {
        "State": (str, False),
        "ThresholdSeconds": (integer, False),
    }


class AudioMonitoringSetting(AWSProperty):
    """
    `AudioMonitoringSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-audiomonitoringsetting.html>`__
    """

    props: PropsDictType = {
        "SilentAudio": (SilentAudio, False),
    }


class BlackFrames(AWSProperty):
    """
    `BlackFrames <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-blackframes.html>`__
    """

    props: PropsDictType = {
        "State": (str, False),
        "ThresholdSeconds": (integer, False),
    }


class FrozenFrames(AWSProperty):
    """
    `FrozenFrames <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-frozenframes.html>`__
    """

    props: PropsDictType = {
        "State": (str, False),
        "ThresholdSeconds": (integer, False),
    }


class VideoMonitoringSetting(AWSProperty):
    """
    `VideoMonitoringSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-videomonitoringsetting.html>`__
    """

    props: PropsDictType = {
        "BlackFrames": (BlackFrames, False),
        "FrozenFrames": (FrozenFrames, False),
    }


class SourceMonitoringConfig(AWSProperty):
    """
    `SourceMonitoringConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-sourcemonitoringconfig.html>`__
    """

    props: PropsDictType = {
        "AudioMonitoringSettings": ([AudioMonitoringSetting], False),
        "ContentQualityAnalysisState": (str, False),
        "ThumbnailState": (str, False),
        "VideoMonitoringSettings": ([VideoMonitoringSetting], False),
    }


class VpcInterface(AWSProperty):
    """
    `VpcInterface <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flow-vpcinterface.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "NetworkInterfaceIds": ([str], False),
        "NetworkInterfaceType": (str, False),
        "RoleArn": (str, True),
        "SecurityGroupIds": ([str], True),
        "SubnetId": (str, True),
    }


class Flow(AWSObject):
    """
    `Flow <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-mediaconnect-flow.html>`__
    """

    resource_type = "AWS::MediaConnect::Flow"

    props: PropsDictType = {
        "AvailabilityZone": (str, False),
        "FlowSize": (str, False),
        "Maintenance": (Maintenance, False),
        "MediaStreams": ([MediaStream], False),
        "Name": (str, True),
        "NdiConfig": (NdiConfig, False),
        "Source": (Source, True),
        "SourceFailoverConfig": (FailoverConfig, False),
        "SourceMonitoringConfig": (SourceMonitoringConfig, False),
        "VpcInterfaces": ([VpcInterface], False),
    }


class FlowEntitlement(AWSObject):
    """
    `FlowEntitlement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-mediaconnect-flowentitlement.html>`__
    """

    resource_type = "AWS::MediaConnect::FlowEntitlement"

    props: PropsDictType = {
        "DataTransferSubscriberFeePercent": (integer, False),
        "Description": (str, True),
        "Encryption": (Encryption, False),
        "EntitlementStatus": (str, False),
        "FlowArn": (str, True),
        "Name": (str, True),
        "Subscribers": ([str], True),
    }


class FlowOutputEncryption(AWSProperty):
    """
    `FlowOutputEncryption <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flowoutput-encryption.html>`__
    """

    props: PropsDictType = {
        "Algorithm": (str, False),
        "KeyType": (str, False),
        "RoleArn": (str, True),
        "SecretArn": (str, True),
    }


class DestinationConfiguration(AWSProperty):
    """
    `DestinationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flowoutput-destinationconfiguration.html>`__
    """

    props: PropsDictType = {
        "DestinationIp": (str, True),
        "DestinationPort": (integer, True),
        "Interface": (Interface, True),
    }


class EncodingParameters(AWSProperty):
    """
    `EncodingParameters <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flowoutput-encodingparameters.html>`__
    """

    props: PropsDictType = {
        "CompressionFactor": (double, True),
        "EncoderProfile": (str, False),
    }


class MediaStreamOutputConfiguration(AWSProperty):
    """
    `MediaStreamOutputConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-flowoutput-mediastreamoutputconfiguration.html>`__
    """

    props: PropsDictType = {
        "DestinationConfigurations": ([DestinationConfiguration], False),
        "EncodingName": (str, True),
        "EncodingParameters": (EncodingParameters, False),
        "MediaStreamName": (str, True),
    }


class FlowOutput(AWSObject):
    """
    `FlowOutput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-mediaconnect-flowoutput.html>`__
    """

    resource_type = "AWS::MediaConnect::FlowOutput"

    props: PropsDictType = {
        "CidrAllowList": ([str], False),
        "Description": (str, False),
        "Destination": (str, False),
        "Encryption": (FlowOutputEncryption, False),
        "FlowArn": (str, True),
        "MaxLatency": (integer, False),
        "MediaStreamOutputConfigurations": ([MediaStreamOutputConfiguration], False),
        "MinLatency": (integer, False),
        "Name": (str, False),
        "NdiProgramName": (str, False),
        "NdiSpeedHqQuality": (integer, False),
        "OutputStatus": (str, False),
        "Port": (integer, False),
        "Protocol": (str, True),
        "RemoteId": (str, False),
        "SmoothingLatency": (integer, False),
        "StreamId": (str, False),
        "VpcInterfaceAttachment": (VpcInterfaceAttachment, False),
    }


class FlowSource(AWSObject):
    """
    `FlowSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-mediaconnect-flowsource.html>`__
    """

    resource_type = "AWS::MediaConnect::FlowSource"

    props: PropsDictType = {
        "Decryption": (Encryption, False),
        "Description": (str, True),
        "EntitlementArn": (str, False),
        "FlowArn": (str, False),
        "GatewayBridgeSource": (GatewayBridgeSource, False),
        "IngestPort": (integer, False),
        "MaxBitrate": (integer, False),
        "MaxLatency": (integer, False),
        "MinLatency": (integer, False),
        "Name": (str, True),
        "Protocol": (str, False),
        "SenderControlPort": (integer, False),
        "SenderIpAddress": (str, False),
        "SourceListenerAddress": (str, False),
        "SourceListenerPort": (integer, False),
        "StreamId": (str, False),
        "VpcInterfaceName": (str, False),
        "WhitelistCidr": (str, False),
    }


class FlowVpcInterface(AWSObject):
    """
    `FlowVpcInterface <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-mediaconnect-flowvpcinterface.html>`__
    """

    resource_type = "AWS::MediaConnect::FlowVpcInterface"

    props: PropsDictType = {
        "FlowArn": (str, True),
        "Name": (str, True),
        "RoleArn": (str, True),
        "SecurityGroupIds": ([str], True),
        "SubnetId": (str, True),
    }


class GatewayNetwork(AWSProperty):
    """
    `GatewayNetwork <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediaconnect-gateway-gatewaynetwork.html>`__
    """

    props: PropsDictType = {
        "CidrBlock": (str, True),
        "Name": (str, True),
    }


class Gateway(AWSObject):
    """
    `Gateway <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-mediaconnect-gateway.html>`__
    """

    resource_type = "AWS::MediaConnect::Gateway"

    props: PropsDictType = {
        "EgressCidrBlocks": ([str], True),
        "Name": (str, True),
        "Networks": ([GatewayNetwork], True),
    }
