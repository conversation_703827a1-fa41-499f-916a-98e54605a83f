# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, integer
from .validators.cassandra import (
    validate_billingmode_mode,
    validate_clusteringkeycolumn_orderby,
)


class ReplicationSpecification(AWSProperty):
    """
    `ReplicationSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cassandra-keyspace-replicationspecification.html>`__
    """

    props: PropsDictType = {
        "RegionList": ([str], False),
        "ReplicationStrategy": (str, False),
    }


class Keyspace(AWSObject):
    """
    `Keyspace <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cassandra-keyspace.html>`__
    """

    resource_type = "AWS::Cassandra::Keyspace"

    props: PropsDictType = {
        "ClientSideTimestampsEnabled": (boolean, False),
        "KeyspaceName": (str, False),
        "ReplicationSpecification": (ReplicationSpecification, False),
        "Tags": (Tags, False),
    }


class TargetTrackingScalingPolicyConfiguration(AWSProperty):
    """
    `TargetTrackingScalingPolicyConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cassandra-table-targettrackingscalingpolicyconfiguration.html>`__
    """

    props: PropsDictType = {
        "DisableScaleIn": (boolean, False),
        "ScaleInCooldown": (integer, False),
        "ScaleOutCooldown": (integer, False),
        "TargetValue": (integer, True),
    }


class ScalingPolicy(AWSProperty):
    """
    `ScalingPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cassandra-table-scalingpolicy.html>`__
    """

    props: PropsDictType = {
        "TargetTrackingScalingPolicyConfiguration": (
            TargetTrackingScalingPolicyConfiguration,
            False,
        ),
    }


class AutoScalingSetting(AWSProperty):
    """
    `AutoScalingSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cassandra-table-autoscalingsetting.html>`__
    """

    props: PropsDictType = {
        "AutoScalingDisabled": (boolean, False),
        "MaximumUnits": (integer, False),
        "MinimumUnits": (integer, False),
        "ScalingPolicy": (ScalingPolicy, False),
    }


class AutoScalingSpecification(AWSProperty):
    """
    `AutoScalingSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cassandra-table-autoscalingspecification.html>`__
    """

    props: PropsDictType = {
        "ReadCapacityAutoScaling": (AutoScalingSetting, False),
        "WriteCapacityAutoScaling": (AutoScalingSetting, False),
    }


class ProvisionedThroughput(AWSProperty):
    """
    `ProvisionedThroughput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cassandra-table-provisionedthroughput.html>`__
    """

    props: PropsDictType = {
        "ReadCapacityUnits": (integer, True),
        "WriteCapacityUnits": (integer, True),
    }


class BillingMode(AWSProperty):
    """
    `BillingMode <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cassandra-table-billingmode.html>`__
    """

    props: PropsDictType = {
        "Mode": (validate_billingmode_mode, True),
        "ProvisionedThroughput": (ProvisionedThroughput, False),
    }


class Column(AWSProperty):
    """
    `Column <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cassandra-table-column.html>`__
    """

    props: PropsDictType = {
        "ColumnName": (str, True),
        "ColumnType": (str, True),
    }


class ClusteringKeyColumn(AWSProperty):
    """
    `ClusteringKeyColumn <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cassandra-table-clusteringkeycolumn.html>`__
    """

    props: PropsDictType = {
        "Column": (Column, True),
        "OrderBy": (validate_clusteringkeycolumn_orderby, False),
    }


class EncryptionSpecification(AWSProperty):
    """
    `EncryptionSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cassandra-table-encryptionspecification.html>`__
    """

    props: PropsDictType = {
        "EncryptionType": (str, True),
        "KmsKeyIdentifier": (str, False),
    }


class ReplicaSpecification(AWSProperty):
    """
    `ReplicaSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cassandra-table-replicaspecification.html>`__
    """

    props: PropsDictType = {
        "ReadCapacityAutoScaling": (AutoScalingSetting, False),
        "ReadCapacityUnits": (integer, False),
        "Region": (str, True),
    }


class Table(AWSObject):
    """
    `Table <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cassandra-table.html>`__
    """

    resource_type = "AWS::Cassandra::Table"

    props: PropsDictType = {
        "AutoScalingSpecifications": (AutoScalingSpecification, False),
        "BillingMode": (BillingMode, False),
        "ClientSideTimestampsEnabled": (boolean, False),
        "ClusteringKeyColumns": ([ClusteringKeyColumn], False),
        "DefaultTimeToLive": (integer, False),
        "EncryptionSpecification": (EncryptionSpecification, False),
        "KeyspaceName": (str, True),
        "PartitionKeyColumns": ([Column], True),
        "PointInTimeRecoveryEnabled": (boolean, False),
        "RegularColumns": ([Column], False),
        "ReplicaSpecifications": ([ReplicaSpecification], False),
        "TableName": (str, False),
        "Tags": (Tags, False),
    }


class Field(AWSProperty):
    """
    `Field <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cassandra-type-field.html>`__
    """

    props: PropsDictType = {
        "FieldName": (str, True),
        "FieldType": (str, True),
    }


class Type(AWSObject):
    """
    `Type <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cassandra-type.html>`__
    """

    resource_type = "AWS::Cassandra::Type"

    props: PropsDictType = {
        "Fields": ([Field], True),
        "KeyspaceName": (str, True),
        "TypeName": (str, True),
    }
