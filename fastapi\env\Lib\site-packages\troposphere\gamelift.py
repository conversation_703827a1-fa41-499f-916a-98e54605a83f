# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer


class RoutingStrategy(AWSProperty):
    """
    `RoutingStrategy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-alias-routingstrategy.html>`__
    """

    props: PropsDictType = {
        "FleetId": (str, False),
        "Message": (str, False),
        "Type": (str, True),
    }


class Alias(AWSObject):
    """
    `Alias <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-gamelift-alias.html>`__
    """

    resource_type = "AWS::GameLift::Alias"

    props: PropsDictType = {
        "Description": (str, False),
        "Name": (str, True),
        "RoutingStrategy": (RoutingStrategy, True),
        "Tags": (Tags, False),
    }


class StorageLocation(AWSProperty):
    """
    `StorageLocation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-build-storagelocation.html>`__
    """

    props: PropsDictType = {
        "Bucket": (str, True),
        "Key": (str, True),
        "ObjectVersion": (str, False),
        "RoleArn": (str, True),
    }


class Build(AWSObject):
    """
    `Build <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-gamelift-build.html>`__
    """

    resource_type = "AWS::GameLift::Build"

    props: PropsDictType = {
        "Name": (str, False),
        "OperatingSystem": (str, False),
        "ServerSdkVersion": (str, False),
        "StorageLocation": (StorageLocation, False),
        "Tags": (Tags, False),
        "Version": (str, False),
    }


class ConnectionPortRange(AWSProperty):
    """
    `ConnectionPortRange <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-containerfleet-connectionportrange.html>`__
    """

    props: PropsDictType = {
        "FromPort": (integer, True),
        "ToPort": (integer, True),
    }


class DeploymentConfiguration(AWSProperty):
    """
    `DeploymentConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-containerfleet-deploymentconfiguration.html>`__
    """

    props: PropsDictType = {
        "ImpairmentStrategy": (str, False),
        "MinimumHealthyPercentage": (integer, False),
        "ProtectionStrategy": (str, False),
    }


class GameSessionCreationLimitPolicy(AWSProperty):
    """
    `GameSessionCreationLimitPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-containerfleet-gamesessioncreationlimitpolicy.html>`__
    """

    props: PropsDictType = {
        "NewGameSessionsPerCreator": (integer, False),
        "PolicyPeriodInMinutes": (integer, False),
    }


class IpPermission(AWSProperty):
    """
    `IpPermission <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-fleet-ippermission.html>`__
    """

    props: PropsDictType = {
        "FromPort": (integer, True),
        "IpRange": (str, True),
        "Protocol": (str, True),
        "ToPort": (integer, True),
    }


class LocationCapacity(AWSProperty):
    """
    `LocationCapacity <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-fleet-locationcapacity.html>`__
    """

    props: PropsDictType = {
        "DesiredEC2Instances": (integer, True),
        "MaxSize": (integer, True),
        "MinSize": (integer, True),
    }


class LocationConfiguration(AWSProperty):
    """
    `LocationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-containerfleet-locationconfiguration.html>`__
    """

    props: PropsDictType = {
        "Location": (str, True),
        "LocationCapacity": (LocationCapacity, False),
        "StoppedActions": ([str], False),
    }


class LogConfiguration(AWSProperty):
    """
    `LogConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-containerfleet-logconfiguration.html>`__
    """

    props: PropsDictType = {
        "LogDestination": (str, False),
        "S3BucketName": (str, False),
    }


class TargetConfiguration(AWSProperty):
    """
    `TargetConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-fleet-targetconfiguration.html>`__
    """

    props: PropsDictType = {
        "TargetValue": (double, True),
    }


class ScalingPolicy(AWSProperty):
    """
    `ScalingPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-fleet-scalingpolicy.html>`__
    """

    props: PropsDictType = {
        "ComparisonOperator": (str, False),
        "EvaluationPeriods": (integer, False),
        "Location": (str, False),
        "MetricName": (str, True),
        "Name": (str, True),
        "PolicyType": (str, False),
        "ScalingAdjustment": (integer, False),
        "ScalingAdjustmentType": (str, False),
        "Status": (str, False),
        "TargetConfiguration": (TargetConfiguration, False),
        "Threshold": (double, False),
        "UpdateStatus": (str, False),
    }


class ContainerFleet(AWSObject):
    """
    `ContainerFleet <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-gamelift-containerfleet.html>`__
    """

    resource_type = "AWS::GameLift::ContainerFleet"

    props: PropsDictType = {
        "BillingType": (str, False),
        "DeploymentConfiguration": (DeploymentConfiguration, False),
        "Description": (str, False),
        "FleetRoleArn": (str, True),
        "GameServerContainerGroupDefinitionName": (str, False),
        "GameServerContainerGroupsPerInstance": (integer, False),
        "GameSessionCreationLimitPolicy": (GameSessionCreationLimitPolicy, False),
        "InstanceConnectionPortRange": (ConnectionPortRange, False),
        "InstanceInboundPermissions": ([IpPermission], False),
        "InstanceType": (str, False),
        "Locations": ([LocationConfiguration], False),
        "LogConfiguration": (LogConfiguration, False),
        "MetricGroups": ([str], False),
        "NewGameSessionProtectionPolicy": (str, False),
        "PerInstanceContainerGroupDefinitionName": (str, False),
        "ScalingPolicies": ([ScalingPolicy], False),
        "Tags": (Tags, False),
    }


class ContainerDependency(AWSProperty):
    """
    `ContainerDependency <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-containergroupdefinition-containerdependency.html>`__
    """

    props: PropsDictType = {
        "Condition": (str, True),
        "ContainerName": (str, True),
    }


class ContainerEnvironment(AWSProperty):
    """
    `ContainerEnvironment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-containergroupdefinition-containerenvironment.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "Value": (str, True),
    }


class ContainerMountPoint(AWSProperty):
    """
    `ContainerMountPoint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-containergroupdefinition-containermountpoint.html>`__
    """

    props: PropsDictType = {
        "AccessLevel": (str, False),
        "ContainerPath": (str, False),
        "InstancePath": (str, True),
    }


class ContainerPortRange(AWSProperty):
    """
    `ContainerPortRange <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-containergroupdefinition-containerportrange.html>`__
    """

    props: PropsDictType = {
        "FromPort": (integer, True),
        "Protocol": (str, True),
        "ToPort": (integer, True),
    }


class PortConfiguration(AWSProperty):
    """
    `PortConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-containergroupdefinition-portconfiguration.html>`__
    """

    props: PropsDictType = {
        "ContainerPortRanges": ([ContainerPortRange], True),
    }


class GameServerContainerDefinition(AWSProperty):
    """
    `GameServerContainerDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-containergroupdefinition-gameservercontainerdefinition.html>`__
    """

    props: PropsDictType = {
        "ContainerName": (str, True),
        "DependsOn": ([ContainerDependency], False),
        "EnvironmentOverride": ([ContainerEnvironment], False),
        "ImageUri": (str, True),
        "MountPoints": ([ContainerMountPoint], False),
        "PortConfiguration": (PortConfiguration, False),
        "ResolvedImageDigest": (str, False),
        "ServerSdkVersion": (str, True),
    }


class ContainerHealthCheck(AWSProperty):
    """
    `ContainerHealthCheck <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-containergroupdefinition-containerhealthcheck.html>`__
    """

    props: PropsDictType = {
        "Command": ([str], True),
        "Interval": (integer, False),
        "Retries": (integer, False),
        "StartPeriod": (integer, False),
        "Timeout": (integer, False),
    }


class SupportContainerDefinition(AWSProperty):
    """
    `SupportContainerDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-containergroupdefinition-supportcontainerdefinition.html>`__
    """

    props: PropsDictType = {
        "ContainerName": (str, True),
        "DependsOn": ([ContainerDependency], False),
        "EnvironmentOverride": ([ContainerEnvironment], False),
        "Essential": (boolean, False),
        "HealthCheck": (ContainerHealthCheck, False),
        "ImageUri": (str, True),
        "MemoryHardLimitMebibytes": (integer, False),
        "MountPoints": ([ContainerMountPoint], False),
        "PortConfiguration": (PortConfiguration, False),
        "ResolvedImageDigest": (str, False),
        "Vcpu": (double, False),
    }


class ContainerGroupDefinition(AWSObject):
    """
    `ContainerGroupDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-gamelift-containergroupdefinition.html>`__
    """

    resource_type = "AWS::GameLift::ContainerGroupDefinition"

    props: PropsDictType = {
        "ContainerGroupType": (str, False),
        "GameServerContainerDefinition": (GameServerContainerDefinition, False),
        "Name": (str, True),
        "OperatingSystem": (str, True),
        "SourceVersionNumber": (integer, False),
        "SupportContainerDefinitions": ([SupportContainerDefinition], False),
        "Tags": (Tags, False),
        "TotalMemoryLimitMebibytes": (integer, True),
        "TotalVcpuLimit": (double, True),
        "VersionDescription": (str, False),
    }


class AnywhereConfiguration(AWSProperty):
    """
    `AnywhereConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-fleet-anywhereconfiguration.html>`__
    """

    props: PropsDictType = {
        "Cost": (str, True),
    }


class CertificateConfiguration(AWSProperty):
    """
    `CertificateConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-fleet-certificateconfiguration.html>`__
    """

    props: PropsDictType = {
        "CertificateType": (str, True),
    }


class ResourceCreationLimitPolicy(AWSProperty):
    """
    `ResourceCreationLimitPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-fleet-resourcecreationlimitpolicy.html>`__
    """

    props: PropsDictType = {
        "NewGameSessionsPerCreator": (integer, False),
        "PolicyPeriodInMinutes": (integer, False),
    }


class ServerProcess(AWSProperty):
    """
    `ServerProcess <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-fleet-serverprocess.html>`__
    """

    props: PropsDictType = {
        "ConcurrentExecutions": (integer, True),
        "LaunchPath": (str, True),
        "Parameters": (str, False),
    }


class RuntimeConfiguration(AWSProperty):
    """
    `RuntimeConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-fleet-runtimeconfiguration.html>`__
    """

    props: PropsDictType = {
        "GameSessionActivationTimeoutSeconds": (integer, False),
        "MaxConcurrentGameSessionActivations": (integer, False),
        "ServerProcesses": ([ServerProcess], False),
    }


class Fleet(AWSObject):
    """
    `Fleet <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-gamelift-fleet.html>`__
    """

    resource_type = "AWS::GameLift::Fleet"

    props: PropsDictType = {
        "AnywhereConfiguration": (AnywhereConfiguration, False),
        "ApplyCapacity": (str, False),
        "BuildId": (str, False),
        "CertificateConfiguration": (CertificateConfiguration, False),
        "ComputeType": (str, False),
        "Description": (str, False),
        "DesiredEC2Instances": (integer, False),
        "EC2InboundPermissions": ([IpPermission], False),
        "EC2InstanceType": (str, False),
        "FleetType": (str, False),
        "InstanceRoleARN": (str, False),
        "InstanceRoleCredentialsProvider": (str, False),
        "Locations": ([LocationConfiguration], False),
        "MaxSize": (integer, False),
        "MetricGroups": ([str], False),
        "MinSize": (integer, False),
        "Name": (str, True),
        "NewGameSessionProtectionPolicy": (str, False),
        "PeerVpcAwsAccountId": (str, False),
        "PeerVpcId": (str, False),
        "ResourceCreationLimitPolicy": (ResourceCreationLimitPolicy, False),
        "RuntimeConfiguration": (RuntimeConfiguration, False),
        "ScalingPolicies": ([ScalingPolicy], False),
        "ScriptId": (str, False),
    }


class TargetTrackingConfiguration(AWSProperty):
    """
    `TargetTrackingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-gameservergroup-targettrackingconfiguration.html>`__
    """

    props: PropsDictType = {
        "TargetValue": (double, True),
    }


class AutoScalingPolicy(AWSProperty):
    """
    `AutoScalingPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-gameservergroup-autoscalingpolicy.html>`__
    """

    props: PropsDictType = {
        "EstimatedInstanceWarmup": (double, False),
        "TargetTrackingConfiguration": (TargetTrackingConfiguration, True),
    }


class InstanceDefinition(AWSProperty):
    """
    `InstanceDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-gameservergroup-instancedefinition.html>`__
    """

    props: PropsDictType = {
        "InstanceType": (str, True),
        "WeightedCapacity": (str, False),
    }


class LaunchTemplate(AWSProperty):
    """
    `LaunchTemplate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-gameservergroup-launchtemplate.html>`__
    """

    props: PropsDictType = {
        "LaunchTemplateId": (str, False),
        "LaunchTemplateName": (str, False),
        "Version": (str, False),
    }


class GameServerGroup(AWSObject):
    """
    `GameServerGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-gamelift-gameservergroup.html>`__
    """

    resource_type = "AWS::GameLift::GameServerGroup"

    props: PropsDictType = {
        "AutoScalingPolicy": (AutoScalingPolicy, False),
        "BalancingStrategy": (str, False),
        "DeleteOption": (str, False),
        "GameServerGroupName": (str, True),
        "GameServerProtectionPolicy": (str, False),
        "InstanceDefinitions": ([InstanceDefinition], True),
        "LaunchTemplate": (LaunchTemplate, False),
        "MaxSize": (double, False),
        "MinSize": (double, False),
        "RoleArn": (str, True),
        "Tags": (Tags, False),
        "VpcSubnets": ([str], False),
    }


class FilterConfiguration(AWSProperty):
    """
    `FilterConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-gamesessionqueue-filterconfiguration.html>`__
    """

    props: PropsDictType = {
        "AllowedLocations": ([str], False),
    }


class GameSessionQueueDestination(AWSProperty):
    """
    `GameSessionQueueDestination <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-gamesessionqueue-gamesessionqueuedestination.html>`__
    """

    props: PropsDictType = {
        "DestinationArn": (str, False),
    }


class PlayerLatencyPolicy(AWSProperty):
    """
    `PlayerLatencyPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-gamesessionqueue-playerlatencypolicy.html>`__
    """

    props: PropsDictType = {
        "MaximumIndividualPlayerLatencyMilliseconds": (integer, False),
        "PolicyDurationSeconds": (integer, False),
    }


class PriorityConfiguration(AWSProperty):
    """
    `PriorityConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-gamesessionqueue-priorityconfiguration.html>`__
    """

    props: PropsDictType = {
        "LocationOrder": ([str], False),
        "PriorityOrder": ([str], False),
    }


class GameSessionQueue(AWSObject):
    """
    `GameSessionQueue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-gamelift-gamesessionqueue.html>`__
    """

    resource_type = "AWS::GameLift::GameSessionQueue"

    props: PropsDictType = {
        "CustomEventData": (str, False),
        "Destinations": ([GameSessionQueueDestination], False),
        "FilterConfiguration": (FilterConfiguration, False),
        "Name": (str, True),
        "NotificationTarget": (str, False),
        "PlayerLatencyPolicies": ([PlayerLatencyPolicy], False),
        "PriorityConfiguration": (PriorityConfiguration, False),
        "Tags": (Tags, False),
        "TimeoutInSeconds": (integer, False),
    }


class Location(AWSObject):
    """
    `Location <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-gamelift-location.html>`__
    """

    resource_type = "AWS::GameLift::Location"

    props: PropsDictType = {
        "LocationName": (str, True),
        "Tags": (Tags, False),
    }


class GameProperty(AWSProperty):
    """
    `GameProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-matchmakingconfiguration-gameproperty.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (str, True),
    }


class MatchmakingConfiguration(AWSObject):
    """
    `MatchmakingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-gamelift-matchmakingconfiguration.html>`__
    """

    resource_type = "AWS::GameLift::MatchmakingConfiguration"

    props: PropsDictType = {
        "AcceptanceRequired": (boolean, True),
        "AcceptanceTimeoutSeconds": (integer, False),
        "AdditionalPlayerCount": (integer, False),
        "BackfillMode": (str, False),
        "CreationTime": (str, False),
        "CustomEventData": (str, False),
        "Description": (str, False),
        "FlexMatchMode": (str, False),
        "GameProperties": ([GameProperty], False),
        "GameSessionData": (str, False),
        "GameSessionQueueArns": ([str], False),
        "Name": (str, True),
        "NotificationTarget": (str, False),
        "RequestTimeoutSeconds": (integer, True),
        "RuleSetArn": (str, False),
        "RuleSetName": (str, True),
        "Tags": (Tags, False),
    }


class MatchmakingRuleSet(AWSObject):
    """
    `MatchmakingRuleSet <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-gamelift-matchmakingruleset.html>`__
    """

    resource_type = "AWS::GameLift::MatchmakingRuleSet"

    props: PropsDictType = {
        "Name": (str, True),
        "RuleSetBody": (str, True),
        "Tags": (Tags, False),
    }


class S3Location(AWSProperty):
    """
    `S3Location <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-script-s3location.html>`__
    """

    props: PropsDictType = {
        "Bucket": (str, True),
        "Key": (str, True),
        "ObjectVersion": (str, False),
        "RoleArn": (str, True),
    }


class Script(AWSObject):
    """
    `Script <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-gamelift-script.html>`__
    """

    resource_type = "AWS::GameLift::Script"

    props: PropsDictType = {
        "Name": (str, False),
        "StorageLocation": (S3Location, True),
        "Tags": (Tags, False),
        "Version": (str, False),
    }


class DeploymentDetails(AWSProperty):
    """
    `DeploymentDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-gamelift-containerfleet-deploymentdetails.html>`__
    """

    props: PropsDictType = {
        "LatestDeploymentId": (str, False),
    }
