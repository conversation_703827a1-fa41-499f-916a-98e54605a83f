# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags


class DRTAccess(AWSObject):
    """
    `DRTAccess <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-shield-drtaccess.html>`__
    """

    resource_type = "AWS::Shield::DRTAccess"

    props: PropsDictType = {
        "LogBucketList": ([str], False),
        "RoleArn": (str, True),
    }


class EmergencyContact(AWSProperty):
    """
    `EmergencyContact <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-shield-proactiveengagement-emergencycontact.html>`__
    """

    props: PropsDictType = {
        "ContactNotes": (str, False),
        "EmailAddress": (str, True),
        "PhoneNumber": (str, False),
    }


class ProactiveEngagement(AWSObject):
    """
    `ProactiveEngagement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-shield-proactiveengagement.html>`__
    """

    resource_type = "AWS::Shield::ProactiveEngagement"

    props: PropsDictType = {
        "EmergencyContactList": ([EmergencyContact], True),
        "ProactiveEngagementStatus": (str, True),
    }


class Action(AWSProperty):
    """
    `Action <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-shield-protection-action.html>`__
    """

    props: PropsDictType = {
        "Block": (dict, False),
        "Count": (dict, False),
    }


class ApplicationLayerAutomaticResponseConfiguration(AWSProperty):
    """
    `ApplicationLayerAutomaticResponseConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-shield-protection-applicationlayerautomaticresponseconfiguration.html>`__
    """

    props: PropsDictType = {
        "Action": (Action, True),
        "Status": (str, True),
    }


class Protection(AWSObject):
    """
    `Protection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-shield-protection.html>`__
    """

    resource_type = "AWS::Shield::Protection"

    props: PropsDictType = {
        "ApplicationLayerAutomaticResponseConfiguration": (
            ApplicationLayerAutomaticResponseConfiguration,
            False,
        ),
        "HealthCheckArns": ([str], False),
        "Name": (str, True),
        "ResourceArn": (str, True),
        "Tags": (Tags, False),
    }


class ProtectionGroup(AWSObject):
    """
    `ProtectionGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-shield-protectiongroup.html>`__
    """

    resource_type = "AWS::Shield::ProtectionGroup"

    props: PropsDictType = {
        "Aggregation": (str, True),
        "Members": ([str], False),
        "Pattern": (str, True),
        "ProtectionGroupId": (str, True),
        "ResourceType": (str, False),
        "Tags": (Tags, False),
    }
