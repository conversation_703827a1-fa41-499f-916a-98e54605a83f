# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer


class Discovery(AWSObject):
    """
    `Discovery <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-applicationsignals-discovery.html>`__
    """

    resource_type = "AWS::ApplicationSignals::Discovery"

    props: PropsDictType = {}


class BurnRateConfiguration(AWSProperty):
    """
    `BurnRateConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-burnrateconfiguration.html>`__
    """

    props: PropsDictType = {
        "LookBackWindowMinutes": (integer, True),
    }


class RecurrenceRule(AWSProperty):
    """
    `RecurrenceRule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-recurrencerule.html>`__
    """

    props: PropsDictType = {
        "Expression": (str, True),
    }


class Window(AWSProperty):
    """
    `Window <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-window.html>`__
    """

    props: PropsDictType = {
        "Duration": (integer, True),
        "DurationUnit": (str, True),
    }


class ExclusionWindow(AWSProperty):
    """
    `ExclusionWindow <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-exclusionwindow.html>`__
    """

    props: PropsDictType = {
        "Reason": (str, False),
        "RecurrenceRule": (RecurrenceRule, False),
        "StartTime": (str, False),
        "Window": (Window, True),
    }


class CalendarInterval(AWSProperty):
    """
    `CalendarInterval <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-calendarinterval.html>`__
    """

    props: PropsDictType = {
        "Duration": (integer, True),
        "DurationUnit": (str, True),
        "StartTime": (integer, True),
    }


class RollingInterval(AWSProperty):
    """
    `RollingInterval <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-rollinginterval.html>`__
    """

    props: PropsDictType = {
        "Duration": (integer, True),
        "DurationUnit": (str, True),
    }


class Interval(AWSProperty):
    """
    `Interval <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-interval.html>`__
    """

    props: PropsDictType = {
        "CalendarInterval": (CalendarInterval, False),
        "RollingInterval": (RollingInterval, False),
    }


class Goal(AWSProperty):
    """
    `Goal <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-goal.html>`__
    """

    props: PropsDictType = {
        "AttainmentGoal": (double, False),
        "Interval": (Interval, False),
        "WarningThreshold": (double, False),
    }


class DependencyConfig(AWSProperty):
    """
    `DependencyConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-dependencyconfig.html>`__
    """

    props: PropsDictType = {
        "DependencyKeyAttributes": (dict, True),
        "DependencyOperationName": (str, True),
    }


class Dimension(AWSProperty):
    """
    `Dimension <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-dimension.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "Value": (str, True),
    }


class Metric(AWSProperty):
    """
    `Metric <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-metric.html>`__
    """

    props: PropsDictType = {
        "Dimensions": ([Dimension], False),
        "MetricName": (str, False),
        "Namespace": (str, False),
    }


class MetricStat(AWSProperty):
    """
    `MetricStat <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-metricstat.html>`__
    """

    props: PropsDictType = {
        "Metric": (Metric, True),
        "Period": (integer, True),
        "Stat": (str, True),
        "Unit": (str, False),
    }


class MetricDataQuery(AWSProperty):
    """
    `MetricDataQuery <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-metricdataquery.html>`__
    """

    props: PropsDictType = {
        "AccountId": (str, False),
        "Expression": (str, False),
        "Id": (str, True),
        "MetricStat": (MetricStat, False),
        "ReturnData": (boolean, False),
    }


class MonitoredRequestCountMetric(AWSProperty):
    """
    `MonitoredRequestCountMetric <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-monitoredrequestcountmetric.html>`__
    """

    props: PropsDictType = {
        "BadCountMetric": ([MetricDataQuery], False),
        "GoodCountMetric": ([MetricDataQuery], False),
    }


class RequestBasedSliMetric(AWSProperty):
    """
    `RequestBasedSliMetric <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-requestbasedslimetric.html>`__
    """

    props: PropsDictType = {
        "DependencyConfig": (DependencyConfig, False),
        "KeyAttributes": (dict, False),
        "MetricType": (str, False),
        "MonitoredRequestCountMetric": (MonitoredRequestCountMetric, False),
        "OperationName": (str, False),
        "TotalRequestCountMetric": ([MetricDataQuery], False),
    }


class RequestBasedSli(AWSProperty):
    """
    `RequestBasedSli <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-requestbasedsli.html>`__
    """

    props: PropsDictType = {
        "ComparisonOperator": (str, False),
        "MetricThreshold": (double, False),
        "RequestBasedSliMetric": (RequestBasedSliMetric, True),
    }


class SliMetric(AWSProperty):
    """
    `SliMetric <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-slimetric.html>`__
    """

    props: PropsDictType = {
        "DependencyConfig": (DependencyConfig, False),
        "KeyAttributes": (dict, False),
        "MetricDataQueries": ([MetricDataQuery], False),
        "MetricType": (str, False),
        "OperationName": (str, False),
        "PeriodSeconds": (integer, False),
        "Statistic": (str, False),
    }


class Sli(AWSProperty):
    """
    `Sli <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-applicationsignals-servicelevelobjective-sli.html>`__
    """

    props: PropsDictType = {
        "ComparisonOperator": (str, True),
        "MetricThreshold": (double, True),
        "SliMetric": (SliMetric, True),
    }


class ServiceLevelObjective(AWSObject):
    """
    `ServiceLevelObjective <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-applicationsignals-servicelevelobjective.html>`__
    """

    resource_type = "AWS::ApplicationSignals::ServiceLevelObjective"

    props: PropsDictType = {
        "BurnRateConfigurations": ([BurnRateConfiguration], False),
        "Description": (str, False),
        "ExclusionWindows": ([ExclusionWindow], False),
        "Goal": (Goal, False),
        "Name": (str, True),
        "RequestBasedSli": (RequestBasedSli, False),
        "Sli": (Sli, False),
        "Tags": (Tags, False),
    }
