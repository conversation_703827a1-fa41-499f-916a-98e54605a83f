# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, PropsDictType, Tags
from .validators import boolean


class Graph(AWSObject):
    """
    `Graph <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-detective-graph.html>`__
    """

    resource_type = "AWS::Detective::Graph"

    props: PropsDictType = {
        "AutoEnableMembers": (boolean, False),
        "Tags": (Tags, False),
    }


class MemberInvitation(AWSObject):
    """
    `MemberInvitation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-detective-memberinvitation.html>`__
    """

    resource_type = "AWS::Detective::MemberInvitation"

    props: PropsDictType = {
        "DisableEmailNotification": (boolean, False),
        "GraphArn": (str, True),
        "MemberEmailAddress": (str, True),
        "MemberId": (str, True),
        "Message": (str, False),
    }


class OrganizationAdmin(AWSObject):
    """
    `OrganizationAdmin <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-detective-organizationadmin.html>`__
    """

    resource_type = "AWS::Detective::OrganizationAdmin"

    props: PropsDictType = {
        "AccountId": (str, True),
    }
