# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer
from .validators.lex import policytypes


class CustomVocabularyItem(AWSProperty):
    """
    `CustomVocabularyItem <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-customvocabularyitem.html>`__
    """

    props: PropsDictType = {
        "DisplayAs": (str, False),
        "Phrase": (str, True),
        "Weight": (integer, False),
    }


class CustomVocabulary(AWSProperty):
    """
    `CustomVocabulary <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-customvocabulary.html>`__
    """

    props: PropsDictType = {
        "CustomVocabularyItems": ([CustomVocabularyItem], True),
    }


class DialogCodeHookSetting(AWSProperty):
    """
    `DialogCodeHookSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-dialogcodehooksetting.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, True),
    }


class CustomPayload(AWSProperty):
    """
    `CustomPayload <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-custompayload.html>`__
    """

    props: PropsDictType = {
        "Value": (str, True),
    }


class Button(AWSProperty):
    """
    `Button <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-button.html>`__
    """

    props: PropsDictType = {
        "Text": (str, True),
        "Value": (str, True),
    }


class ImageResponseCard(AWSProperty):
    """
    `ImageResponseCard <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-imageresponsecard.html>`__
    """

    props: PropsDictType = {
        "Buttons": ([Button], False),
        "ImageUrl": (str, False),
        "Subtitle": (str, False),
        "Title": (str, True),
    }


class PlainTextMessage(AWSProperty):
    """
    `PlainTextMessage <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-plaintextmessage.html>`__
    """

    props: PropsDictType = {
        "Value": (str, True),
    }


class SSMLMessage(AWSProperty):
    """
    `SSMLMessage <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-ssmlmessage.html>`__
    """

    props: PropsDictType = {
        "Value": (str, True),
    }


class Message(AWSProperty):
    """
    `Message <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-message.html>`__
    """

    props: PropsDictType = {
        "CustomPayload": (CustomPayload, False),
        "ImageResponseCard": (ImageResponseCard, False),
        "PlainTextMessage": (PlainTextMessage, False),
        "SSMLMessage": (SSMLMessage, False),
    }


class MessageGroup(AWSProperty):
    """
    `MessageGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-messagegroup.html>`__
    """

    props: PropsDictType = {
        "Message": (Message, True),
        "Variations": ([Message], False),
    }


class FulfillmentStartResponseSpecification(AWSProperty):
    """
    `FulfillmentStartResponseSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-fulfillmentstartresponsespecification.html>`__
    """

    props: PropsDictType = {
        "AllowInterrupt": (boolean, False),
        "DelayInSeconds": (integer, True),
        "MessageGroups": ([MessageGroup], True),
    }


class FulfillmentUpdateResponseSpecification(AWSProperty):
    """
    `FulfillmentUpdateResponseSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-fulfillmentupdateresponsespecification.html>`__
    """

    props: PropsDictType = {
        "AllowInterrupt": (boolean, False),
        "FrequencyInSeconds": (integer, True),
        "MessageGroups": ([MessageGroup], True),
    }


class FulfillmentUpdatesSpecification(AWSProperty):
    """
    `FulfillmentUpdatesSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-fulfillmentupdatesspecification.html>`__
    """

    props: PropsDictType = {
        "Active": (boolean, True),
        "StartResponse": (FulfillmentStartResponseSpecification, False),
        "TimeoutInSeconds": (integer, False),
        "UpdateResponse": (FulfillmentUpdateResponseSpecification, False),
    }


class Condition(AWSProperty):
    """
    `Condition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-condition.html>`__
    """

    props: PropsDictType = {
        "ExpressionString": (str, True),
    }


class DialogAction(AWSProperty):
    """
    `DialogAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-dialogaction.html>`__
    """

    props: PropsDictType = {
        "SlotToElicit": (str, False),
        "SuppressNextMessage": (boolean, False),
        "Type": (str, True),
    }


class SlotValue(AWSProperty):
    """
    `SlotValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-slotvalue.html>`__
    """

    props: PropsDictType = {
        "InterpretedValue": (str, False),
    }


class SlotValueOverride(AWSProperty):
    """
    `SlotValueOverride <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-slotvalueoverride.html>`__
    """

    props: PropsDictType = {
        "Shape": (str, False),
        "Value": (SlotValue, False),
        "Values": ([str], False),
    }


class SlotValueOverrideMap(AWSProperty):
    """
    `SlotValueOverrideMap <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-slotvalueoverridemap.html>`__
    """

    props: PropsDictType = {
        "SlotName": (str, False),
        "SlotValueOverride": (SlotValueOverride, False),
    }


class IntentOverride(AWSProperty):
    """
    `IntentOverride <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-intentoverride.html>`__
    """

    props: PropsDictType = {
        "Name": (str, False),
        "Slots": ([SlotValueOverrideMap], False),
    }


class SessionAttribute(AWSProperty):
    """
    `SessionAttribute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-sessionattribute.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (str, False),
    }


class DialogState(AWSProperty):
    """
    `DialogState <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-dialogstate.html>`__
    """

    props: PropsDictType = {
        "DialogAction": (DialogAction, False),
        "Intent": (IntentOverride, False),
        "SessionAttributes": ([SessionAttribute], False),
    }


class ResponseSpecification(AWSProperty):
    """
    `ResponseSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-responsespecification.html>`__
    """

    props: PropsDictType = {
        "AllowInterrupt": (boolean, False),
        "MessageGroupsList": ([MessageGroup], True),
    }


class ConditionalBranch(AWSProperty):
    """
    `ConditionalBranch <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-conditionalbranch.html>`__
    """

    props: PropsDictType = {
        "Condition": (Condition, True),
        "Name": (str, True),
        "NextStep": (DialogState, True),
        "Response": (ResponseSpecification, False),
    }


class DefaultConditionalBranch(AWSProperty):
    """
    `DefaultConditionalBranch <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-defaultconditionalbranch.html>`__
    """

    props: PropsDictType = {
        "NextStep": (DialogState, False),
        "Response": (ResponseSpecification, False),
    }


class ConditionalSpecification(AWSProperty):
    """
    `ConditionalSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-conditionalspecification.html>`__
    """

    props: PropsDictType = {
        "ConditionalBranches": ([ConditionalBranch], True),
        "DefaultBranch": (DefaultConditionalBranch, True),
        "IsActive": (boolean, True),
    }


class PostFulfillmentStatusSpecification(AWSProperty):
    """
    `PostFulfillmentStatusSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-postfulfillmentstatusspecification.html>`__
    """

    props: PropsDictType = {
        "FailureConditional": (ConditionalSpecification, False),
        "FailureNextStep": (DialogState, False),
        "FailureResponse": (ResponseSpecification, False),
        "SuccessConditional": (ConditionalSpecification, False),
        "SuccessNextStep": (DialogState, False),
        "SuccessResponse": (ResponseSpecification, False),
        "TimeoutConditional": (ConditionalSpecification, False),
        "TimeoutNextStep": (DialogState, False),
        "TimeoutResponse": (ResponseSpecification, False),
    }


class FulfillmentCodeHookSetting(AWSProperty):
    """
    `FulfillmentCodeHookSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-fulfillmentcodehooksetting.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, True),
        "FulfillmentUpdatesSpecification": (FulfillmentUpdatesSpecification, False),
        "IsActive": (boolean, False),
        "PostFulfillmentStatusSpecification": (
            PostFulfillmentStatusSpecification,
            False,
        ),
    }


class PostDialogCodeHookInvocationSpecification(AWSProperty):
    """
    `PostDialogCodeHookInvocationSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-postdialogcodehookinvocationspecification.html>`__
    """

    props: PropsDictType = {
        "FailureConditional": (ConditionalSpecification, False),
        "FailureNextStep": (DialogState, False),
        "FailureResponse": (ResponseSpecification, False),
        "SuccessConditional": (ConditionalSpecification, False),
        "SuccessNextStep": (DialogState, False),
        "SuccessResponse": (ResponseSpecification, False),
        "TimeoutConditional": (ConditionalSpecification, False),
        "TimeoutNextStep": (DialogState, False),
        "TimeoutResponse": (ResponseSpecification, False),
    }


class DialogCodeHookInvocationSetting(AWSProperty):
    """
    `DialogCodeHookInvocationSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-dialogcodehookinvocationsetting.html>`__
    """

    props: PropsDictType = {
        "EnableCodeHookInvocation": (boolean, True),
        "InvocationLabel": (str, False),
        "IsActive": (boolean, True),
        "PostCodeHookSpecification": (PostDialogCodeHookInvocationSpecification, True),
    }


class InitialResponseSetting(AWSProperty):
    """
    `InitialResponseSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-initialresponsesetting.html>`__
    """

    props: PropsDictType = {
        "CodeHook": (DialogCodeHookInvocationSetting, False),
        "Conditional": (ConditionalSpecification, False),
        "InitialResponse": (ResponseSpecification, False),
        "NextStep": (DialogState, False),
    }


class InputContext(AWSProperty):
    """
    `InputContext <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-inputcontext.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
    }


class IntentClosingSetting(AWSProperty):
    """
    `IntentClosingSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-intentclosingsetting.html>`__
    """

    props: PropsDictType = {
        "ClosingResponse": (ResponseSpecification, False),
        "Conditional": (ConditionalSpecification, False),
        "IsActive": (boolean, False),
        "NextStep": (DialogState, False),
    }


class ElicitationCodeHookInvocationSetting(AWSProperty):
    """
    `ElicitationCodeHookInvocationSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-elicitationcodehookinvocationsetting.html>`__
    """

    props: PropsDictType = {
        "EnableCodeHookInvocation": (boolean, True),
        "InvocationLabel": (str, False),
    }


class AllowedInputTypes(AWSProperty):
    """
    `AllowedInputTypes <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-allowedinputtypes.html>`__
    """

    props: PropsDictType = {
        "AllowAudioInput": (boolean, True),
        "AllowDTMFInput": (boolean, True),
    }


class AudioSpecification(AWSProperty):
    """
    `AudioSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-audiospecification.html>`__
    """

    props: PropsDictType = {
        "EndTimeoutMs": (integer, True),
        "MaxLengthMs": (integer, True),
    }


class DTMFSpecification(AWSProperty):
    """
    `DTMFSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-dtmfspecification.html>`__
    """

    props: PropsDictType = {
        "DeletionCharacter": (str, True),
        "EndCharacter": (str, True),
        "EndTimeoutMs": (integer, True),
        "MaxLength": (integer, True),
    }


class AudioAndDTMFInputSpecification(AWSProperty):
    """
    `AudioAndDTMFInputSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-audioanddtmfinputspecification.html>`__
    """

    props: PropsDictType = {
        "AudioSpecification": (AudioSpecification, False),
        "DTMFSpecification": (DTMFSpecification, False),
        "StartTimeoutMs": (integer, True),
    }


class TextInputSpecification(AWSProperty):
    """
    `TextInputSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-textinputspecification.html>`__
    """

    props: PropsDictType = {
        "StartTimeoutMs": (integer, True),
    }


class PromptAttemptSpecification(AWSProperty):
    """
    `PromptAttemptSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-promptattemptspecification.html>`__
    """

    props: PropsDictType = {
        "AllowInterrupt": (boolean, False),
        "AllowedInputTypes": (AllowedInputTypes, True),
        "AudioAndDTMFInputSpecification": (AudioAndDTMFInputSpecification, False),
        "TextInputSpecification": (TextInputSpecification, False),
    }


class PromptSpecification(AWSProperty):
    """
    `PromptSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-promptspecification.html>`__
    """

    props: PropsDictType = {
        "AllowInterrupt": (boolean, False),
        "MaxRetries": (integer, True),
        "MessageGroupsList": ([MessageGroup], True),
        "MessageSelectionStrategy": (str, False),
        "PromptAttemptsSpecification": (dict, False),
    }


class IntentConfirmationSetting(AWSProperty):
    """
    `IntentConfirmationSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-intentconfirmationsetting.html>`__
    """

    props: PropsDictType = {
        "CodeHook": (DialogCodeHookInvocationSetting, False),
        "ConfirmationConditional": (ConditionalSpecification, False),
        "ConfirmationNextStep": (DialogState, False),
        "ConfirmationResponse": (ResponseSpecification, False),
        "DeclinationConditional": (ConditionalSpecification, False),
        "DeclinationNextStep": (DialogState, False),
        "DeclinationResponse": (ResponseSpecification, False),
        "ElicitationCodeHook": (ElicitationCodeHookInvocationSetting, False),
        "FailureConditional": (ConditionalSpecification, False),
        "FailureNextStep": (DialogState, False),
        "FailureResponse": (ResponseSpecification, False),
        "IsActive": (boolean, False),
        "PromptSpecification": (PromptSpecification, True),
    }


class KendraConfiguration(AWSProperty):
    """
    `KendraConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-kendraconfiguration.html>`__
    """

    props: PropsDictType = {
        "KendraIndex": (str, True),
        "QueryFilterString": (str, False),
        "QueryFilterStringEnabled": (boolean, False),
    }


class OutputContext(AWSProperty):
    """
    `OutputContext <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-outputcontext.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "TimeToLiveInSeconds": (integer, True),
        "TurnsToLive": (integer, True),
    }


class BedrockGuardrailConfiguration(AWSProperty):
    """
    `BedrockGuardrailConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-bedrockguardrailconfiguration.html>`__
    """

    props: PropsDictType = {
        "BedrockGuardrailIdentifier": (str, False),
        "BedrockGuardrailVersion": (str, False),
    }


class BedrockModelSpecification(AWSProperty):
    """
    `BedrockModelSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-bedrockmodelspecification.html>`__
    """

    props: PropsDictType = {
        "BedrockGuardrailConfiguration": (BedrockGuardrailConfiguration, False),
        "BedrockModelCustomPrompt": (str, False),
        "BedrockTraceStatus": (str, False),
        "ModelArn": (str, True),
    }


class BKBExactResponseFields(AWSProperty):
    """
    `BKBExactResponseFields <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-bkbexactresponsefields.html>`__
    """

    props: PropsDictType = {
        "AnswerField": (str, False),
    }


class BedrockKnowledgeStoreConfiguration(AWSProperty):
    """
    `BedrockKnowledgeStoreConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-bedrockknowledgestoreconfiguration.html>`__
    """

    props: PropsDictType = {
        "BKBExactResponseFields": (BKBExactResponseFields, False),
        "BedrockKnowledgeBaseArn": (str, False),
        "ExactResponse": (boolean, False),
    }


class ExactResponseFields(AWSProperty):
    """
    `ExactResponseFields <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-exactresponsefields.html>`__
    """

    props: PropsDictType = {
        "AnswerField": (str, False),
        "QuestionField": (str, False),
    }


class OpensearchConfiguration(AWSProperty):
    """
    `OpensearchConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-opensearchconfiguration.html>`__
    """

    props: PropsDictType = {
        "DomainEndpoint": (str, False),
        "ExactResponse": (boolean, False),
        "ExactResponseFields": (ExactResponseFields, False),
        "IncludeFields": ([str], False),
        "IndexName": (str, False),
    }


class QnAKendraConfiguration(AWSProperty):
    """
    `QnAKendraConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-qnakendraconfiguration.html>`__
    """

    props: PropsDictType = {
        "ExactResponse": (boolean, True),
        "KendraIndex": (str, True),
        "QueryFilterString": (str, False),
        "QueryFilterStringEnabled": (boolean, True),
    }


class DataSourceConfiguration(AWSProperty):
    """
    `DataSourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-datasourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "BedrockKnowledgeStoreConfiguration": (
            BedrockKnowledgeStoreConfiguration,
            False,
        ),
        "KendraConfiguration": (QnAKendraConfiguration, False),
        "OpensearchConfiguration": (OpensearchConfiguration, False),
    }


class QnAIntentConfiguration(AWSProperty):
    """
    `QnAIntentConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-qnaintentconfiguration.html>`__
    """

    props: PropsDictType = {
        "BedrockModelConfiguration": (BedrockModelSpecification, True),
        "DataSourceConfiguration": (DataSourceConfiguration, True),
    }


class SampleUtterance(AWSProperty):
    """
    `SampleUtterance <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-sampleutterance.html>`__
    """

    props: PropsDictType = {
        "Utterance": (str, True),
    }


class MultipleValuesSetting(AWSProperty):
    """
    `MultipleValuesSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-multiplevaluessetting.html>`__
    """

    props: PropsDictType = {
        "AllowMultipleValues": (boolean, False),
    }


class ObfuscationSetting(AWSProperty):
    """
    `ObfuscationSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-obfuscationsetting.html>`__
    """

    props: PropsDictType = {
        "ObfuscationSettingType": (str, True),
    }


class SlotCaptureSetting(AWSProperty):
    """
    `SlotCaptureSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-slotcapturesetting.html>`__
    """

    props: PropsDictType = {
        "CaptureConditional": (ConditionalSpecification, False),
        "CaptureNextStep": (DialogState, False),
        "CaptureResponse": (ResponseSpecification, False),
        "CodeHook": (DialogCodeHookInvocationSetting, False),
        "ElicitationCodeHook": (ElicitationCodeHookInvocationSetting, False),
        "FailureConditional": (ConditionalSpecification, False),
        "FailureNextStep": (DialogState, False),
        "FailureResponse": (ResponseSpecification, False),
    }


class SlotDefaultValue(AWSProperty):
    """
    `SlotDefaultValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-slotdefaultvalue.html>`__
    """

    props: PropsDictType = {
        "DefaultValue": (str, True),
    }


class SlotDefaultValueSpecification(AWSProperty):
    """
    `SlotDefaultValueSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-slotdefaultvaluespecification.html>`__
    """

    props: PropsDictType = {
        "DefaultValueList": ([SlotDefaultValue], True),
    }


class StillWaitingResponseSpecification(AWSProperty):
    """
    `StillWaitingResponseSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-stillwaitingresponsespecification.html>`__
    """

    props: PropsDictType = {
        "AllowInterrupt": (boolean, False),
        "FrequencyInSeconds": (integer, True),
        "MessageGroupsList": ([MessageGroup], True),
        "TimeoutInSeconds": (integer, True),
    }


class WaitAndContinueSpecification(AWSProperty):
    """
    `WaitAndContinueSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-waitandcontinuespecification.html>`__
    """

    props: PropsDictType = {
        "ContinueResponse": (ResponseSpecification, True),
        "IsActive": (boolean, False),
        "StillWaitingResponse": (StillWaitingResponseSpecification, False),
        "WaitingResponse": (ResponseSpecification, True),
    }


class SlotValueElicitationSetting(AWSProperty):
    """
    `SlotValueElicitationSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-slotvalueelicitationsetting.html>`__
    """

    props: PropsDictType = {
        "DefaultValueSpecification": (SlotDefaultValueSpecification, False),
        "PromptSpecification": (PromptSpecification, False),
        "SampleUtterances": ([SampleUtterance], False),
        "SlotCaptureSetting": (SlotCaptureSetting, False),
        "SlotConstraint": (str, True),
        "WaitAndContinueSpecification": (WaitAndContinueSpecification, False),
    }


class Slot(AWSProperty):
    """
    `Slot <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-slot.html>`__
    """

    props: PropsDictType = {
        "Description": (str, False),
        "MultipleValuesSetting": (MultipleValuesSetting, False),
        "Name": (str, True),
        "ObfuscationSetting": (ObfuscationSetting, False),
        "SlotTypeName": (str, True),
        "ValueElicitationSetting": (SlotValueElicitationSetting, True),
    }


class SlotPriority(AWSProperty):
    """
    `SlotPriority <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-slotpriority.html>`__
    """

    props: PropsDictType = {
        "Priority": (integer, True),
        "SlotName": (str, True),
    }


class Intent(AWSProperty):
    """
    `Intent <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-intent.html>`__
    """

    props: PropsDictType = {
        "Description": (str, False),
        "DialogCodeHook": (DialogCodeHookSetting, False),
        "FulfillmentCodeHook": (FulfillmentCodeHookSetting, False),
        "InitialResponseSetting": (InitialResponseSetting, False),
        "InputContexts": ([InputContext], False),
        "IntentClosingSetting": (IntentClosingSetting, False),
        "IntentConfirmationSetting": (IntentConfirmationSetting, False),
        "KendraConfiguration": (KendraConfiguration, False),
        "Name": (str, True),
        "OutputContexts": ([OutputContext], False),
        "ParentIntentSignature": (str, False),
        "QnAIntentConfiguration": (QnAIntentConfiguration, False),
        "SampleUtterances": ([SampleUtterance], False),
        "SlotPriorities": ([SlotPriority], False),
        "Slots": ([Slot], False),
    }


class GrammarSlotTypeSource(AWSProperty):
    """
    `GrammarSlotTypeSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-grammarslottypesource.html>`__
    """

    props: PropsDictType = {
        "KmsKeyArn": (str, False),
        "S3BucketName": (str, True),
        "S3ObjectKey": (str, True),
    }


class GrammarSlotTypeSetting(AWSProperty):
    """
    `GrammarSlotTypeSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-grammarslottypesetting.html>`__
    """

    props: PropsDictType = {
        "Source": (GrammarSlotTypeSource, False),
    }


class ExternalSourceSetting(AWSProperty):
    """
    `ExternalSourceSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-externalsourcesetting.html>`__
    """

    props: PropsDictType = {
        "GrammarSlotTypeSetting": (GrammarSlotTypeSetting, False),
    }


class SampleValue(AWSProperty):
    """
    `SampleValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-samplevalue.html>`__
    """

    props: PropsDictType = {
        "Value": (str, True),
    }


class SlotTypeValue(AWSProperty):
    """
    `SlotTypeValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-slottypevalue.html>`__
    """

    props: PropsDictType = {
        "SampleValue": (SampleValue, True),
        "Synonyms": ([SampleValue], False),
    }


class AdvancedRecognitionSetting(AWSProperty):
    """
    `AdvancedRecognitionSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-advancedrecognitionsetting.html>`__
    """

    props: PropsDictType = {
        "AudioRecognitionStrategy": (str, False),
    }


class SlotValueRegexFilter(AWSProperty):
    """
    `SlotValueRegexFilter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-slotvalueregexfilter.html>`__
    """

    props: PropsDictType = {
        "Pattern": (str, True),
    }


class SlotValueSelectionSetting(AWSProperty):
    """
    `SlotValueSelectionSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-slotvalueselectionsetting.html>`__
    """

    props: PropsDictType = {
        "AdvancedRecognitionSetting": (AdvancedRecognitionSetting, False),
        "RegexFilter": (SlotValueRegexFilter, False),
        "ResolutionStrategy": (str, True),
    }


class SlotType(AWSProperty):
    """
    `SlotType <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-slottype.html>`__
    """

    props: PropsDictType = {
        "Description": (str, False),
        "ExternalSourceSetting": (ExternalSourceSetting, False),
        "Name": (str, True),
        "ParentSlotTypeSignature": (str, False),
        "SlotTypeValues": ([SlotTypeValue], False),
        "ValueSelectionSetting": (SlotValueSelectionSetting, False),
    }


class VoiceSettings(AWSProperty):
    """
    `VoiceSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-voicesettings.html>`__
    """

    props: PropsDictType = {
        "Engine": (str, False),
        "VoiceId": (str, True),
    }


class BotLocale(AWSProperty):
    """
    `BotLocale <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-botlocale.html>`__
    """

    props: PropsDictType = {
        "CustomVocabulary": (CustomVocabulary, False),
        "Description": (str, False),
        "Intents": ([Intent], False),
        "LocaleId": (str, True),
        "NluConfidenceThreshold": (double, True),
        "SlotTypes": ([SlotType], False),
        "VoiceSettings": (VoiceSettings, False),
    }


class DataPrivacy(AWSProperty):
    """
    `DataPrivacy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-dataprivacy.html>`__
    """

    props: PropsDictType = {
        "ChildDirected": (boolean, True),
    }


class Replication(AWSProperty):
    """
    `Replication <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-replication.html>`__
    """

    props: PropsDictType = {
        "ReplicaRegions": ([str], True),
    }


class S3Location(AWSProperty):
    """
    `S3Location <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-s3location.html>`__
    """

    props: PropsDictType = {
        "S3Bucket": (str, True),
        "S3ObjectKey": (str, True),
        "S3ObjectVersion": (str, False),
    }


class LambdaCodeHook(AWSProperty):
    """
    `LambdaCodeHook <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-botalias-lambdacodehook.html>`__
    """

    props: PropsDictType = {
        "CodeHookInterfaceVersion": (str, True),
        "LambdaArn": (str, True),
    }


class CodeHookSpecification(AWSProperty):
    """
    `CodeHookSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-botalias-codehookspecification.html>`__
    """

    props: PropsDictType = {
        "LambdaCodeHook": (LambdaCodeHook, True),
    }


class BotAliasLocaleSettings(AWSProperty):
    """
    `BotAliasLocaleSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-botalias-botaliaslocalesettings.html>`__
    """

    props: PropsDictType = {
        "CodeHookSpecification": (CodeHookSpecification, False),
        "Enabled": (boolean, True),
    }


class BotAliasLocaleSettingsItem(AWSProperty):
    """
    `BotAliasLocaleSettingsItem <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-botalias-botaliaslocalesettingsitem.html>`__
    """

    props: PropsDictType = {
        "BotAliasLocaleSetting": (BotAliasLocaleSettings, True),
        "LocaleId": (str, True),
    }


class S3BucketLogDestination(AWSProperty):
    """
    `S3BucketLogDestination <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-botalias-s3bucketlogdestination.html>`__
    """

    props: PropsDictType = {
        "KmsKeyArn": (str, False),
        "LogPrefix": (str, True),
        "S3BucketArn": (str, True),
    }


class AudioLogDestination(AWSProperty):
    """
    `AudioLogDestination <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-botalias-audiologdestination.html>`__
    """

    props: PropsDictType = {
        "S3Bucket": (S3BucketLogDestination, True),
    }


class AudioLogSetting(AWSProperty):
    """
    `AudioLogSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-botalias-audiologsetting.html>`__
    """

    props: PropsDictType = {
        "Destination": (AudioLogDestination, True),
        "Enabled": (boolean, True),
    }


class CloudWatchLogGroupLogDestination(AWSProperty):
    """
    `CloudWatchLogGroupLogDestination <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-botalias-cloudwatchloggrouplogdestination.html>`__
    """

    props: PropsDictType = {
        "CloudWatchLogGroupArn": (str, True),
        "LogPrefix": (str, True),
    }


class TextLogDestination(AWSProperty):
    """
    `TextLogDestination <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-botalias-textlogdestination.html>`__
    """

    props: PropsDictType = {
        "CloudWatch": (CloudWatchLogGroupLogDestination, True),
    }


class TextLogSetting(AWSProperty):
    """
    `TextLogSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-botalias-textlogsetting.html>`__
    """

    props: PropsDictType = {
        "Destination": (TextLogDestination, True),
        "Enabled": (boolean, True),
    }


class ConversationLogSettings(AWSProperty):
    """
    `ConversationLogSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-botalias-conversationlogsettings.html>`__
    """

    props: PropsDictType = {
        "AudioLogSettings": ([AudioLogSetting], False),
        "TextLogSettings": ([TextLogSetting], False),
    }


class SentimentAnalysisSettings(AWSProperty):
    """
    `SentimentAnalysisSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-botalias-sentimentanalysissettings.html>`__
    """

    props: PropsDictType = {
        "DetectSentiment": (boolean, True),
    }


class TestBotAliasSettings(AWSProperty):
    """
    `TestBotAliasSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-bot-testbotaliassettings.html>`__
    """

    props: PropsDictType = {
        "BotAliasLocaleSettings": ([BotAliasLocaleSettingsItem], False),
        "ConversationLogSettings": (ConversationLogSettings, False),
        "Description": (str, False),
        "SentimentAnalysisSettings": (SentimentAnalysisSettings, False),
    }


class Bot(AWSObject):
    """
    `Bot <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-lex-bot.html>`__
    """

    resource_type = "AWS::Lex::Bot"

    props: PropsDictType = {
        "AutoBuildBotLocales": (boolean, False),
        "BotFileS3Location": (S3Location, False),
        "BotLocales": ([BotLocale], False),
        "BotTags": (Tags, False),
        "DataPrivacy": (DataPrivacy, True),
        "Description": (str, False),
        "IdleSessionTTLInSeconds": (integer, True),
        "Name": (str, True),
        "Replication": (Replication, False),
        "RoleArn": (str, True),
        "TestBotAliasSettings": (TestBotAliasSettings, False),
        "TestBotAliasTags": (Tags, False),
    }


class BotAlias(AWSObject):
    """
    `BotAlias <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-lex-botalias.html>`__
    """

    resource_type = "AWS::Lex::BotAlias"

    props: PropsDictType = {
        "BotAliasLocaleSettings": ([BotAliasLocaleSettingsItem], False),
        "BotAliasName": (str, True),
        "BotAliasTags": (Tags, False),
        "BotId": (str, True),
        "BotVersion": (str, False),
        "ConversationLogSettings": (ConversationLogSettings, False),
        "Description": (str, False),
        "SentimentAnalysisSettings": (SentimentAnalysisSettings, False),
    }


class BotVersionLocaleDetails(AWSProperty):
    """
    `BotVersionLocaleDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-botversion-botversionlocaledetails.html>`__
    """

    props: PropsDictType = {
        "SourceBotVersion": (str, True),
    }


class BotVersionLocaleSpecification(AWSProperty):
    """
    `BotVersionLocaleSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-lex-botversion-botversionlocalespecification.html>`__
    """

    props: PropsDictType = {
        "BotVersionLocaleDetails": (BotVersionLocaleDetails, True),
        "LocaleId": (str, True),
    }


class BotVersion(AWSObject):
    """
    `BotVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-lex-botversion.html>`__
    """

    resource_type = "AWS::Lex::BotVersion"

    props: PropsDictType = {
        "BotId": (str, True),
        "BotVersionLocaleSpecification": ([BotVersionLocaleSpecification], True),
        "Description": (str, False),
    }


class ResourcePolicy(AWSObject):
    """
    `ResourcePolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-lex-resourcepolicy.html>`__
    """

    resource_type = "AWS::Lex::ResourcePolicy"

    props: PropsDictType = {
        "Policy": (policytypes, True),
        "ResourceArn": (str, True),
    }
