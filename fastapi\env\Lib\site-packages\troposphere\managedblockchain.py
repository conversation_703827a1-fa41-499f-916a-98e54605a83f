# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import integer


class Accessor(AWSObject):
    """
    `Accessor <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-managedblockchain-accessor.html>`__
    """

    resource_type = "AWS::ManagedBlockchain::Accessor"

    props: PropsDictType = {
        "AccessorType": (str, True),
        "NetworkType": (str, False),
        "Tags": (Tags, False),
    }


class MemberFabricConfiguration(AWSProperty):
    """
    `MemberFabricConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-managedblockchain-member-memberfabricconfiguration.html>`__
    """

    props: PropsDictType = {
        "AdminPassword": (str, True),
        "AdminUsername": (str, True),
    }


class MemberFrameworkConfiguration(AWSProperty):
    """
    `MemberFrameworkConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-managedblockchain-member-memberframeworkconfiguration.html>`__
    """

    props: PropsDictType = {
        "MemberFabricConfiguration": (MemberFabricConfiguration, False),
    }


class MemberConfiguration(AWSProperty):
    """
    `MemberConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-managedblockchain-member-memberconfiguration.html>`__
    """

    props: PropsDictType = {
        "Description": (str, False),
        "MemberFrameworkConfiguration": (MemberFrameworkConfiguration, False),
        "Name": (str, True),
    }


class NetworkFabricConfiguration(AWSProperty):
    """
    `NetworkFabricConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-managedblockchain-member-networkfabricconfiguration.html>`__
    """

    props: PropsDictType = {
        "Edition": (str, True),
    }


class NetworkFrameworkConfiguration(AWSProperty):
    """
    `NetworkFrameworkConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-managedblockchain-member-networkframeworkconfiguration.html>`__
    """

    props: PropsDictType = {
        "NetworkFabricConfiguration": (NetworkFabricConfiguration, False),
    }


class ApprovalThresholdPolicy(AWSProperty):
    """
    `ApprovalThresholdPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-managedblockchain-member-approvalthresholdpolicy.html>`__
    """

    props: PropsDictType = {
        "ProposalDurationInHours": (integer, False),
        "ThresholdComparator": (str, False),
        "ThresholdPercentage": (integer, False),
    }


class VotingPolicy(AWSProperty):
    """
    `VotingPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-managedblockchain-member-votingpolicy.html>`__
    """

    props: PropsDictType = {
        "ApprovalThresholdPolicy": (ApprovalThresholdPolicy, False),
    }


class NetworkConfiguration(AWSProperty):
    """
    `NetworkConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-managedblockchain-member-networkconfiguration.html>`__
    """

    props: PropsDictType = {
        "Description": (str, False),
        "Framework": (str, True),
        "FrameworkVersion": (str, True),
        "Name": (str, True),
        "NetworkFrameworkConfiguration": (NetworkFrameworkConfiguration, False),
        "VotingPolicy": (VotingPolicy, True),
    }


class Member(AWSObject):
    """
    `Member <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-managedblockchain-member.html>`__
    """

    resource_type = "AWS::ManagedBlockchain::Member"

    props: PropsDictType = {
        "InvitationId": (str, False),
        "MemberConfiguration": (MemberConfiguration, True),
        "NetworkConfiguration": (NetworkConfiguration, False),
        "NetworkId": (str, False),
    }


class NodeConfiguration(AWSProperty):
    """
    `NodeConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-managedblockchain-node-nodeconfiguration.html>`__
    """

    props: PropsDictType = {
        "AvailabilityZone": (str, True),
        "InstanceType": (str, True),
    }


class Node(AWSObject):
    """
    `Node <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-managedblockchain-node.html>`__
    """

    resource_type = "AWS::ManagedBlockchain::Node"

    props: PropsDictType = {
        "MemberId": (str, False),
        "NetworkId": (str, True),
        "NodeConfiguration": (NodeConfiguration, True),
    }
