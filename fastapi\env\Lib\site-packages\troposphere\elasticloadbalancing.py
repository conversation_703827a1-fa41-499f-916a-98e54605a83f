# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType
from .validators import boolean, integer
from .validators.elasticloadbalancing import (
    validate_elb_name,
    validate_int_to_str,
    validate_network_port,
    validate_tags_or_list,
    validate_threshold,
)


class AccessLoggingPolicy(AWSProperty):
    """
    `AccessLoggingPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-elb-accessloggingpolicy.html>`__
    """

    props: PropsDictType = {
        "EmitInterval": (integer, False),
        "Enabled": (boolean, True),
        "S3BucketName": (str, True),
        "S3BucketPrefix": (str, False),
    }


class ConnectionDrainingPolicy(AWSProperty):
    """
    `ConnectionDrainingPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-elb-connectiondrainingpolicy.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, True),
        "Timeout": (integer, False),
    }


class ConnectionSettings(AWSProperty):
    """
    `ConnectionSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-elb-connectionsettings.html>`__
    """

    props: PropsDictType = {
        "IdleTimeout": (integer, True),
    }


class HealthCheck(AWSProperty):
    """
    `HealthCheck <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-elb-health-check.html>`__
    """

    props: PropsDictType = {
        "HealthyThreshold": (validate_threshold, True),
        "Interval": (validate_int_to_str, True),
        "Target": (str, True),
        "Timeout": (validate_int_to_str, True),
        "UnhealthyThreshold": (validate_threshold, True),
    }


class LoadBalancer(AWSObject):
    """
    `LoadBalancer <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-elb.html>`__
    """

    resource_type = "AWS::ElasticLoadBalancing::LoadBalancer"

    props: PropsDictType = {
        "AccessLoggingPolicy": (AccessLoggingPolicy, False),
        "AppCookieStickinessPolicy": (list, False),
        "AvailabilityZones": (list, False),
        "ConnectionDrainingPolicy": (ConnectionDrainingPolicy, False),
        "ConnectionSettings": (ConnectionSettings, False),
        "CrossZone": (boolean, False),
        "HealthCheck": (HealthCheck, False),
        "Instances": ([str], False),
        "LBCookieStickinessPolicy": (list, False),
        "Listeners": (list, True),
        "LoadBalancerName": (validate_elb_name, False),
        "Policies": (list, False),
        "Scheme": (str, False),
        "SecurityGroups": ([str], False),
        "Subnets": ([str], False),
        "Tags": (validate_tags_or_list, False),
    }


class AppCookieStickinessPolicy(AWSProperty):
    """
    `AppCookieStickinessPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-elb-AppCookieStickinessPolicy.html>`__
    """

    props: PropsDictType = {
        "CookieName": (str, True),
        "PolicyName": (str, True),
    }


class LBCookieStickinessPolicy(AWSProperty):
    """
    `LBCookieStickinessPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-elb-LBCookieStickinessPolicy.html>`__
    """

    props: PropsDictType = {
        "CookieExpirationPeriod": (str, False),
        "PolicyName": (str, False),
    }


class Listener(AWSProperty):
    """
    `Listener <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-elb-listener.html>`__
    """

    props: PropsDictType = {
        "InstancePort": (validate_network_port, True),
        "InstanceProtocol": (str, False),
        "LoadBalancerPort": (validate_network_port, True),
        "PolicyNames": ([str], False),
        "Protocol": (str, True),
        "SSLCertificateId": (str, False),
    }


class Policy(AWSProperty):
    """
    `Policy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ec2-elb-policy.html>`__
    """

    props: PropsDictType = {
        "Attributes": ([dict], True),
        "InstancePorts": ([str], False),
        "LoadBalancerPorts": ([str], False),
        "PolicyName": (str, True),
        "PolicyType": (str, True),
    }
