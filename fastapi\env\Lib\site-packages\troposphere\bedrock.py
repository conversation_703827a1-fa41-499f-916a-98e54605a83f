# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer


class S3Identifier(AWSProperty):
    """
    `S3Identifier <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agent-s3identifier.html>`__
    """

    props: PropsDictType = {
        "S3BucketName": (str, False),
        "S3ObjectKey": (str, False),
    }


class APISchema(AWSProperty):
    """
    `APISchema <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agent-apischema.html>`__
    """

    props: PropsDictType = {
        "Payload": (str, False),
        "S3": (S3Identifier, False),
    }


class ActionGroupExecutor(AWSProperty):
    """
    `ActionGroupExecutor <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agent-actiongroupexecutor.html>`__
    """

    props: PropsDictType = {
        "CustomControl": (str, False),
        "Lambda": (str, False),
    }


class ParameterDetail(AWSProperty):
    """
    `ParameterDetail <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agent-parameterdetail.html>`__
    """

    props: PropsDictType = {
        "Description": (str, False),
        "Required": (boolean, False),
        "Type": (str, True),
    }


class Function(AWSProperty):
    """
    `Function <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agent-function.html>`__
    """

    props: PropsDictType = {
        "Description": (str, False),
        "Name": (str, True),
        "Parameters": (dict, False),
        "RequireConfirmation": (str, False),
    }


class FunctionSchema(AWSProperty):
    """
    `FunctionSchema <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agent-functionschema.html>`__
    """

    props: PropsDictType = {
        "Functions": ([Function], True),
    }


class AgentActionGroup(AWSProperty):
    """
    `AgentActionGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agent-agentactiongroup.html>`__
    """

    props: PropsDictType = {
        "ActionGroupExecutor": (ActionGroupExecutor, False),
        "ActionGroupName": (str, True),
        "ActionGroupState": (str, False),
        "ApiSchema": (APISchema, False),
        "Description": (str, False),
        "FunctionSchema": (FunctionSchema, False),
        "ParentActionGroupSignature": (str, False),
        "SkipResourceInUseCheckOnDelete": (boolean, False),
    }


class AgentDescriptor(AWSProperty):
    """
    `AgentDescriptor <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agent-agentdescriptor.html>`__
    """

    props: PropsDictType = {
        "AliasArn": (str, False),
    }


class AgentCollaborator(AWSProperty):
    """
    `AgentCollaborator <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agent-agentcollaborator.html>`__
    """

    props: PropsDictType = {
        "AgentDescriptor": (AgentDescriptor, True),
        "CollaborationInstruction": (str, True),
        "CollaboratorName": (str, True),
        "RelayConversationHistory": (str, False),
    }


class AgentKnowledgeBase(AWSProperty):
    """
    `AgentKnowledgeBase <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agent-agentknowledgebase.html>`__
    """

    props: PropsDictType = {
        "Description": (str, True),
        "KnowledgeBaseId": (str, True),
        "KnowledgeBaseState": (str, False),
    }


class OrchestrationExecutor(AWSProperty):
    """
    `OrchestrationExecutor <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agent-orchestrationexecutor.html>`__
    """

    props: PropsDictType = {
        "Lambda": (str, True),
    }


class CustomOrchestration(AWSProperty):
    """
    `CustomOrchestration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agent-customorchestration.html>`__
    """

    props: PropsDictType = {
        "Executor": (OrchestrationExecutor, False),
    }


class GuardrailConfiguration(AWSProperty):
    """
    `GuardrailConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-guardrailconfiguration.html>`__
    """

    props: PropsDictType = {
        "GuardrailIdentifier": (str, False),
        "GuardrailVersion": (str, False),
    }


class SessionSummaryConfiguration(AWSProperty):
    """
    `SessionSummaryConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agent-sessionsummaryconfiguration.html>`__
    """

    props: PropsDictType = {
        "MaxRecentSessions": (double, False),
    }


class MemoryConfiguration(AWSProperty):
    """
    `MemoryConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agent-memoryconfiguration.html>`__
    """

    props: PropsDictType = {
        "EnabledMemoryTypes": ([str], False),
        "SessionSummaryConfiguration": (SessionSummaryConfiguration, False),
        "StorageDays": (double, False),
    }


class InferenceConfiguration(AWSProperty):
    """
    `InferenceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agent-inferenceconfiguration.html>`__
    """

    props: PropsDictType = {
        "MaximumLength": (double, False),
        "StopSequences": ([str], False),
        "Temperature": (double, False),
        "TopK": (double, False),
        "TopP": (double, False),
    }


class PromptConfiguration(AWSProperty):
    """
    `PromptConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agent-promptconfiguration.html>`__
    """

    props: PropsDictType = {
        "AdditionalModelRequestFields": (dict, False),
        "BasePromptTemplate": (str, False),
        "FoundationModel": (str, False),
        "InferenceConfiguration": (InferenceConfiguration, False),
        "ParserMode": (str, False),
        "PromptCreationMode": (str, False),
        "PromptState": (str, False),
        "PromptType": (str, False),
    }


class PromptOverrideConfiguration(AWSProperty):
    """
    `PromptOverrideConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agent-promptoverrideconfiguration.html>`__
    """

    props: PropsDictType = {
        "OverrideLambda": (str, False),
        "PromptConfigurations": ([PromptConfiguration], True),
    }


class Agent(AWSObject):
    """
    `Agent <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-bedrock-agent.html>`__
    """

    resource_type = "AWS::Bedrock::Agent"

    props: PropsDictType = {
        "ActionGroups": ([AgentActionGroup], False),
        "AgentCollaboration": (str, False),
        "AgentCollaborators": ([AgentCollaborator], False),
        "AgentName": (str, True),
        "AgentResourceRoleArn": (str, False),
        "AutoPrepare": (boolean, False),
        "CustomOrchestration": (CustomOrchestration, False),
        "CustomerEncryptionKeyArn": (str, False),
        "Description": (str, False),
        "FoundationModel": (str, False),
        "GuardrailConfiguration": (GuardrailConfiguration, False),
        "IdleSessionTTLInSeconds": (double, False),
        "Instruction": (str, False),
        "KnowledgeBases": ([AgentKnowledgeBase], False),
        "MemoryConfiguration": (MemoryConfiguration, False),
        "OrchestrationType": (str, False),
        "PromptOverrideConfiguration": (PromptOverrideConfiguration, False),
        "SkipResourceInUseCheckOnDelete": (boolean, False),
        "Tags": (dict, False),
        "TestAliasTags": (dict, False),
    }


class AgentAliasRoutingConfigurationListItem(AWSProperty):
    """
    `AgentAliasRoutingConfigurationListItem <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agentalias-agentaliasroutingconfigurationlistitem.html>`__
    """

    props: PropsDictType = {
        "AgentVersion": (str, True),
    }


class AgentAlias(AWSObject):
    """
    `AgentAlias <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-bedrock-agentalias.html>`__
    """

    resource_type = "AWS::Bedrock::AgentAlias"

    props: PropsDictType = {
        "AgentAliasName": (str, True),
        "AgentId": (str, True),
        "Description": (str, False),
        "RoutingConfiguration": ([AgentAliasRoutingConfigurationListItem], False),
        "Tags": (dict, False),
    }


class InferenceProfileModelSource(AWSProperty):
    """
    `InferenceProfileModelSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-applicationinferenceprofile-inferenceprofilemodelsource.html>`__
    """

    props: PropsDictType = {
        "CopyFrom": (str, True),
    }


class ApplicationInferenceProfile(AWSObject):
    """
    `ApplicationInferenceProfile <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-bedrock-applicationinferenceprofile.html>`__
    """

    resource_type = "AWS::Bedrock::ApplicationInferenceProfile"

    props: PropsDictType = {
        "Description": (str, False),
        "InferenceProfileName": (str, True),
        "ModelSource": (InferenceProfileModelSource, False),
        "Tags": (Tags, False),
    }


class Blueprint(AWSObject):
    """
    `Blueprint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-bedrock-blueprint.html>`__
    """

    resource_type = "AWS::Bedrock::Blueprint"

    props: PropsDictType = {
        "BlueprintName": (str, True),
        "KmsEncryptionContext": (dict, False),
        "KmsKeyId": (str, False),
        "Schema": (dict, True),
        "Tags": (Tags, False),
        "Type": (str, True),
    }


class BlueprintItem(AWSProperty):
    """
    `BlueprintItem <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-blueprintitem.html>`__
    """

    props: PropsDictType = {
        "BlueprintArn": (str, True),
        "BlueprintStage": (str, False),
        "BlueprintVersion": (str, False),
    }


class CustomOutputConfiguration(AWSProperty):
    """
    `CustomOutputConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-customoutputconfiguration.html>`__
    """

    props: PropsDictType = {
        "Blueprints": ([BlueprintItem], False),
    }


class SplitterConfiguration(AWSProperty):
    """
    `SplitterConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-splitterconfiguration.html>`__
    """

    props: PropsDictType = {
        "State": (str, False),
    }


class DocumentOverrideConfiguration(AWSProperty):
    """
    `DocumentOverrideConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-documentoverrideconfiguration.html>`__
    """

    props: PropsDictType = {
        "Splitter": (SplitterConfiguration, False),
    }


class OverrideConfiguration(AWSProperty):
    """
    `OverrideConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-overrideconfiguration.html>`__
    """

    props: PropsDictType = {
        "Document": (DocumentOverrideConfiguration, False),
    }


class AudioExtractionCategory(AWSProperty):
    """
    `AudioExtractionCategory <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-audioextractioncategory.html>`__
    """

    props: PropsDictType = {
        "State": (str, True),
        "Types": ([str], False),
    }


class AudioStandardExtraction(AWSProperty):
    """
    `AudioStandardExtraction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-audiostandardextraction.html>`__
    """

    props: PropsDictType = {
        "Category": (AudioExtractionCategory, True),
    }


class AudioStandardGenerativeField(AWSProperty):
    """
    `AudioStandardGenerativeField <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-audiostandardgenerativefield.html>`__
    """

    props: PropsDictType = {
        "State": (str, True),
        "Types": ([str], False),
    }


class AudioStandardOutputConfiguration(AWSProperty):
    """
    `AudioStandardOutputConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-audiostandardoutputconfiguration.html>`__
    """

    props: PropsDictType = {
        "Extraction": (AudioStandardExtraction, False),
        "GenerativeField": (AudioStandardGenerativeField, False),
    }


class DocumentOutputAdditionalFileFormat(AWSProperty):
    """
    `DocumentOutputAdditionalFileFormat <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-documentoutputadditionalfileformat.html>`__
    """

    props: PropsDictType = {
        "State": (str, True),
    }


class DocumentOutputTextFormat(AWSProperty):
    """
    `DocumentOutputTextFormat <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-documentoutputtextformat.html>`__
    """

    props: PropsDictType = {
        "Types": ([str], False),
    }


class DocumentOutputFormat(AWSProperty):
    """
    `DocumentOutputFormat <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-documentoutputformat.html>`__
    """

    props: PropsDictType = {
        "AdditionalFileFormat": (DocumentOutputAdditionalFileFormat, True),
        "TextFormat": (DocumentOutputTextFormat, True),
    }


class DocumentBoundingBox(AWSProperty):
    """
    `DocumentBoundingBox <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-documentboundingbox.html>`__
    """

    props: PropsDictType = {
        "State": (str, True),
    }


class DocumentExtractionGranularity(AWSProperty):
    """
    `DocumentExtractionGranularity <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-documentextractiongranularity.html>`__
    """

    props: PropsDictType = {
        "Types": ([str], False),
    }


class DocumentStandardExtraction(AWSProperty):
    """
    `DocumentStandardExtraction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-documentstandardextraction.html>`__
    """

    props: PropsDictType = {
        "BoundingBox": (DocumentBoundingBox, True),
        "Granularity": (DocumentExtractionGranularity, True),
    }


class DocumentStandardGenerativeField(AWSProperty):
    """
    `DocumentStandardGenerativeField <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-documentstandardgenerativefield.html>`__
    """

    props: PropsDictType = {
        "State": (str, True),
    }


class DocumentStandardOutputConfiguration(AWSProperty):
    """
    `DocumentStandardOutputConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-documentstandardoutputconfiguration.html>`__
    """

    props: PropsDictType = {
        "Extraction": (DocumentStandardExtraction, False),
        "GenerativeField": (DocumentStandardGenerativeField, False),
        "OutputFormat": (DocumentOutputFormat, False),
    }


class ImageBoundingBox(AWSProperty):
    """
    `ImageBoundingBox <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-imageboundingbox.html>`__
    """

    props: PropsDictType = {
        "State": (str, True),
    }


class ImageExtractionCategory(AWSProperty):
    """
    `ImageExtractionCategory <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-imageextractioncategory.html>`__
    """

    props: PropsDictType = {
        "State": (str, True),
        "Types": ([str], False),
    }


class ImageStandardExtraction(AWSProperty):
    """
    `ImageStandardExtraction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-imagestandardextraction.html>`__
    """

    props: PropsDictType = {
        "BoundingBox": (ImageBoundingBox, True),
        "Category": (ImageExtractionCategory, True),
    }


class ImageStandardGenerativeField(AWSProperty):
    """
    `ImageStandardGenerativeField <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-imagestandardgenerativefield.html>`__
    """

    props: PropsDictType = {
        "State": (str, True),
        "Types": ([str], False),
    }


class ImageStandardOutputConfiguration(AWSProperty):
    """
    `ImageStandardOutputConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-imagestandardoutputconfiguration.html>`__
    """

    props: PropsDictType = {
        "Extraction": (ImageStandardExtraction, False),
        "GenerativeField": (ImageStandardGenerativeField, False),
    }


class VideoBoundingBox(AWSProperty):
    """
    `VideoBoundingBox <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-videoboundingbox.html>`__
    """

    props: PropsDictType = {
        "State": (str, True),
    }


class VideoExtractionCategory(AWSProperty):
    """
    `VideoExtractionCategory <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-videoextractioncategory.html>`__
    """

    props: PropsDictType = {
        "State": (str, True),
        "Types": ([str], False),
    }


class VideoStandardExtraction(AWSProperty):
    """
    `VideoStandardExtraction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-videostandardextraction.html>`__
    """

    props: PropsDictType = {
        "BoundingBox": (VideoBoundingBox, True),
        "Category": (VideoExtractionCategory, True),
    }


class VideoStandardGenerativeField(AWSProperty):
    """
    `VideoStandardGenerativeField <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-videostandardgenerativefield.html>`__
    """

    props: PropsDictType = {
        "State": (str, True),
        "Types": ([str], False),
    }


class VideoStandardOutputConfiguration(AWSProperty):
    """
    `VideoStandardOutputConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-videostandardoutputconfiguration.html>`__
    """

    props: PropsDictType = {
        "Extraction": (VideoStandardExtraction, False),
        "GenerativeField": (VideoStandardGenerativeField, False),
    }


class StandardOutputConfiguration(AWSProperty):
    """
    `StandardOutputConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-dataautomationproject-standardoutputconfiguration.html>`__
    """

    props: PropsDictType = {
        "Audio": (AudioStandardOutputConfiguration, False),
        "Document": (DocumentStandardOutputConfiguration, False),
        "Image": (ImageStandardOutputConfiguration, False),
        "Video": (VideoStandardOutputConfiguration, False),
    }


class DataAutomationProject(AWSObject):
    """
    `DataAutomationProject <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-bedrock-dataautomationproject.html>`__
    """

    resource_type = "AWS::Bedrock::DataAutomationProject"

    props: PropsDictType = {
        "CustomOutputConfiguration": (CustomOutputConfiguration, False),
        "KmsEncryptionContext": (dict, False),
        "KmsKeyId": (str, False),
        "OverrideConfiguration": (OverrideConfiguration, False),
        "ProjectDescription": (str, False),
        "ProjectName": (str, True),
        "StandardOutputConfiguration": (StandardOutputConfiguration, False),
        "Tags": (Tags, False),
    }


class PatternObjectFilter(AWSProperty):
    """
    `PatternObjectFilter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-patternobjectfilter.html>`__
    """

    props: PropsDictType = {
        "ExclusionFilters": ([str], False),
        "InclusionFilters": ([str], False),
        "ObjectType": (str, True),
    }


class PatternObjectFilterConfiguration(AWSProperty):
    """
    `PatternObjectFilterConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-patternobjectfilterconfiguration.html>`__
    """

    props: PropsDictType = {
        "Filters": ([PatternObjectFilter], True),
    }


class CrawlFilterConfiguration(AWSProperty):
    """
    `CrawlFilterConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-crawlfilterconfiguration.html>`__
    """

    props: PropsDictType = {
        "PatternObjectFilter": (PatternObjectFilterConfiguration, False),
        "Type": (str, True),
    }


class ConfluenceCrawlerConfiguration(AWSProperty):
    """
    `ConfluenceCrawlerConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-confluencecrawlerconfiguration.html>`__
    """

    props: PropsDictType = {
        "FilterConfiguration": (CrawlFilterConfiguration, False),
    }


class ConfluenceSourceConfiguration(AWSProperty):
    """
    `ConfluenceSourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-confluencesourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "AuthType": (str, True),
        "CredentialsSecretArn": (str, True),
        "HostType": (str, True),
        "HostUrl": (str, True),
    }


class ConfluenceDataSourceConfiguration(AWSProperty):
    """
    `ConfluenceDataSourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-confluencedatasourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "CrawlerConfiguration": (ConfluenceCrawlerConfiguration, False),
        "SourceConfiguration": (ConfluenceSourceConfiguration, True),
    }


class S3DataSourceConfiguration(AWSProperty):
    """
    `S3DataSourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-s3datasourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "BucketArn": (str, True),
        "BucketOwnerAccountId": (str, False),
        "InclusionPrefixes": ([str], False),
    }


class SalesforceCrawlerConfiguration(AWSProperty):
    """
    `SalesforceCrawlerConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-salesforcecrawlerconfiguration.html>`__
    """

    props: PropsDictType = {
        "FilterConfiguration": (CrawlFilterConfiguration, False),
    }


class SalesforceSourceConfiguration(AWSProperty):
    """
    `SalesforceSourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-salesforcesourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "AuthType": (str, True),
        "CredentialsSecretArn": (str, True),
        "HostUrl": (str, True),
    }


class SalesforceDataSourceConfiguration(AWSProperty):
    """
    `SalesforceDataSourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-salesforcedatasourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "CrawlerConfiguration": (SalesforceCrawlerConfiguration, False),
        "SourceConfiguration": (SalesforceSourceConfiguration, True),
    }


class SharePointCrawlerConfiguration(AWSProperty):
    """
    `SharePointCrawlerConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-sharepointcrawlerconfiguration.html>`__
    """

    props: PropsDictType = {
        "FilterConfiguration": (CrawlFilterConfiguration, False),
    }


class SharePointSourceConfiguration(AWSProperty):
    """
    `SharePointSourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-sharepointsourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "AuthType": (str, True),
        "CredentialsSecretArn": (str, True),
        "Domain": (str, True),
        "HostType": (str, True),
        "SiteUrls": ([str], True),
        "TenantId": (str, False),
    }


class SharePointDataSourceConfiguration(AWSProperty):
    """
    `SharePointDataSourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-sharepointdatasourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "CrawlerConfiguration": (SharePointCrawlerConfiguration, False),
        "SourceConfiguration": (SharePointSourceConfiguration, True),
    }


class WebCrawlerLimits(AWSProperty):
    """
    `WebCrawlerLimits <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-webcrawlerlimits.html>`__
    """

    props: PropsDictType = {
        "MaxPages": (integer, False),
        "RateLimit": (integer, False),
    }


class WebCrawlerConfiguration(AWSProperty):
    """
    `WebCrawlerConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-webcrawlerconfiguration.html>`__
    """

    props: PropsDictType = {
        "CrawlerLimits": (WebCrawlerLimits, False),
        "ExclusionFilters": ([str], False),
        "InclusionFilters": ([str], False),
        "Scope": (str, False),
        "UserAgent": (str, False),
        "UserAgentHeader": (str, False),
    }


class SeedUrl(AWSProperty):
    """
    `SeedUrl <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-seedurl.html>`__
    """

    props: PropsDictType = {
        "Url": (str, True),
    }


class UrlConfiguration(AWSProperty):
    """
    `UrlConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-urlconfiguration.html>`__
    """

    props: PropsDictType = {
        "SeedUrls": ([SeedUrl], True),
    }


class WebSourceConfiguration(AWSProperty):
    """
    `WebSourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-websourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "UrlConfiguration": (UrlConfiguration, True),
    }


class WebDataSourceConfiguration(AWSProperty):
    """
    `WebDataSourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-webdatasourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "CrawlerConfiguration": (WebCrawlerConfiguration, False),
        "SourceConfiguration": (WebSourceConfiguration, True),
    }


class DataSourceConfiguration(AWSProperty):
    """
    `DataSourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-datasourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "ConfluenceConfiguration": (ConfluenceDataSourceConfiguration, False),
        "S3Configuration": (S3DataSourceConfiguration, False),
        "SalesforceConfiguration": (SalesforceDataSourceConfiguration, False),
        "SharePointConfiguration": (SharePointDataSourceConfiguration, False),
        "Type": (str, True),
        "WebConfiguration": (WebDataSourceConfiguration, False),
    }


class ServerSideEncryptionConfiguration(AWSProperty):
    """
    `ServerSideEncryptionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-serversideencryptionconfiguration.html>`__
    """

    props: PropsDictType = {
        "KmsKeyArn": (str, False),
    }


class FixedSizeChunkingConfiguration(AWSProperty):
    """
    `FixedSizeChunkingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-fixedsizechunkingconfiguration.html>`__
    """

    props: PropsDictType = {
        "MaxTokens": (integer, True),
        "OverlapPercentage": (integer, True),
    }


class HierarchicalChunkingLevelConfiguration(AWSProperty):
    """
    `HierarchicalChunkingLevelConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-hierarchicalchunkinglevelconfiguration.html>`__
    """

    props: PropsDictType = {
        "MaxTokens": (integer, True),
    }


class HierarchicalChunkingConfiguration(AWSProperty):
    """
    `HierarchicalChunkingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-hierarchicalchunkingconfiguration.html>`__
    """

    props: PropsDictType = {
        "LevelConfigurations": ([HierarchicalChunkingLevelConfiguration], True),
        "OverlapTokens": (integer, True),
    }


class SemanticChunkingConfiguration(AWSProperty):
    """
    `SemanticChunkingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-semanticchunkingconfiguration.html>`__
    """

    props: PropsDictType = {
        "BreakpointPercentileThreshold": (integer, True),
        "BufferSize": (integer, True),
        "MaxTokens": (integer, True),
    }


class ChunkingConfiguration(AWSProperty):
    """
    `ChunkingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-chunkingconfiguration.html>`__
    """

    props: PropsDictType = {
        "ChunkingStrategy": (str, True),
        "FixedSizeChunkingConfiguration": (FixedSizeChunkingConfiguration, False),
        "HierarchicalChunkingConfiguration": (HierarchicalChunkingConfiguration, False),
        "SemanticChunkingConfiguration": (SemanticChunkingConfiguration, False),
    }


class EnrichmentStrategyConfiguration(AWSProperty):
    """
    `EnrichmentStrategyConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-enrichmentstrategyconfiguration.html>`__
    """

    props: PropsDictType = {
        "Method": (str, True),
    }


class BedrockFoundationModelContextEnrichmentConfiguration(AWSProperty):
    """
    `BedrockFoundationModelContextEnrichmentConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-bedrockfoundationmodelcontextenrichmentconfiguration.html>`__
    """

    props: PropsDictType = {
        "EnrichmentStrategyConfiguration": (EnrichmentStrategyConfiguration, True),
        "ModelArn": (str, True),
    }


class ContextEnrichmentConfiguration(AWSProperty):
    """
    `ContextEnrichmentConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-contextenrichmentconfiguration.html>`__
    """

    props: PropsDictType = {
        "BedrockFoundationModelConfiguration": (
            BedrockFoundationModelContextEnrichmentConfiguration,
            False,
        ),
        "Type": (str, True),
    }


class S3Location(AWSProperty):
    """
    `S3Location <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-s3location.html>`__
    """

    props: PropsDictType = {
        "URI": (str, True),
    }


class IntermediateStorage(AWSProperty):
    """
    `IntermediateStorage <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-intermediatestorage.html>`__
    """

    props: PropsDictType = {
        "S3Location": (S3Location, True),
    }


class TransformationLambdaConfiguration(AWSProperty):
    """
    `TransformationLambdaConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-transformationlambdaconfiguration.html>`__
    """

    props: PropsDictType = {
        "LambdaArn": (str, True),
    }


class TransformationFunction(AWSProperty):
    """
    `TransformationFunction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-transformationfunction.html>`__
    """

    props: PropsDictType = {
        "TransformationLambdaConfiguration": (TransformationLambdaConfiguration, True),
    }


class Transformation(AWSProperty):
    """
    `Transformation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-transformation.html>`__
    """

    props: PropsDictType = {
        "StepToApply": (str, True),
        "TransformationFunction": (TransformationFunction, True),
    }


class CustomTransformationConfiguration(AWSProperty):
    """
    `CustomTransformationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-customtransformationconfiguration.html>`__
    """

    props: PropsDictType = {
        "IntermediateStorage": (IntermediateStorage, True),
        "Transformations": ([Transformation], True),
    }


class BedrockDataAutomationConfiguration(AWSProperty):
    """
    `BedrockDataAutomationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-bedrockdataautomationconfiguration.html>`__
    """

    props: PropsDictType = {
        "ParsingModality": (str, False),
    }


class ParsingPrompt(AWSProperty):
    """
    `ParsingPrompt <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-parsingprompt.html>`__
    """

    props: PropsDictType = {
        "ParsingPromptText": (str, True),
    }


class BedrockFoundationModelConfiguration(AWSProperty):
    """
    `BedrockFoundationModelConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-bedrockfoundationmodelconfiguration.html>`__
    """

    props: PropsDictType = {
        "ModelArn": (str, True),
        "ParsingModality": (str, False),
        "ParsingPrompt": (ParsingPrompt, False),
    }


class ParsingConfiguration(AWSProperty):
    """
    `ParsingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-parsingconfiguration.html>`__
    """

    props: PropsDictType = {
        "BedrockDataAutomationConfiguration": (
            BedrockDataAutomationConfiguration,
            False,
        ),
        "BedrockFoundationModelConfiguration": (
            BedrockFoundationModelConfiguration,
            False,
        ),
        "ParsingStrategy": (str, True),
    }


class VectorIngestionConfiguration(AWSProperty):
    """
    `VectorIngestionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-datasource-vectoringestionconfiguration.html>`__
    """

    props: PropsDictType = {
        "ChunkingConfiguration": (ChunkingConfiguration, False),
        "ContextEnrichmentConfiguration": (ContextEnrichmentConfiguration, False),
        "CustomTransformationConfiguration": (CustomTransformationConfiguration, False),
        "ParsingConfiguration": (ParsingConfiguration, False),
    }


class DataSource(AWSObject):
    """
    `DataSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-bedrock-datasource.html>`__
    """

    resource_type = "AWS::Bedrock::DataSource"

    props: PropsDictType = {
        "DataDeletionPolicy": (str, False),
        "DataSourceConfiguration": (DataSourceConfiguration, True),
        "Description": (str, False),
        "KnowledgeBaseId": (str, True),
        "Name": (str, True),
        "ServerSideEncryptionConfiguration": (ServerSideEncryptionConfiguration, False),
        "VectorIngestionConfiguration": (VectorIngestionConfiguration, False),
    }


class FlowConditionalConnectionConfiguration(AWSProperty):
    """
    `FlowConditionalConnectionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-flowconditionalconnectionconfiguration.html>`__
    """

    props: PropsDictType = {
        "Condition": (str, True),
    }


class FlowDataConnectionConfiguration(AWSProperty):
    """
    `FlowDataConnectionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-flowdataconnectionconfiguration.html>`__
    """

    props: PropsDictType = {
        "SourceOutput": (str, True),
        "TargetInput": (str, True),
    }


class FlowConnectionConfiguration(AWSProperty):
    """
    `FlowConnectionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-flowconnectionconfiguration.html>`__
    """

    props: PropsDictType = {
        "Conditional": (FlowConditionalConnectionConfiguration, False),
        "Data": (FlowDataConnectionConfiguration, False),
    }


class FlowConnection(AWSProperty):
    """
    `FlowConnection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-flowconnection.html>`__
    """

    props: PropsDictType = {
        "Configuration": (FlowConnectionConfiguration, False),
        "Name": (str, True),
        "Source": (str, True),
        "Target": (str, True),
        "Type": (str, True),
    }


class AgentFlowNodeConfiguration(AWSProperty):
    """
    `AgentFlowNodeConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-agentflownodeconfiguration.html>`__
    """

    props: PropsDictType = {
        "AgentAliasArn": (str, True),
    }


class FlowCondition(AWSProperty):
    """
    `FlowCondition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-flowcondition.html>`__
    """

    props: PropsDictType = {
        "Expression": (str, False),
        "Name": (str, True),
    }


class ConditionFlowNodeConfiguration(AWSProperty):
    """
    `ConditionFlowNodeConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-conditionflownodeconfiguration.html>`__
    """

    props: PropsDictType = {
        "Conditions": ([FlowCondition], True),
    }


class KnowledgeBaseFlowNodeConfiguration(AWSProperty):
    """
    `KnowledgeBaseFlowNodeConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-knowledgebaseflownodeconfiguration.html>`__
    """

    props: PropsDictType = {
        "GuardrailConfiguration": (GuardrailConfiguration, False),
        "KnowledgeBaseId": (str, True),
        "ModelId": (str, False),
    }


class LambdaFunctionFlowNodeConfiguration(AWSProperty):
    """
    `LambdaFunctionFlowNodeConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-lambdafunctionflownodeconfiguration.html>`__
    """

    props: PropsDictType = {
        "LambdaArn": (str, True),
    }


class LexFlowNodeConfiguration(AWSProperty):
    """
    `LexFlowNodeConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-lexflownodeconfiguration.html>`__
    """

    props: PropsDictType = {
        "BotAliasArn": (str, True),
        "LocaleId": (str, True),
    }


class PromptModelInferenceConfiguration(AWSProperty):
    """
    `PromptModelInferenceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-promptmodelinferenceconfiguration.html>`__
    """

    props: PropsDictType = {
        "MaxTokens": (double, False),
        "StopSequences": ([str], False),
        "Temperature": (double, False),
        "TopP": (double, False),
    }


class PromptInferenceConfiguration(AWSProperty):
    """
    `PromptInferenceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-promptinferenceconfiguration.html>`__
    """

    props: PropsDictType = {
        "Text": (PromptModelInferenceConfiguration, True),
    }


class CachePointBlock(AWSProperty):
    """
    `CachePointBlock <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-cachepointblock.html>`__
    """

    props: PropsDictType = {
        "Type": (str, True),
    }


class ContentBlock(AWSProperty):
    """
    `ContentBlock <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-contentblock.html>`__
    """

    props: PropsDictType = {
        "CachePoint": (CachePointBlock, False),
        "Text": (str, False),
    }


class Message(AWSProperty):
    """
    `Message <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-message.html>`__
    """

    props: PropsDictType = {
        "Content": ([ContentBlock], True),
        "Role": (str, True),
    }


class PromptInputVariable(AWSProperty):
    """
    `PromptInputVariable <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-promptinputvariable.html>`__
    """

    props: PropsDictType = {
        "Name": (str, False),
    }


class SystemContentBlock(AWSProperty):
    """
    `SystemContentBlock <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-systemcontentblock.html>`__
    """

    props: PropsDictType = {
        "CachePoint": (CachePointBlock, False),
        "Text": (str, False),
    }


class ToolInputSchema(AWSProperty):
    """
    `ToolInputSchema <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-toolinputschema.html>`__
    """

    props: PropsDictType = {
        "Json": (dict, True),
    }


class ToolSpecification(AWSProperty):
    """
    `ToolSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-toolspecification.html>`__
    """

    props: PropsDictType = {
        "Description": (str, False),
        "InputSchema": (ToolInputSchema, True),
        "Name": (str, True),
    }


class Tool(AWSProperty):
    """
    `Tool <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-tool.html>`__
    """

    props: PropsDictType = {
        "CachePoint": (CachePointBlock, False),
        "ToolSpec": (ToolSpecification, False),
    }


class SpecificToolChoice(AWSProperty):
    """
    `SpecificToolChoice <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-specifictoolchoice.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
    }


class ToolChoice(AWSProperty):
    """
    `ToolChoice <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-toolchoice.html>`__
    """

    props: PropsDictType = {
        "Any": (dict, False),
        "Auto": (dict, False),
        "Tool": (SpecificToolChoice, False),
    }


class ToolConfiguration(AWSProperty):
    """
    `ToolConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-toolconfiguration.html>`__
    """

    props: PropsDictType = {
        "ToolChoice": (ToolChoice, False),
        "Tools": ([Tool], True),
    }


class ChatPromptTemplateConfiguration(AWSProperty):
    """
    `ChatPromptTemplateConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-chatprompttemplateconfiguration.html>`__
    """

    props: PropsDictType = {
        "InputVariables": ([PromptInputVariable], False),
        "Messages": ([Message], True),
        "System": ([SystemContentBlock], False),
        "ToolConfiguration": (ToolConfiguration, False),
    }


class TextPromptTemplateConfiguration(AWSProperty):
    """
    `TextPromptTemplateConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-textprompttemplateconfiguration.html>`__
    """

    props: PropsDictType = {
        "CachePoint": (CachePointBlock, False),
        "InputVariables": ([PromptInputVariable], False),
        "Text": (str, True),
    }


class PromptTemplateConfiguration(AWSProperty):
    """
    `PromptTemplateConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-prompt-prompttemplateconfiguration.html>`__
    """

    props: PropsDictType = {
        "Chat": (ChatPromptTemplateConfiguration, False),
        "Text": (TextPromptTemplateConfiguration, False),
    }


class PromptFlowNodeInlineConfiguration(AWSProperty):
    """
    `PromptFlowNodeInlineConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-promptflownodeinlineconfiguration.html>`__
    """

    props: PropsDictType = {
        "InferenceConfiguration": (PromptInferenceConfiguration, False),
        "ModelId": (str, True),
        "TemplateConfiguration": (PromptTemplateConfiguration, True),
        "TemplateType": (str, True),
    }


class PromptFlowNodeResourceConfiguration(AWSProperty):
    """
    `PromptFlowNodeResourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-promptflownoderesourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "PromptArn": (str, True),
    }


class PromptFlowNodeSourceConfiguration(AWSProperty):
    """
    `PromptFlowNodeSourceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-promptflownodesourceconfiguration.html>`__
    """

    props: PropsDictType = {
        "Inline": (PromptFlowNodeInlineConfiguration, False),
        "Resource": (PromptFlowNodeResourceConfiguration, False),
    }


class PromptFlowNodeConfiguration(AWSProperty):
    """
    `PromptFlowNodeConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-promptflownodeconfiguration.html>`__
    """

    props: PropsDictType = {
        "GuardrailConfiguration": (GuardrailConfiguration, False),
        "SourceConfiguration": (PromptFlowNodeSourceConfiguration, True),
    }


class RetrievalFlowNodeS3Configuration(AWSProperty):
    """
    `RetrievalFlowNodeS3Configuration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-retrievalflownodes3configuration.html>`__
    """

    props: PropsDictType = {
        "BucketName": (str, True),
    }


class RetrievalFlowNodeServiceConfiguration(AWSProperty):
    """
    `RetrievalFlowNodeServiceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-retrievalflownodeserviceconfiguration.html>`__
    """

    props: PropsDictType = {
        "S3": (RetrievalFlowNodeS3Configuration, False),
    }


class RetrievalFlowNodeConfiguration(AWSProperty):
    """
    `RetrievalFlowNodeConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-retrievalflownodeconfiguration.html>`__
    """

    props: PropsDictType = {
        "ServiceConfiguration": (RetrievalFlowNodeServiceConfiguration, True),
    }


class StorageFlowNodeS3Configuration(AWSProperty):
    """
    `StorageFlowNodeS3Configuration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-storageflownodes3configuration.html>`__
    """

    props: PropsDictType = {
        "BucketName": (str, True),
    }


class StorageFlowNodeServiceConfiguration(AWSProperty):
    """
    `StorageFlowNodeServiceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-storageflownodeserviceconfiguration.html>`__
    """

    props: PropsDictType = {
        "S3": (StorageFlowNodeS3Configuration, False),
    }


class StorageFlowNodeConfiguration(AWSProperty):
    """
    `StorageFlowNodeConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-storageflownodeconfiguration.html>`__
    """

    props: PropsDictType = {
        "ServiceConfiguration": (StorageFlowNodeServiceConfiguration, True),
    }


class FlowNodeConfiguration(AWSProperty):
    """
    `FlowNodeConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-flownodeconfiguration.html>`__
    """

    props: PropsDictType = {
        "Agent": (AgentFlowNodeConfiguration, False),
        "Collector": (dict, False),
        "Condition": (ConditionFlowNodeConfiguration, False),
        "Input": (dict, False),
        "Iterator": (dict, False),
        "KnowledgeBase": (KnowledgeBaseFlowNodeConfiguration, False),
        "LambdaFunction": (LambdaFunctionFlowNodeConfiguration, False),
        "Lex": (LexFlowNodeConfiguration, False),
        "Output": (dict, False),
        "Prompt": (PromptFlowNodeConfiguration, False),
        "Retrieval": (RetrievalFlowNodeConfiguration, False),
        "Storage": (StorageFlowNodeConfiguration, False),
    }


class FlowNodeInput(AWSProperty):
    """
    `FlowNodeInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-flownodeinput.html>`__
    """

    props: PropsDictType = {
        "Expression": (str, True),
        "Name": (str, True),
        "Type": (str, True),
    }


class FlowNodeOutput(AWSProperty):
    """
    `FlowNodeOutput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-flownodeoutput.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "Type": (str, True),
    }


class FlowNode(AWSProperty):
    """
    `FlowNode <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-flownode.html>`__
    """

    props: PropsDictType = {
        "Configuration": (FlowNodeConfiguration, False),
        "Inputs": ([FlowNodeInput], False),
        "Name": (str, True),
        "Outputs": ([FlowNodeOutput], False),
        "Type": (str, True),
    }


class FlowDefinition(AWSProperty):
    """
    `FlowDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowversion-flowdefinition.html>`__
    """

    props: PropsDictType = {
        "Connections": ([FlowConnection], False),
        "Nodes": ([FlowNode], False),
    }


class Flow(AWSObject):
    """
    `Flow <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-bedrock-flow.html>`__
    """

    resource_type = "AWS::Bedrock::Flow"

    props: PropsDictType = {
        "CustomerEncryptionKeyArn": (str, False),
        "Definition": (FlowDefinition, False),
        "DefinitionS3Location": (S3Location, False),
        "DefinitionString": (str, False),
        "DefinitionSubstitutions": (dict, False),
        "Description": (str, False),
        "ExecutionRoleArn": (str, True),
        "Name": (str, True),
        "Tags": (dict, False),
        "TestAliasTags": (dict, False),
    }


class FlowAliasRoutingConfigurationListItem(AWSProperty):
    """
    `FlowAliasRoutingConfigurationListItem <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flowalias-flowaliasroutingconfigurationlistitem.html>`__
    """

    props: PropsDictType = {
        "FlowVersion": (str, False),
    }


class FlowAlias(AWSObject):
    """
    `FlowAlias <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-bedrock-flowalias.html>`__
    """

    resource_type = "AWS::Bedrock::FlowAlias"

    props: PropsDictType = {
        "Description": (str, False),
        "FlowArn": (str, True),
        "Name": (str, True),
        "RoutingConfiguration": ([FlowAliasRoutingConfigurationListItem], True),
        "Tags": (dict, False),
    }


class FlowVersion(AWSObject):
    """
    `FlowVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-bedrock-flowversion.html>`__
    """

    resource_type = "AWS::Bedrock::FlowVersion"

    props: PropsDictType = {
        "Description": (str, False),
        "FlowArn": (str, True),
    }


class ContentFilterConfig(AWSProperty):
    """
    `ContentFilterConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-guardrail-contentfilterconfig.html>`__
    """

    props: PropsDictType = {
        "InputModalities": ([str], False),
        "InputStrength": (str, True),
        "OutputModalities": ([str], False),
        "OutputStrength": (str, True),
        "Type": (str, True),
    }


class ContentPolicyConfig(AWSProperty):
    """
    `ContentPolicyConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-guardrail-contentpolicyconfig.html>`__
    """

    props: PropsDictType = {
        "FiltersConfig": ([ContentFilterConfig], True),
    }


class ContextualGroundingFilterConfig(AWSProperty):
    """
    `ContextualGroundingFilterConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-guardrail-contextualgroundingfilterconfig.html>`__
    """

    props: PropsDictType = {
        "Threshold": (double, True),
        "Type": (str, True),
    }


class ContextualGroundingPolicyConfig(AWSProperty):
    """
    `ContextualGroundingPolicyConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-guardrail-contextualgroundingpolicyconfig.html>`__
    """

    props: PropsDictType = {
        "FiltersConfig": ([ContextualGroundingFilterConfig], True),
    }


class PiiEntityConfig(AWSProperty):
    """
    `PiiEntityConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-guardrail-piientityconfig.html>`__
    """

    props: PropsDictType = {
        "Action": (str, True),
        "Type": (str, True),
    }


class RegexConfig(AWSProperty):
    """
    `RegexConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-guardrail-regexconfig.html>`__
    """

    props: PropsDictType = {
        "Action": (str, True),
        "Description": (str, False),
        "Name": (str, True),
        "Pattern": (str, True),
    }


class SensitiveInformationPolicyConfig(AWSProperty):
    """
    `SensitiveInformationPolicyConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-guardrail-sensitiveinformationpolicyconfig.html>`__
    """

    props: PropsDictType = {
        "PiiEntitiesConfig": ([PiiEntityConfig], False),
        "RegexesConfig": ([RegexConfig], False),
    }


class TopicConfig(AWSProperty):
    """
    `TopicConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-guardrail-topicconfig.html>`__
    """

    props: PropsDictType = {
        "Definition": (str, True),
        "Examples": ([str], False),
        "Name": (str, True),
        "Type": (str, True),
    }


class TopicPolicyConfig(AWSProperty):
    """
    `TopicPolicyConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-guardrail-topicpolicyconfig.html>`__
    """

    props: PropsDictType = {
        "TopicsConfig": ([TopicConfig], True),
    }


class ManagedWordsConfig(AWSProperty):
    """
    `ManagedWordsConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-guardrail-managedwordsconfig.html>`__
    """

    props: PropsDictType = {
        "Type": (str, True),
    }


class WordConfig(AWSProperty):
    """
    `WordConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-guardrail-wordconfig.html>`__
    """

    props: PropsDictType = {
        "Text": (str, True),
    }


class WordPolicyConfig(AWSProperty):
    """
    `WordPolicyConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-guardrail-wordpolicyconfig.html>`__
    """

    props: PropsDictType = {
        "ManagedWordListsConfig": ([ManagedWordsConfig], False),
        "WordsConfig": ([WordConfig], False),
    }


class Guardrail(AWSObject):
    """
    `Guardrail <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-bedrock-guardrail.html>`__
    """

    resource_type = "AWS::Bedrock::Guardrail"

    props: PropsDictType = {
        "BlockedInputMessaging": (str, True),
        "BlockedOutputsMessaging": (str, True),
        "ContentPolicyConfig": (ContentPolicyConfig, False),
        "ContextualGroundingPolicyConfig": (ContextualGroundingPolicyConfig, False),
        "Description": (str, False),
        "KmsKeyArn": (str, False),
        "Name": (str, True),
        "SensitiveInformationPolicyConfig": (SensitiveInformationPolicyConfig, False),
        "Tags": (Tags, False),
        "TopicPolicyConfig": (TopicPolicyConfig, False),
        "WordPolicyConfig": (WordPolicyConfig, False),
    }


class GuardrailVersion(AWSObject):
    """
    `GuardrailVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-bedrock-guardrailversion.html>`__
    """

    resource_type = "AWS::Bedrock::GuardrailVersion"

    props: PropsDictType = {
        "Description": (str, False),
        "GuardrailIdentifier": (str, True),
    }


class KendraKnowledgeBaseConfiguration(AWSProperty):
    """
    `KendraKnowledgeBaseConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-kendraknowledgebaseconfiguration.html>`__
    """

    props: PropsDictType = {
        "KendraIndexArn": (str, True),
    }


class CuratedQuery(AWSProperty):
    """
    `CuratedQuery <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-curatedquery.html>`__
    """

    props: PropsDictType = {
        "NaturalLanguage": (str, True),
        "Sql": (str, True),
    }


class QueryGenerationColumn(AWSProperty):
    """
    `QueryGenerationColumn <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-querygenerationcolumn.html>`__
    """

    props: PropsDictType = {
        "Description": (str, False),
        "Inclusion": (str, False),
        "Name": (str, False),
    }


class QueryGenerationTable(AWSProperty):
    """
    `QueryGenerationTable <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-querygenerationtable.html>`__
    """

    props: PropsDictType = {
        "Columns": ([QueryGenerationColumn], False),
        "Description": (str, False),
        "Inclusion": (str, False),
        "Name": (str, True),
    }


class QueryGenerationContext(AWSProperty):
    """
    `QueryGenerationContext <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-querygenerationcontext.html>`__
    """

    props: PropsDictType = {
        "CuratedQueries": ([CuratedQuery], False),
        "Tables": ([QueryGenerationTable], False),
    }


class QueryGenerationConfiguration(AWSProperty):
    """
    `QueryGenerationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-querygenerationconfiguration.html>`__
    """

    props: PropsDictType = {
        "ExecutionTimeoutSeconds": (integer, False),
        "GenerationContext": (QueryGenerationContext, False),
    }


class RedshiftProvisionedAuthConfiguration(AWSProperty):
    """
    `RedshiftProvisionedAuthConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-redshiftprovisionedauthconfiguration.html>`__
    """

    props: PropsDictType = {
        "DatabaseUser": (str, False),
        "Type": (str, True),
        "UsernamePasswordSecretArn": (str, False),
    }


class RedshiftProvisionedConfiguration(AWSProperty):
    """
    `RedshiftProvisionedConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-redshiftprovisionedconfiguration.html>`__
    """

    props: PropsDictType = {
        "AuthConfiguration": (RedshiftProvisionedAuthConfiguration, True),
        "ClusterIdentifier": (str, True),
    }


class RedshiftServerlessAuthConfiguration(AWSProperty):
    """
    `RedshiftServerlessAuthConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-redshiftserverlessauthconfiguration.html>`__
    """

    props: PropsDictType = {
        "Type": (str, True),
        "UsernamePasswordSecretArn": (str, False),
    }


class RedshiftServerlessConfiguration(AWSProperty):
    """
    `RedshiftServerlessConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-redshiftserverlessconfiguration.html>`__
    """

    props: PropsDictType = {
        "AuthConfiguration": (RedshiftServerlessAuthConfiguration, True),
        "WorkgroupArn": (str, True),
    }


class RedshiftQueryEngineConfiguration(AWSProperty):
    """
    `RedshiftQueryEngineConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-redshiftqueryengineconfiguration.html>`__
    """

    props: PropsDictType = {
        "ProvisionedConfiguration": (RedshiftProvisionedConfiguration, False),
        "ServerlessConfiguration": (RedshiftServerlessConfiguration, False),
        "Type": (str, True),
    }


class RedshiftQueryEngineAwsDataCatalogStorageConfiguration(AWSProperty):
    """
    `RedshiftQueryEngineAwsDataCatalogStorageConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-redshiftqueryengineawsdatacatalogstorageconfiguration.html>`__
    """

    props: PropsDictType = {
        "TableNames": ([str], True),
    }


class RedshiftQueryEngineRedshiftStorageConfiguration(AWSProperty):
    """
    `RedshiftQueryEngineRedshiftStorageConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-redshiftqueryengineredshiftstorageconfiguration.html>`__
    """

    props: PropsDictType = {
        "DatabaseName": (str, True),
    }


class RedshiftQueryEngineStorageConfiguration(AWSProperty):
    """
    `RedshiftQueryEngineStorageConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-redshiftqueryenginestorageconfiguration.html>`__
    """

    props: PropsDictType = {
        "AwsDataCatalogConfiguration": (
            RedshiftQueryEngineAwsDataCatalogStorageConfiguration,
            False,
        ),
        "RedshiftConfiguration": (
            RedshiftQueryEngineRedshiftStorageConfiguration,
            False,
        ),
        "Type": (str, True),
    }


class RedshiftConfiguration(AWSProperty):
    """
    `RedshiftConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-redshiftconfiguration.html>`__
    """

    props: PropsDictType = {
        "QueryEngineConfiguration": (RedshiftQueryEngineConfiguration, True),
        "QueryGenerationConfiguration": (QueryGenerationConfiguration, False),
        "StorageConfigurations": ([RedshiftQueryEngineStorageConfiguration], True),
    }


class SqlKnowledgeBaseConfiguration(AWSProperty):
    """
    `SqlKnowledgeBaseConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-sqlknowledgebaseconfiguration.html>`__
    """

    props: PropsDictType = {
        "RedshiftConfiguration": (RedshiftConfiguration, False),
        "Type": (str, True),
    }


class BedrockEmbeddingModelConfiguration(AWSProperty):
    """
    `BedrockEmbeddingModelConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-bedrockembeddingmodelconfiguration.html>`__
    """

    props: PropsDictType = {
        "Dimensions": (integer, False),
        "EmbeddingDataType": (str, False),
    }


class EmbeddingModelConfiguration(AWSProperty):
    """
    `EmbeddingModelConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-embeddingmodelconfiguration.html>`__
    """

    props: PropsDictType = {
        "BedrockEmbeddingModelConfiguration": (
            BedrockEmbeddingModelConfiguration,
            False,
        ),
    }


class SupplementalDataStorageLocation(AWSProperty):
    """
    `SupplementalDataStorageLocation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-supplementaldatastoragelocation.html>`__
    """

    props: PropsDictType = {
        "S3Location": (S3Location, False),
        "SupplementalDataStorageLocationType": (str, True),
    }


class SupplementalDataStorageConfiguration(AWSProperty):
    """
    `SupplementalDataStorageConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-supplementaldatastorageconfiguration.html>`__
    """

    props: PropsDictType = {
        "SupplementalDataStorageLocations": ([SupplementalDataStorageLocation], True),
    }


class VectorKnowledgeBaseConfiguration(AWSProperty):
    """
    `VectorKnowledgeBaseConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-vectorknowledgebaseconfiguration.html>`__
    """

    props: PropsDictType = {
        "EmbeddingModelArn": (str, True),
        "EmbeddingModelConfiguration": (EmbeddingModelConfiguration, False),
        "SupplementalDataStorageConfiguration": (
            SupplementalDataStorageConfiguration,
            False,
        ),
    }


class KnowledgeBaseConfiguration(AWSProperty):
    """
    `KnowledgeBaseConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-knowledgebaseconfiguration.html>`__
    """

    props: PropsDictType = {
        "KendraKnowledgeBaseConfiguration": (KendraKnowledgeBaseConfiguration, False),
        "SqlKnowledgeBaseConfiguration": (SqlKnowledgeBaseConfiguration, False),
        "Type": (str, True),
        "VectorKnowledgeBaseConfiguration": (VectorKnowledgeBaseConfiguration, False),
    }


class MongoDbAtlasFieldMapping(AWSProperty):
    """
    `MongoDbAtlasFieldMapping <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-mongodbatlasfieldmapping.html>`__
    """

    props: PropsDictType = {
        "MetadataField": (str, True),
        "TextField": (str, True),
        "VectorField": (str, True),
    }


class MongoDbAtlasConfiguration(AWSProperty):
    """
    `MongoDbAtlasConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-mongodbatlasconfiguration.html>`__
    """

    props: PropsDictType = {
        "CollectionName": (str, True),
        "CredentialsSecretArn": (str, True),
        "DatabaseName": (str, True),
        "Endpoint": (str, True),
        "EndpointServiceName": (str, False),
        "FieldMapping": (MongoDbAtlasFieldMapping, True),
        "VectorIndexName": (str, True),
    }


class NeptuneAnalyticsFieldMapping(AWSProperty):
    """
    `NeptuneAnalyticsFieldMapping <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-neptuneanalyticsfieldmapping.html>`__
    """

    props: PropsDictType = {
        "MetadataField": (str, True),
        "TextField": (str, True),
    }


class NeptuneAnalyticsConfiguration(AWSProperty):
    """
    `NeptuneAnalyticsConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-neptuneanalyticsconfiguration.html>`__
    """

    props: PropsDictType = {
        "FieldMapping": (NeptuneAnalyticsFieldMapping, True),
        "GraphArn": (str, True),
    }


class OpenSearchManagedClusterFieldMapping(AWSProperty):
    """
    `OpenSearchManagedClusterFieldMapping <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-opensearchmanagedclusterfieldmapping.html>`__
    """

    props: PropsDictType = {
        "MetadataField": (str, True),
        "TextField": (str, True),
        "VectorField": (str, True),
    }


class OpenSearchManagedClusterConfiguration(AWSProperty):
    """
    `OpenSearchManagedClusterConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-opensearchmanagedclusterconfiguration.html>`__
    """

    props: PropsDictType = {
        "DomainArn": (str, True),
        "DomainEndpoint": (str, True),
        "FieldMapping": (OpenSearchManagedClusterFieldMapping, True),
        "VectorIndexName": (str, True),
    }


class OpenSearchServerlessFieldMapping(AWSProperty):
    """
    `OpenSearchServerlessFieldMapping <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-opensearchserverlessfieldmapping.html>`__
    """

    props: PropsDictType = {
        "MetadataField": (str, True),
        "TextField": (str, True),
        "VectorField": (str, True),
    }


class OpenSearchServerlessConfiguration(AWSProperty):
    """
    `OpenSearchServerlessConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-opensearchserverlessconfiguration.html>`__
    """

    props: PropsDictType = {
        "CollectionArn": (str, True),
        "FieldMapping": (OpenSearchServerlessFieldMapping, True),
        "VectorIndexName": (str, True),
    }


class PineconeFieldMapping(AWSProperty):
    """
    `PineconeFieldMapping <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-pineconefieldmapping.html>`__
    """

    props: PropsDictType = {
        "MetadataField": (str, True),
        "TextField": (str, True),
    }


class PineconeConfiguration(AWSProperty):
    """
    `PineconeConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-pineconeconfiguration.html>`__
    """

    props: PropsDictType = {
        "ConnectionString": (str, True),
        "CredentialsSecretArn": (str, True),
        "FieldMapping": (PineconeFieldMapping, True),
        "Namespace": (str, False),
    }


class RdsFieldMapping(AWSProperty):
    """
    `RdsFieldMapping <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-rdsfieldmapping.html>`__
    """

    props: PropsDictType = {
        "MetadataField": (str, True),
        "PrimaryKeyField": (str, True),
        "TextField": (str, True),
        "VectorField": (str, True),
    }


class RdsConfiguration(AWSProperty):
    """
    `RdsConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-rdsconfiguration.html>`__
    """

    props: PropsDictType = {
        "CredentialsSecretArn": (str, True),
        "DatabaseName": (str, True),
        "FieldMapping": (RdsFieldMapping, True),
        "ResourceArn": (str, True),
        "TableName": (str, True),
    }


class StorageConfiguration(AWSProperty):
    """
    `StorageConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-knowledgebase-storageconfiguration.html>`__
    """

    props: PropsDictType = {
        "MongoDbAtlasConfiguration": (MongoDbAtlasConfiguration, False),
        "NeptuneAnalyticsConfiguration": (NeptuneAnalyticsConfiguration, False),
        "OpensearchManagedClusterConfiguration": (
            OpenSearchManagedClusterConfiguration,
            False,
        ),
        "OpensearchServerlessConfiguration": (OpenSearchServerlessConfiguration, False),
        "PineconeConfiguration": (PineconeConfiguration, False),
        "RdsConfiguration": (RdsConfiguration, False),
        "Type": (str, True),
    }


class KnowledgeBase(AWSObject):
    """
    `KnowledgeBase <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-bedrock-knowledgebase.html>`__
    """

    resource_type = "AWS::Bedrock::KnowledgeBase"

    props: PropsDictType = {
        "Description": (str, False),
        "KnowledgeBaseConfiguration": (KnowledgeBaseConfiguration, True),
        "Name": (str, True),
        "RoleArn": (str, True),
        "StorageConfiguration": (StorageConfiguration, False),
        "Tags": (dict, False),
    }


class PromptAgentResource(AWSProperty):
    """
    `PromptAgentResource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-promptagentresource.html>`__
    """

    props: PropsDictType = {
        "AgentIdentifier": (str, True),
    }


class PromptGenAiResource(AWSProperty):
    """
    `PromptGenAiResource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-promptgenairesource.html>`__
    """

    props: PropsDictType = {
        "Agent": (PromptAgentResource, True),
    }


class PromptMetadataEntry(AWSProperty):
    """
    `PromptMetadataEntry <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-promptmetadataentry.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (str, True),
    }


class PromptVariant(AWSProperty):
    """
    `PromptVariant <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-promptversion-promptvariant.html>`__
    """

    props: PropsDictType = {
        "AdditionalModelRequestFields": (dict, False),
        "GenAiResource": (PromptGenAiResource, False),
        "InferenceConfiguration": (PromptInferenceConfiguration, False),
        "Metadata": ([PromptMetadataEntry], False),
        "ModelId": (str, False),
        "Name": (str, True),
        "TemplateConfiguration": (PromptTemplateConfiguration, True),
        "TemplateType": (str, True),
    }


class Prompt(AWSObject):
    """
    `Prompt <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-bedrock-prompt.html>`__
    """

    resource_type = "AWS::Bedrock::Prompt"

    props: PropsDictType = {
        "CustomerEncryptionKeyArn": (str, False),
        "DefaultVariant": (str, False),
        "Description": (str, False),
        "Name": (str, True),
        "Tags": (dict, False),
        "Variants": ([PromptVariant], False),
    }


class PromptVersion(AWSObject):
    """
    `PromptVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-bedrock-promptversion.html>`__
    """

    resource_type = "AWS::Bedrock::PromptVersion"

    props: PropsDictType = {
        "Description": (str, False),
        "PromptArn": (str, True),
        "Tags": (dict, False),
    }


class AgentAliasHistoryEvent(AWSProperty):
    """
    `AgentAliasHistoryEvent <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-agentalias-agentaliashistoryevent.html>`__
    """

    props: PropsDictType = {
        "EndDate": (str, False),
        "RoutingConfiguration": ([AgentAliasRoutingConfigurationListItem], False),
        "StartDate": (str, False),
    }


class FlowValidation(AWSProperty):
    """
    `FlowValidation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-flow-flowvalidation.html>`__
    """

    props: PropsDictType = {
        "Message": (str, True),
    }


class InferenceProfileModel(AWSProperty):
    """
    `InferenceProfileModel <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-applicationinferenceprofile-inferenceprofilemodel.html>`__
    """

    props: PropsDictType = {
        "ModelArn": (str, False),
    }


class TextS3Location(AWSProperty):
    """
    `TextS3Location <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-bedrock-prompt-texts3location.html>`__
    """

    props: PropsDictType = {
        "Bucket": (str, True),
        "Key": (str, True),
        "Version": (str, False),
    }
