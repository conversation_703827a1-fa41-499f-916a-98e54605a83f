# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, PropsDictType, Tags


class EnvironmentAccountConnection(AWSObject):
    """
    `EnvironmentAccountConnection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-proton-environmentaccountconnection.html>`__
    """

    resource_type = "AWS::Proton::EnvironmentAccountConnection"

    props: PropsDictType = {
        "CodebuildRoleArn": (str, False),
        "ComponentRoleArn": (str, False),
        "EnvironmentAccountId": (str, False),
        "EnvironmentName": (str, False),
        "ManagementAccountId": (str, False),
        "RoleArn": (str, False),
        "Tags": (Tags, False),
    }


class EnvironmentTemplate(AWSObject):
    """
    `EnvironmentTemplate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-proton-environmenttemplate.html>`__
    """

    resource_type = "AWS::Proton::EnvironmentTemplate"

    props: PropsDictType = {
        "Description": (str, False),
        "DisplayName": (str, False),
        "EncryptionKey": (str, False),
        "Name": (str, False),
        "Provisioning": (str, False),
        "Tags": (Tags, False),
    }


class ServiceTemplate(AWSObject):
    """
    `ServiceTemplate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-proton-servicetemplate.html>`__
    """

    resource_type = "AWS::Proton::ServiceTemplate"

    props: PropsDictType = {
        "Description": (str, False),
        "DisplayName": (str, False),
        "EncryptionKey": (str, False),
        "Name": (str, False),
        "PipelineProvisioning": (str, False),
        "Tags": (Tags, False),
    }
