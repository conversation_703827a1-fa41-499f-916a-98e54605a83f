# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, integer
from .validators.servicecatalog import validate_tag_update


class AcceptedPortfolioShare(AWSObject):
    """
    `AcceptedPortfolioShare <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicecatalog-acceptedportfolioshare.html>`__
    """

    resource_type = "AWS::ServiceCatalog::AcceptedPortfolioShare"

    props: PropsDictType = {
        "AcceptLanguage": (str, False),
        "PortfolioId": (str, True),
    }


class ProvisioningArtifactProperties(AWSProperty):
    """
    `ProvisioningArtifactProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-servicecatalog-cloudformationproduct-provisioningartifactproperties.html>`__
    """

    props: PropsDictType = {
        "Description": (str, False),
        "DisableTemplateValidation": (boolean, False),
        "Info": (dict, True),
        "Name": (str, False),
        "Type": (str, False),
    }


class CodeStarParameters(AWSProperty):
    """
    `CodeStarParameters <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-servicecatalog-cloudformationproduct-codestarparameters.html>`__
    """

    props: PropsDictType = {
        "ArtifactPath": (str, True),
        "Branch": (str, True),
        "ConnectionArn": (str, True),
        "Repository": (str, True),
    }


class ConnectionParameters(AWSProperty):
    """
    `ConnectionParameters <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-servicecatalog-cloudformationproduct-sourceconnection-connectionparameters.html>`__
    """

    props: PropsDictType = {
        "CodeStar": (CodeStarParameters, False),
    }


class SourceConnection(AWSProperty):
    """
    `SourceConnection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-servicecatalog-cloudformationproduct-sourceconnection.html>`__
    """

    props: PropsDictType = {
        "ConnectionParameters": (ConnectionParameters, True),
        "Type": (str, True),
    }


class CloudFormationProduct(AWSObject):
    """
    `CloudFormationProduct <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicecatalog-cloudformationproduct.html>`__
    """

    resource_type = "AWS::ServiceCatalog::CloudFormationProduct"

    props: PropsDictType = {
        "AcceptLanguage": (str, False),
        "Description": (str, False),
        "Distributor": (str, False),
        "Name": (str, True),
        "Owner": (str, True),
        "ProductType": (str, False),
        "ProvisioningArtifactParameters": ([ProvisioningArtifactProperties], False),
        "ReplaceProvisioningArtifacts": (boolean, False),
        "SourceConnection": (SourceConnection, False),
        "SupportDescription": (str, False),
        "SupportEmail": (str, False),
        "SupportUrl": (str, False),
        "Tags": (Tags, False),
    }


class ProvisioningParameter(AWSProperty):
    """
    `ProvisioningParameter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-servicecatalog-cloudformationprovisionedproduct-provisioningparameter.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (str, True),
    }


class ProvisioningPreferences(AWSProperty):
    """
    `ProvisioningPreferences <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-servicecatalog-cloudformationprovisionedproduct-provisioningpreferences.html>`__
    """

    props: PropsDictType = {
        "StackSetAccounts": ([str], False),
        "StackSetFailureToleranceCount": (integer, False),
        "StackSetFailureTolerancePercentage": (integer, False),
        "StackSetMaxConcurrencyCount": (integer, False),
        "StackSetMaxConcurrencyPercentage": (integer, False),
        "StackSetOperationType": (str, False),
        "StackSetRegions": ([str], False),
    }


class CloudFormationProvisionedProduct(AWSObject):
    """
    `CloudFormationProvisionedProduct <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicecatalog-cloudformationprovisionedproduct.html>`__
    """

    resource_type = "AWS::ServiceCatalog::CloudFormationProvisionedProduct"

    props: PropsDictType = {
        "AcceptLanguage": (str, False),
        "NotificationArns": ([str], False),
        "PathId": (str, False),
        "PathName": (str, False),
        "ProductId": (str, False),
        "ProductName": (str, False),
        "ProvisionedProductName": (str, False),
        "ProvisioningArtifactId": (str, False),
        "ProvisioningArtifactName": (str, False),
        "ProvisioningParameters": ([ProvisioningParameter], False),
        "ProvisioningPreferences": (ProvisioningPreferences, False),
        "Tags": (Tags, False),
    }


class LaunchNotificationConstraint(AWSObject):
    """
    `LaunchNotificationConstraint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicecatalog-launchnotificationconstraint.html>`__
    """

    resource_type = "AWS::ServiceCatalog::LaunchNotificationConstraint"

    props: PropsDictType = {
        "AcceptLanguage": (str, False),
        "Description": (str, False),
        "NotificationArns": ([str], True),
        "PortfolioId": (str, True),
        "ProductId": (str, True),
    }


class LaunchRoleConstraint(AWSObject):
    """
    `LaunchRoleConstraint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicecatalog-launchroleconstraint.html>`__
    """

    resource_type = "AWS::ServiceCatalog::LaunchRoleConstraint"

    props: PropsDictType = {
        "AcceptLanguage": (str, False),
        "Description": (str, False),
        "LocalRoleName": (str, False),
        "PortfolioId": (str, True),
        "ProductId": (str, True),
        "RoleArn": (str, False),
    }


class LaunchTemplateConstraint(AWSObject):
    """
    `LaunchTemplateConstraint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicecatalog-launchtemplateconstraint.html>`__
    """

    resource_type = "AWS::ServiceCatalog::LaunchTemplateConstraint"

    props: PropsDictType = {
        "AcceptLanguage": (str, False),
        "Description": (str, False),
        "PortfolioId": (str, True),
        "ProductId": (str, True),
        "Rules": (str, True),
    }


class Portfolio(AWSObject):
    """
    `Portfolio <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicecatalog-portfolio.html>`__
    """

    resource_type = "AWS::ServiceCatalog::Portfolio"

    props: PropsDictType = {
        "AcceptLanguage": (str, False),
        "Description": (str, False),
        "DisplayName": (str, True),
        "ProviderName": (str, True),
        "Tags": (Tags, False),
    }


class PortfolioPrincipalAssociation(AWSObject):
    """
    `PortfolioPrincipalAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicecatalog-portfolioprincipalassociation.html>`__
    """

    resource_type = "AWS::ServiceCatalog::PortfolioPrincipalAssociation"

    props: PropsDictType = {
        "AcceptLanguage": (str, False),
        "PortfolioId": (str, True),
        "PrincipalARN": (str, True),
        "PrincipalType": (str, True),
    }


class PortfolioProductAssociation(AWSObject):
    """
    `PortfolioProductAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicecatalog-portfolioproductassociation.html>`__
    """

    resource_type = "AWS::ServiceCatalog::PortfolioProductAssociation"

    props: PropsDictType = {
        "AcceptLanguage": (str, False),
        "PortfolioId": (str, True),
        "ProductId": (str, True),
        "SourcePortfolioId": (str, False),
    }


class PortfolioShare(AWSObject):
    """
    `PortfolioShare <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicecatalog-portfolioshare.html>`__
    """

    resource_type = "AWS::ServiceCatalog::PortfolioShare"

    props: PropsDictType = {
        "AcceptLanguage": (str, False),
        "AccountId": (str, True),
        "PortfolioId": (str, True),
        "ShareTagOptions": (boolean, False),
    }


class ResourceUpdateConstraint(AWSObject):
    """
    `ResourceUpdateConstraint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicecatalog-resourceupdateconstraint.html>`__
    """

    resource_type = "AWS::ServiceCatalog::ResourceUpdateConstraint"

    props: PropsDictType = {
        "AcceptLanguage": (str, False),
        "Description": (str, False),
        "PortfolioId": (str, True),
        "ProductId": (str, True),
        "TagUpdateOnProvisionedProduct": (validate_tag_update, True),
    }


class DefinitionParameter(AWSProperty):
    """
    `DefinitionParameter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-servicecatalog-serviceaction-definitionparameter.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (str, True),
    }


class ServiceAction(AWSObject):
    """
    `ServiceAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicecatalog-serviceaction.html>`__
    """

    resource_type = "AWS::ServiceCatalog::ServiceAction"

    props: PropsDictType = {
        "AcceptLanguage": (str, False),
        "Definition": ([DefinitionParameter], True),
        "DefinitionType": (str, True),
        "Description": (str, False),
        "Name": (str, True),
    }


class ServiceActionAssociation(AWSObject):
    """
    `ServiceActionAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicecatalog-serviceactionassociation.html>`__
    """

    resource_type = "AWS::ServiceCatalog::ServiceActionAssociation"

    props: PropsDictType = {
        "ProductId": (str, True),
        "ProvisioningArtifactId": (str, True),
        "ServiceActionId": (str, True),
    }


class StackSetConstraint(AWSObject):
    """
    `StackSetConstraint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicecatalog-stacksetconstraint.html>`__
    """

    resource_type = "AWS::ServiceCatalog::StackSetConstraint"

    props: PropsDictType = {
        "AcceptLanguage": (str, False),
        "AccountList": ([str], True),
        "AdminRole": (str, True),
        "Description": (str, True),
        "ExecutionRole": (str, True),
        "PortfolioId": (str, True),
        "ProductId": (str, True),
        "RegionList": ([str], True),
        "StackInstanceControl": (str, True),
    }


class TagOption(AWSObject):
    """
    `TagOption <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicecatalog-tagoption.html>`__
    """

    resource_type = "AWS::ServiceCatalog::TagOption"

    props: PropsDictType = {
        "Active": (boolean, False),
        "Key": (str, True),
        "Value": (str, True),
    }


class TagOptionAssociation(AWSObject):
    """
    `TagOptionAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-servicecatalog-tagoptionassociation.html>`__
    """

    resource_type = "AWS::ServiceCatalog::TagOptionAssociation"

    props: PropsDictType = {
        "ResourceId": (str, True),
        "TagOptionId": (str, True),
    }
