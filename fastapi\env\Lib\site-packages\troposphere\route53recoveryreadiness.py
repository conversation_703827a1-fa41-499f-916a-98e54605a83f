# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags


class Cell(AWSObject):
    """
    `Cell <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-route53recoveryreadiness-cell.html>`__
    """

    resource_type = "AWS::Route53RecoveryReadiness::Cell"

    props: PropsDictType = {
        "CellName": (str, False),
        "Cells": ([str], False),
        "Tags": (Tags, False),
    }


class ReadinessCheck(AWSObject):
    """
    `ReadinessCheck <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-route53recoveryreadiness-readinesscheck.html>`__
    """

    resource_type = "AWS::Route53RecoveryReadiness::ReadinessCheck"

    props: PropsDictType = {
        "ReadinessCheckName": (str, False),
        "ResourceSetName": (str, False),
        "Tags": (Tags, False),
    }


class RecoveryGroup(AWSObject):
    """
    `RecoveryGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-route53recoveryreadiness-recoverygroup.html>`__
    """

    resource_type = "AWS::Route53RecoveryReadiness::RecoveryGroup"

    props: PropsDictType = {
        "Cells": ([str], False),
        "RecoveryGroupName": (str, False),
        "Tags": (Tags, False),
    }


class NLBResource(AWSProperty):
    """
    `NLBResource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-route53recoveryreadiness-resourceset-nlbresource.html>`__
    """

    props: PropsDictType = {
        "Arn": (str, False),
    }


class R53ResourceRecord(AWSProperty):
    """
    `R53ResourceRecord <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-route53recoveryreadiness-resourceset-r53resourcerecord.html>`__
    """

    props: PropsDictType = {
        "DomainName": (str, False),
        "RecordSetId": (str, False),
    }


class TargetResource(AWSProperty):
    """
    `TargetResource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-route53recoveryreadiness-resourceset-targetresource.html>`__
    """

    props: PropsDictType = {
        "NLBResource": (NLBResource, False),
        "R53Resource": (R53ResourceRecord, False),
    }


class DNSTargetResource(AWSProperty):
    """
    `DNSTargetResource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-route53recoveryreadiness-resourceset-dnstargetresource.html>`__
    """

    props: PropsDictType = {
        "DomainName": (str, False),
        "HostedZoneArn": (str, False),
        "RecordSetId": (str, False),
        "RecordType": (str, False),
        "TargetResource": (TargetResource, False),
    }


class Resource(AWSProperty):
    """
    `Resource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-route53recoveryreadiness-resourceset-resource.html>`__
    """

    props: PropsDictType = {
        "ComponentId": (str, False),
        "DnsTargetResource": (DNSTargetResource, False),
        "ReadinessScopes": ([str], False),
        "ResourceArn": (str, False),
    }


class ResourceSet(AWSObject):
    """
    `ResourceSet <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-route53recoveryreadiness-resourceset.html>`__
    """

    resource_type = "AWS::Route53RecoveryReadiness::ResourceSet"

    props: PropsDictType = {
        "ResourceSetName": (str, False),
        "ResourceSetType": (str, True),
        "Resources": ([Resource], True),
        "Tags": (Tags, False),
    }
