# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators.codeartifact import policytypes


class Domain(AWSObject):
    """
    `Domain <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-codeartifact-domain.html>`__
    """

    resource_type = "AWS::CodeArtifact::Domain"

    props: PropsDictType = {
        "DomainName": (str, True),
        "EncryptionKey": (str, False),
        "PermissionsPolicyDocument": (policytypes, False),
        "Tags": (Tags, False),
    }


class RestrictionType(AWSProperty):
    """
    `RestrictionType <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codeartifact-packagegroup-restrictiontype.html>`__
    """

    props: PropsDictType = {
        "Repositories": ([str], False),
        "RestrictionMode": (str, True),
    }


class Restrictions(AWSProperty):
    """
    `Restrictions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codeartifact-packagegroup-restrictions.html>`__
    """

    props: PropsDictType = {
        "ExternalUpstream": (RestrictionType, False),
        "InternalUpstream": (RestrictionType, False),
        "Publish": (RestrictionType, False),
    }


class OriginConfiguration(AWSProperty):
    """
    `OriginConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-codeartifact-packagegroup-originconfiguration.html>`__
    """

    props: PropsDictType = {
        "Restrictions": (Restrictions, True),
    }


class PackageGroup(AWSObject):
    """
    `PackageGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-codeartifact-packagegroup.html>`__
    """

    resource_type = "AWS::CodeArtifact::PackageGroup"

    props: PropsDictType = {
        "ContactInfo": (str, False),
        "Description": (str, False),
        "DomainName": (str, True),
        "DomainOwner": (str, False),
        "OriginConfiguration": (OriginConfiguration, False),
        "Pattern": (str, True),
        "Tags": (Tags, False),
    }


class Repository(AWSObject):
    """
    `Repository <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-codeartifact-repository.html>`__
    """

    resource_type = "AWS::CodeArtifact::Repository"

    props: PropsDictType = {
        "Description": (str, False),
        "DomainName": (str, True),
        "DomainOwner": (str, False),
        "ExternalConnections": ([str], False),
        "PermissionsPolicyDocument": (policytypes, False),
        "RepositoryName": (str, True),
        "Tags": (Tags, False),
        "Upstreams": ([str], False),
    }
