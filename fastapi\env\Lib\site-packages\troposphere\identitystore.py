# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType


class Group(AWSObject):
    """
    `Group <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-identitystore-group.html>`__
    """

    resource_type = "AWS::IdentityStore::Group"

    props: PropsDictType = {
        "Description": (str, False),
        "DisplayName": (str, True),
        "IdentityStoreId": (str, True),
    }


class MemberId(AWSProperty):
    """
    `MemberId <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-identitystore-groupmembership-memberid.html>`__
    """

    props: PropsDictType = {
        "UserId": (str, True),
    }


class GroupMembership(AWSObject):
    """
    `GroupMembership <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-identitystore-groupmembership.html>`__
    """

    resource_type = "AWS::IdentityStore::GroupMembership"

    props: PropsDictType = {
        "GroupId": (str, True),
        "IdentityStoreId": (str, True),
        "MemberId": (MemberId, True),
    }
