# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, integer
from .validators.appmesh import validate_listenertls_mode


class GatewayRouteVirtualService(AWSProperty):
    """
    `GatewayRouteVirtualService <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-gatewayroutevirtualservice.html>`__
    """

    props: PropsDictType = {
        "VirtualServiceName": (str, True),
    }


class GatewayRouteTarget(AWSProperty):
    """
    `GatewayRouteTarget <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-gatewayroutetarget.html>`__
    """

    props: PropsDictType = {
        "Port": (integer, False),
        "VirtualService": (GatewayRouteVirtualService, True),
    }


class GatewayRouteHostnameRewrite(AWSProperty):
    """
    `GatewayRouteHostnameRewrite <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-gatewayroutehostnamerewrite.html>`__
    """

    props: PropsDictType = {
        "DefaultTargetHostname": (str, False),
    }


class GrpcGatewayRouteRewrite(AWSProperty):
    """
    `GrpcGatewayRouteRewrite <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-grpcgatewayrouterewrite.html>`__
    """

    props: PropsDictType = {
        "Hostname": (GatewayRouteHostnameRewrite, False),
    }


class GrpcGatewayRouteAction(AWSProperty):
    """
    `GrpcGatewayRouteAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-grpcgatewayrouteaction.html>`__
    """

    props: PropsDictType = {
        "Rewrite": (GrpcGatewayRouteRewrite, False),
        "Target": (GatewayRouteTarget, True),
    }


class GatewayRouteHostnameMatch(AWSProperty):
    """
    `GatewayRouteHostnameMatch <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-gatewayroutehostnamematch.html>`__
    """

    props: PropsDictType = {
        "Exact": (str, False),
        "Suffix": (str, False),
    }


class GatewayRouteRangeMatch(AWSProperty):
    """
    `GatewayRouteRangeMatch <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-gatewayrouterangematch.html>`__
    """

    props: PropsDictType = {
        "End": (integer, True),
        "Start": (integer, True),
    }


class GatewayRouteMetadataMatch(AWSProperty):
    """
    `GatewayRouteMetadataMatch <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-gatewayroutemetadatamatch.html>`__
    """

    props: PropsDictType = {
        "Exact": (str, False),
        "Prefix": (str, False),
        "Range": (GatewayRouteRangeMatch, False),
        "Regex": (str, False),
        "Suffix": (str, False),
    }


class GrpcGatewayRouteMetadata(AWSProperty):
    """
    `GrpcGatewayRouteMetadata <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-grpcgatewayroutemetadata.html>`__
    """

    props: PropsDictType = {
        "Invert": (boolean, False),
        "Match": (GatewayRouteMetadataMatch, False),
        "Name": (str, True),
    }


class GrpcGatewayRouteMatch(AWSProperty):
    """
    `GrpcGatewayRouteMatch <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-grpcgatewayroutematch.html>`__
    """

    props: PropsDictType = {
        "Hostname": (GatewayRouteHostnameMatch, False),
        "Metadata": ([GrpcGatewayRouteMetadata], False),
        "Port": (integer, False),
        "ServiceName": (str, False),
    }


class GrpcGatewayRoute(AWSProperty):
    """
    `GrpcGatewayRoute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-grpcgatewayroute.html>`__
    """

    props: PropsDictType = {
        "Action": (GrpcGatewayRouteAction, True),
        "Match": (GrpcGatewayRouteMatch, True),
    }


class HttpGatewayRoutePathRewrite(AWSProperty):
    """
    `HttpGatewayRoutePathRewrite <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-httpgatewayroutepathrewrite.html>`__
    """

    props: PropsDictType = {
        "Exact": (str, False),
    }


class HttpGatewayRoutePrefixRewrite(AWSProperty):
    """
    `HttpGatewayRoutePrefixRewrite <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-httpgatewayrouteprefixrewrite.html>`__
    """

    props: PropsDictType = {
        "DefaultPrefix": (str, False),
        "Value": (str, False),
    }


class HttpGatewayRouteRewrite(AWSProperty):
    """
    `HttpGatewayRouteRewrite <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-httpgatewayrouterewrite.html>`__
    """

    props: PropsDictType = {
        "Hostname": (GatewayRouteHostnameRewrite, False),
        "Path": (HttpGatewayRoutePathRewrite, False),
        "Prefix": (HttpGatewayRoutePrefixRewrite, False),
    }


class HttpGatewayRouteAction(AWSProperty):
    """
    `HttpGatewayRouteAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-httpgatewayrouteaction.html>`__
    """

    props: PropsDictType = {
        "Rewrite": (HttpGatewayRouteRewrite, False),
        "Target": (GatewayRouteTarget, True),
    }


class HttpGatewayRouteHeaderMatch(AWSProperty):
    """
    `HttpGatewayRouteHeaderMatch <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-httpgatewayrouteheadermatch.html>`__
    """

    props: PropsDictType = {
        "Exact": (str, False),
        "Prefix": (str, False),
        "Range": (GatewayRouteRangeMatch, False),
        "Regex": (str, False),
        "Suffix": (str, False),
    }


class HttpGatewayRouteHeader(AWSProperty):
    """
    `HttpGatewayRouteHeader <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-httpgatewayrouteheader.html>`__
    """

    props: PropsDictType = {
        "Invert": (boolean, False),
        "Match": (HttpGatewayRouteHeaderMatch, False),
        "Name": (str, True),
    }


class HttpPathMatch(AWSProperty):
    """
    `HttpPathMatch <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-httppathmatch.html>`__
    """

    props: PropsDictType = {
        "Exact": (str, False),
        "Regex": (str, False),
    }


class HttpQueryParameterMatch(AWSProperty):
    """
    `HttpQueryParameterMatch <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-httpqueryparametermatch.html>`__
    """

    props: PropsDictType = {
        "Exact": (str, False),
    }


class QueryParameter(AWSProperty):
    """
    `QueryParameter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-queryparameter.html>`__
    """

    props: PropsDictType = {
        "Match": (HttpQueryParameterMatch, False),
        "Name": (str, True),
    }


class HttpGatewayRouteMatch(AWSProperty):
    """
    `HttpGatewayRouteMatch <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-httpgatewayroutematch.html>`__
    """

    props: PropsDictType = {
        "Headers": ([HttpGatewayRouteHeader], False),
        "Hostname": (GatewayRouteHostnameMatch, False),
        "Method": (str, False),
        "Path": (HttpPathMatch, False),
        "Port": (integer, False),
        "Prefix": (str, False),
        "QueryParameters": ([QueryParameter], False),
    }


class HttpGatewayRoute(AWSProperty):
    """
    `HttpGatewayRoute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-httpgatewayroute.html>`__
    """

    props: PropsDictType = {
        "Action": (HttpGatewayRouteAction, True),
        "Match": (HttpGatewayRouteMatch, True),
    }


class GatewayRouteSpec(AWSProperty):
    """
    `GatewayRouteSpec <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-gatewayroute-gatewayroutespec.html>`__
    """

    props: PropsDictType = {
        "GrpcRoute": (GrpcGatewayRoute, False),
        "Http2Route": (HttpGatewayRoute, False),
        "HttpRoute": (HttpGatewayRoute, False),
        "Priority": (integer, False),
    }


class GatewayRoute(AWSObject):
    """
    `GatewayRoute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appmesh-gatewayroute.html>`__
    """

    resource_type = "AWS::AppMesh::GatewayRoute"

    props: PropsDictType = {
        "GatewayRouteName": (str, False),
        "MeshName": (str, True),
        "MeshOwner": (str, False),
        "Spec": (GatewayRouteSpec, True),
        "Tags": (Tags, False),
        "VirtualGatewayName": (str, True),
    }


class EgressFilter(AWSProperty):
    """
    `EgressFilter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-mesh-egressfilter.html>`__
    """

    props: PropsDictType = {
        "Type": (str, True),
    }


class MeshServiceDiscovery(AWSProperty):
    """
    `MeshServiceDiscovery <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-mesh-meshservicediscovery.html>`__
    """

    props: PropsDictType = {
        "IpPreference": (str, False),
    }


class MeshSpec(AWSProperty):
    """
    `MeshSpec <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-mesh-meshspec.html>`__
    """

    props: PropsDictType = {
        "EgressFilter": (EgressFilter, False),
        "ServiceDiscovery": (MeshServiceDiscovery, False),
    }


class Mesh(AWSObject):
    """
    `Mesh <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appmesh-mesh.html>`__
    """

    resource_type = "AWS::AppMesh::Mesh"

    props: PropsDictType = {
        "MeshName": (str, False),
        "Spec": (MeshSpec, False),
        "Tags": (Tags, False),
    }


class Duration(AWSProperty):
    """
    `Duration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-duration.html>`__
    """

    props: PropsDictType = {
        "Unit": (str, True),
        "Value": (integer, True),
    }


class GrpcRetryPolicy(AWSProperty):
    """
    `GrpcRetryPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-grpcretrypolicy.html>`__
    """

    props: PropsDictType = {
        "GrpcRetryEvents": ([str], False),
        "HttpRetryEvents": ([str], False),
        "MaxRetries": (integer, True),
        "PerRetryTimeout": (Duration, True),
        "TcpRetryEvents": ([str], False),
    }


class WeightedTarget(AWSProperty):
    """
    `WeightedTarget <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-weightedtarget.html>`__
    """

    props: PropsDictType = {
        "Port": (integer, False),
        "VirtualNode": (str, True),
        "Weight": (integer, True),
    }


class GrpcRouteAction(AWSProperty):
    """
    `GrpcRouteAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-grpcrouteaction.html>`__
    """

    props: PropsDictType = {
        "WeightedTargets": ([WeightedTarget], True),
    }


class MatchRange(AWSProperty):
    """
    `MatchRange <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-matchrange.html>`__
    """

    props: PropsDictType = {
        "End": (integer, True),
        "Start": (integer, True),
    }


class GrpcRouteMetadataMatchMethod(AWSProperty):
    """
    `GrpcRouteMetadataMatchMethod <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-grpcroutemetadatamatchmethod.html>`__
    """

    props: PropsDictType = {
        "Exact": (str, False),
        "Prefix": (str, False),
        "Range": (MatchRange, False),
        "Regex": (str, False),
        "Suffix": (str, False),
    }


class GrpcRouteMetadata(AWSProperty):
    """
    `GrpcRouteMetadata <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-grpcroutemetadata.html>`__
    """

    props: PropsDictType = {
        "Invert": (boolean, False),
        "Match": (GrpcRouteMetadataMatchMethod, False),
        "Name": (str, True),
    }


class GrpcRouteMatch(AWSProperty):
    """
    `GrpcRouteMatch <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-grpcroutematch.html>`__
    """

    props: PropsDictType = {
        "Metadata": ([GrpcRouteMetadata], False),
        "MethodName": (str, False),
        "Port": (integer, False),
        "ServiceName": (str, False),
    }


class GrpcTimeout(AWSProperty):
    """
    `GrpcTimeout <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-grpctimeout.html>`__
    """

    props: PropsDictType = {
        "Idle": (Duration, False),
        "PerRequest": (Duration, False),
    }


class GrpcRoute(AWSProperty):
    """
    `GrpcRoute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-grpcroute.html>`__
    """

    props: PropsDictType = {
        "Action": (GrpcRouteAction, True),
        "Match": (GrpcRouteMatch, True),
        "RetryPolicy": (GrpcRetryPolicy, False),
        "Timeout": (GrpcTimeout, False),
    }


class HttpRetryPolicy(AWSProperty):
    """
    `HttpRetryPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-httpretrypolicy.html>`__
    """

    props: PropsDictType = {
        "HttpRetryEvents": ([str], False),
        "MaxRetries": (integer, True),
        "PerRetryTimeout": (Duration, True),
        "TcpRetryEvents": ([str], False),
    }


class HttpRouteAction(AWSProperty):
    """
    `HttpRouteAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-httprouteaction.html>`__
    """

    props: PropsDictType = {
        "WeightedTargets": ([WeightedTarget], True),
    }


class HeaderMatchMethod(AWSProperty):
    """
    `HeaderMatchMethod <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-headermatchmethod.html>`__
    """

    props: PropsDictType = {
        "Exact": (str, False),
        "Prefix": (str, False),
        "Range": (MatchRange, False),
        "Regex": (str, False),
        "Suffix": (str, False),
    }


class HttpRouteHeader(AWSProperty):
    """
    `HttpRouteHeader <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-httprouteheader.html>`__
    """

    props: PropsDictType = {
        "Invert": (boolean, False),
        "Match": (HeaderMatchMethod, False),
        "Name": (str, True),
    }


class HttpRouteMatch(AWSProperty):
    """
    `HttpRouteMatch <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-httproutematch.html>`__
    """

    props: PropsDictType = {
        "Headers": ([HttpRouteHeader], False),
        "Method": (str, False),
        "Path": (HttpPathMatch, False),
        "Port": (integer, False),
        "Prefix": (str, False),
        "QueryParameters": ([QueryParameter], False),
        "Scheme": (str, False),
    }


class HttpTimeout(AWSProperty):
    """
    `HttpTimeout <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-httptimeout.html>`__
    """

    props: PropsDictType = {
        "Idle": (Duration, False),
        "PerRequest": (Duration, False),
    }


class HttpRoute(AWSProperty):
    """
    `HttpRoute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-httproute.html>`__
    """

    props: PropsDictType = {
        "Action": (HttpRouteAction, True),
        "Match": (HttpRouteMatch, True),
        "RetryPolicy": (HttpRetryPolicy, False),
        "Timeout": (HttpTimeout, False),
    }


class TcpRouteAction(AWSProperty):
    """
    `TcpRouteAction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-tcprouteaction.html>`__
    """

    props: PropsDictType = {
        "WeightedTargets": ([WeightedTarget], True),
    }


class TcpRouteMatch(AWSProperty):
    """
    `TcpRouteMatch <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-tcproutematch.html>`__
    """

    props: PropsDictType = {
        "Port": (integer, False),
    }


class TcpTimeout(AWSProperty):
    """
    `TcpTimeout <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-tcptimeout.html>`__
    """

    props: PropsDictType = {
        "Idle": (Duration, False),
    }


class TcpRoute(AWSProperty):
    """
    `TcpRoute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-tcproute.html>`__
    """

    props: PropsDictType = {
        "Action": (TcpRouteAction, True),
        "Match": (TcpRouteMatch, False),
        "Timeout": (TcpTimeout, False),
    }


class RouteSpec(AWSProperty):
    """
    `RouteSpec <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-route-routespec.html>`__
    """

    props: PropsDictType = {
        "GrpcRoute": (GrpcRoute, False),
        "Http2Route": (HttpRoute, False),
        "HttpRoute": (HttpRoute, False),
        "Priority": (integer, False),
        "TcpRoute": (TcpRoute, False),
    }


class Route(AWSObject):
    """
    `Route <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appmesh-route.html>`__
    """

    resource_type = "AWS::AppMesh::Route"

    props: PropsDictType = {
        "MeshName": (str, True),
        "MeshOwner": (str, False),
        "RouteName": (str, False),
        "Spec": (RouteSpec, True),
        "Tags": (Tags, False),
        "VirtualRouterName": (str, True),
    }


class VirtualGatewayListenerTlsFileCertificate(AWSProperty):
    """
    `VirtualGatewayListenerTlsFileCertificate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewaylistenertlsfilecertificate.html>`__
    """

    props: PropsDictType = {
        "CertificateChain": (str, True),
        "PrivateKey": (str, True),
    }


class VirtualGatewayListenerTlsSdsCertificate(AWSProperty):
    """
    `VirtualGatewayListenerTlsSdsCertificate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewaylistenertlssdscertificate.html>`__
    """

    props: PropsDictType = {
        "SecretName": (str, True),
    }


class VirtualGatewayClientTlsCertificate(AWSProperty):
    """
    `VirtualGatewayClientTlsCertificate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewayclienttlscertificate.html>`__
    """

    props: PropsDictType = {
        "File": (VirtualGatewayListenerTlsFileCertificate, False),
        "SDS": (VirtualGatewayListenerTlsSdsCertificate, False),
    }


class SubjectAlternativeNameMatchers(AWSProperty):
    """
    `SubjectAlternativeNameMatchers <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-subjectalternativenamematchers.html>`__
    """

    props: PropsDictType = {
        "Exact": ([str], False),
    }


class SubjectAlternativeNames(AWSProperty):
    """
    `SubjectAlternativeNames <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-subjectalternativenames.html>`__
    """

    props: PropsDictType = {
        "Match": (SubjectAlternativeNameMatchers, True),
    }


class VirtualGatewayTlsValidationContextAcmTrust(AWSProperty):
    """
    `VirtualGatewayTlsValidationContextAcmTrust <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewaytlsvalidationcontextacmtrust.html>`__
    """

    props: PropsDictType = {
        "CertificateAuthorityArns": ([str], True),
    }


class VirtualGatewayTlsValidationContextFileTrust(AWSProperty):
    """
    `VirtualGatewayTlsValidationContextFileTrust <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewaytlsvalidationcontextfiletrust.html>`__
    """

    props: PropsDictType = {
        "CertificateChain": (str, True),
    }


class VirtualGatewayTlsValidationContextSdsTrust(AWSProperty):
    """
    `VirtualGatewayTlsValidationContextSdsTrust <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewaytlsvalidationcontextsdstrust.html>`__
    """

    props: PropsDictType = {
        "SecretName": (str, True),
    }


class VirtualGatewayTlsValidationContextTrust(AWSProperty):
    """
    `VirtualGatewayTlsValidationContextTrust <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewaytlsvalidationcontexttrust.html>`__
    """

    props: PropsDictType = {
        "ACM": (VirtualGatewayTlsValidationContextAcmTrust, False),
        "File": (VirtualGatewayTlsValidationContextFileTrust, False),
        "SDS": (VirtualGatewayTlsValidationContextSdsTrust, False),
    }


class VirtualGatewayTlsValidationContext(AWSProperty):
    """
    `VirtualGatewayTlsValidationContext <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewaytlsvalidationcontext.html>`__
    """

    props: PropsDictType = {
        "SubjectAlternativeNames": (SubjectAlternativeNames, False),
        "Trust": (VirtualGatewayTlsValidationContextTrust, True),
    }


class VirtualGatewayClientPolicyTls(AWSProperty):
    """
    `VirtualGatewayClientPolicyTls <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewayclientpolicytls.html>`__
    """

    props: PropsDictType = {
        "Certificate": (VirtualGatewayClientTlsCertificate, False),
        "Enforce": (boolean, False),
        "Ports": ([integer], False),
        "Validation": (VirtualGatewayTlsValidationContext, True),
    }


class VirtualGatewayClientPolicy(AWSProperty):
    """
    `VirtualGatewayClientPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewayclientpolicy.html>`__
    """

    props: PropsDictType = {
        "TLS": (VirtualGatewayClientPolicyTls, False),
    }


class VirtualGatewayBackendDefaults(AWSProperty):
    """
    `VirtualGatewayBackendDefaults <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewaybackenddefaults.html>`__
    """

    props: PropsDictType = {
        "ClientPolicy": (VirtualGatewayClientPolicy, False),
    }


class VirtualGatewayGrpcConnectionPool(AWSProperty):
    """
    `VirtualGatewayGrpcConnectionPool <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewaygrpcconnectionpool.html>`__
    """

    props: PropsDictType = {
        "MaxRequests": (integer, True),
    }


class VirtualGatewayHttp2ConnectionPool(AWSProperty):
    """
    `VirtualGatewayHttp2ConnectionPool <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewayhttp2connectionpool.html>`__
    """

    props: PropsDictType = {
        "MaxRequests": (integer, True),
    }


class VirtualGatewayHttpConnectionPool(AWSProperty):
    """
    `VirtualGatewayHttpConnectionPool <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewayhttpconnectionpool.html>`__
    """

    props: PropsDictType = {
        "MaxConnections": (integer, True),
        "MaxPendingRequests": (integer, False),
    }


class VirtualGatewayConnectionPool(AWSProperty):
    """
    `VirtualGatewayConnectionPool <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewayconnectionpool.html>`__
    """

    props: PropsDictType = {
        "GRPC": (VirtualGatewayGrpcConnectionPool, False),
        "HTTP": (VirtualGatewayHttpConnectionPool, False),
        "HTTP2": (VirtualGatewayHttp2ConnectionPool, False),
    }


class VirtualGatewayHealthCheckPolicy(AWSProperty):
    """
    `VirtualGatewayHealthCheckPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewayhealthcheckpolicy.html>`__
    """

    props: PropsDictType = {
        "HealthyThreshold": (integer, True),
        "IntervalMillis": (integer, True),
        "Path": (str, False),
        "Port": (integer, False),
        "Protocol": (str, True),
        "TimeoutMillis": (integer, True),
        "UnhealthyThreshold": (integer, True),
    }


class VirtualGatewayListenerTlsAcmCertificate(AWSProperty):
    """
    `VirtualGatewayListenerTlsAcmCertificate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewaylistenertlsacmcertificate.html>`__
    """

    props: PropsDictType = {
        "CertificateArn": (str, True),
    }


class VirtualGatewayListenerTlsCertificate(AWSProperty):
    """
    `VirtualGatewayListenerTlsCertificate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewaylistenertlscertificate.html>`__
    """

    props: PropsDictType = {
        "ACM": (VirtualGatewayListenerTlsAcmCertificate, False),
        "File": (VirtualGatewayListenerTlsFileCertificate, False),
        "SDS": (VirtualGatewayListenerTlsSdsCertificate, False),
    }


class VirtualGatewayListenerTlsValidationContextTrust(AWSProperty):
    """
    `VirtualGatewayListenerTlsValidationContextTrust <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewaylistenertlsvalidationcontexttrust.html>`__
    """

    props: PropsDictType = {
        "File": (VirtualGatewayTlsValidationContextFileTrust, False),
        "SDS": (VirtualGatewayTlsValidationContextSdsTrust, False),
    }


class VirtualGatewayListenerTlsValidationContext(AWSProperty):
    """
    `VirtualGatewayListenerTlsValidationContext <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewaylistenertlsvalidationcontext.html>`__
    """

    props: PropsDictType = {
        "SubjectAlternativeNames": (SubjectAlternativeNames, False),
        "Trust": (VirtualGatewayListenerTlsValidationContextTrust, True),
    }


class VirtualGatewayListenerTls(AWSProperty):
    """
    `VirtualGatewayListenerTls <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewaylistenertls.html>`__
    """

    props: PropsDictType = {
        "Certificate": (VirtualGatewayListenerTlsCertificate, True),
        "Mode": (str, True),
        "Validation": (VirtualGatewayListenerTlsValidationContext, False),
    }


class VirtualGatewayPortMapping(AWSProperty):
    """
    `VirtualGatewayPortMapping <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewayportmapping.html>`__
    """

    props: PropsDictType = {
        "Port": (integer, True),
        "Protocol": (str, True),
    }


class VirtualGatewayListener(AWSProperty):
    """
    `VirtualGatewayListener <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewaylistener.html>`__
    """

    props: PropsDictType = {
        "ConnectionPool": (VirtualGatewayConnectionPool, False),
        "HealthCheck": (VirtualGatewayHealthCheckPolicy, False),
        "PortMapping": (VirtualGatewayPortMapping, True),
        "TLS": (VirtualGatewayListenerTls, False),
    }


class JsonFormatRef(AWSProperty):
    """
    `JsonFormatRef <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-jsonformatref.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (str, True),
    }


class LoggingFormat(AWSProperty):
    """
    `LoggingFormat <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-loggingformat.html>`__
    """

    props: PropsDictType = {
        "Json": ([JsonFormatRef], False),
        "Text": (str, False),
    }


class VirtualGatewayFileAccessLog(AWSProperty):
    """
    `VirtualGatewayFileAccessLog <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewayfileaccesslog.html>`__
    """

    props: PropsDictType = {
        "Format": (LoggingFormat, False),
        "Path": (str, True),
    }


class VirtualGatewayAccessLog(AWSProperty):
    """
    `VirtualGatewayAccessLog <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewayaccesslog.html>`__
    """

    props: PropsDictType = {
        "File": (VirtualGatewayFileAccessLog, False),
    }


class VirtualGatewayLogging(AWSProperty):
    """
    `VirtualGatewayLogging <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewaylogging.html>`__
    """

    props: PropsDictType = {
        "AccessLog": (VirtualGatewayAccessLog, False),
    }


class VirtualGatewaySpec(AWSProperty):
    """
    `VirtualGatewaySpec <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualgateway-virtualgatewayspec.html>`__
    """

    props: PropsDictType = {
        "BackendDefaults": (VirtualGatewayBackendDefaults, False),
        "Listeners": ([VirtualGatewayListener], True),
        "Logging": (VirtualGatewayLogging, False),
    }


class VirtualGateway(AWSObject):
    """
    `VirtualGateway <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appmesh-virtualgateway.html>`__
    """

    resource_type = "AWS::AppMesh::VirtualGateway"

    props: PropsDictType = {
        "MeshName": (str, True),
        "MeshOwner": (str, False),
        "Spec": (VirtualGatewaySpec, True),
        "Tags": (Tags, False),
        "VirtualGatewayName": (str, False),
    }


class ListenerTlsFileCertificate(AWSProperty):
    """
    `ListenerTlsFileCertificate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-listenertlsfilecertificate.html>`__
    """

    props: PropsDictType = {
        "CertificateChain": (str, True),
        "PrivateKey": (str, True),
    }


class ListenerTlsSdsCertificate(AWSProperty):
    """
    `ListenerTlsSdsCertificate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-listenertlssdscertificate.html>`__
    """

    props: PropsDictType = {
        "SecretName": (str, True),
    }


class ClientTlsCertificate(AWSProperty):
    """
    `ClientTlsCertificate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-clienttlscertificate.html>`__
    """

    props: PropsDictType = {
        "File": (ListenerTlsFileCertificate, False),
        "SDS": (ListenerTlsSdsCertificate, False),
    }


class TlsValidationContextAcmTrust(AWSProperty):
    """
    `TlsValidationContextAcmTrust <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-tlsvalidationcontextacmtrust.html>`__
    """

    props: PropsDictType = {
        "CertificateAuthorityArns": ([str], True),
    }


class TlsValidationContextFileTrust(AWSProperty):
    """
    `TlsValidationContextFileTrust <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-tlsvalidationcontextfiletrust.html>`__
    """

    props: PropsDictType = {
        "CertificateChain": (str, True),
    }


class TlsValidationContextSdsTrust(AWSProperty):
    """
    `TlsValidationContextSdsTrust <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-tlsvalidationcontextsdstrust.html>`__
    """

    props: PropsDictType = {
        "SecretName": (str, True),
    }


class TlsValidationContextTrust(AWSProperty):
    """
    `TlsValidationContextTrust <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-tlsvalidationcontexttrust.html>`__
    """

    props: PropsDictType = {
        "ACM": (TlsValidationContextAcmTrust, False),
        "File": (TlsValidationContextFileTrust, False),
        "SDS": (TlsValidationContextSdsTrust, False),
    }


class TlsValidationContext(AWSProperty):
    """
    `TlsValidationContext <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-tlsvalidationcontext.html>`__
    """

    props: PropsDictType = {
        "SubjectAlternativeNames": (SubjectAlternativeNames, False),
        "Trust": (TlsValidationContextTrust, True),
    }


class ClientPolicyTls(AWSProperty):
    """
    `ClientPolicyTls <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-clientpolicytls.html>`__
    """

    props: PropsDictType = {
        "Certificate": (ClientTlsCertificate, False),
        "Enforce": (boolean, False),
        "Ports": ([integer], False),
        "Validation": (TlsValidationContext, True),
    }


class ClientPolicy(AWSProperty):
    """
    `ClientPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-clientpolicy.html>`__
    """

    props: PropsDictType = {
        "TLS": (ClientPolicyTls, False),
    }


class VirtualServiceBackend(AWSProperty):
    """
    `VirtualServiceBackend <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-virtualservicebackend.html>`__
    """

    props: PropsDictType = {
        "ClientPolicy": (ClientPolicy, False),
        "VirtualServiceName": (str, True),
    }


class Backend(AWSProperty):
    """
    `Backend <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-backend.html>`__
    """

    props: PropsDictType = {
        "VirtualService": (VirtualServiceBackend, False),
    }


class BackendDefaults(AWSProperty):
    """
    `BackendDefaults <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-backenddefaults.html>`__
    """

    props: PropsDictType = {
        "ClientPolicy": (ClientPolicy, False),
    }


class HealthCheck(AWSProperty):
    """
    `HealthCheck <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-healthcheck.html>`__
    """

    props: PropsDictType = {
        "HealthyThreshold": (integer, True),
        "IntervalMillis": (integer, True),
        "Path": (str, False),
        "Port": (integer, False),
        "Protocol": (str, True),
        "TimeoutMillis": (integer, True),
        "UnhealthyThreshold": (integer, True),
    }


class ListenerTimeout(AWSProperty):
    """
    `ListenerTimeout <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-listenertimeout.html>`__
    """

    props: PropsDictType = {
        "GRPC": (GrpcTimeout, False),
        "HTTP": (HttpTimeout, False),
        "HTTP2": (HttpTimeout, False),
        "TCP": (TcpTimeout, False),
    }


class ListenerTlsAcmCertificate(AWSProperty):
    """
    `ListenerTlsAcmCertificate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-listenertlsacmcertificate.html>`__
    """

    props: PropsDictType = {
        "CertificateArn": (str, True),
    }


class ListenerTlsCertificate(AWSProperty):
    """
    `ListenerTlsCertificate <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-listenertlscertificate.html>`__
    """

    props: PropsDictType = {
        "ACM": (ListenerTlsAcmCertificate, False),
        "File": (ListenerTlsFileCertificate, False),
        "SDS": (ListenerTlsSdsCertificate, False),
    }


class ListenerTlsValidationContextTrust(AWSProperty):
    """
    `ListenerTlsValidationContextTrust <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-listenertlsvalidationcontexttrust.html>`__
    """

    props: PropsDictType = {
        "File": (TlsValidationContextFileTrust, False),
        "SDS": (TlsValidationContextSdsTrust, False),
    }


class ListenerTlsValidationContext(AWSProperty):
    """
    `ListenerTlsValidationContext <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-listenertlsvalidationcontext.html>`__
    """

    props: PropsDictType = {
        "SubjectAlternativeNames": (SubjectAlternativeNames, False),
        "Trust": (ListenerTlsValidationContextTrust, True),
    }


class ListenerTls(AWSProperty):
    """
    `ListenerTls <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-listenertls.html>`__
    """

    props: PropsDictType = {
        "Certificate": (ListenerTlsCertificate, True),
        "Mode": (validate_listenertls_mode, True),
        "Validation": (ListenerTlsValidationContext, False),
    }


class OutlierDetection(AWSProperty):
    """
    `OutlierDetection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-outlierdetection.html>`__
    """

    props: PropsDictType = {
        "BaseEjectionDuration": (Duration, True),
        "Interval": (Duration, True),
        "MaxEjectionPercent": (integer, True),
        "MaxServerErrors": (integer, True),
    }


class PortMapping(AWSProperty):
    """
    `PortMapping <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualrouter-portmapping.html>`__
    """

    props: PropsDictType = {
        "Port": (integer, True),
        "Protocol": (str, True),
    }


class VirtualNodeGrpcConnectionPool(AWSProperty):
    """
    `VirtualNodeGrpcConnectionPool <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-virtualnodegrpcconnectionpool.html>`__
    """

    props: PropsDictType = {
        "MaxRequests": (integer, True),
    }


class VirtualNodeHttp2ConnectionPool(AWSProperty):
    """
    `VirtualNodeHttp2ConnectionPool <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-virtualnodehttp2connectionpool.html>`__
    """

    props: PropsDictType = {
        "MaxRequests": (integer, True),
    }


class VirtualNodeHttpConnectionPool(AWSProperty):
    """
    `VirtualNodeHttpConnectionPool <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-virtualnodehttpconnectionpool.html>`__
    """

    props: PropsDictType = {
        "MaxConnections": (integer, True),
        "MaxPendingRequests": (integer, False),
    }


class VirtualNodeTcpConnectionPool(AWSProperty):
    """
    `VirtualNodeTcpConnectionPool <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-virtualnodetcpconnectionpool.html>`__
    """

    props: PropsDictType = {
        "MaxConnections": (integer, True),
    }


class VirtualNodeConnectionPool(AWSProperty):
    """
    `VirtualNodeConnectionPool <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-virtualnodeconnectionpool.html>`__
    """

    props: PropsDictType = {
        "GRPC": (VirtualNodeGrpcConnectionPool, False),
        "HTTP": (VirtualNodeHttpConnectionPool, False),
        "HTTP2": (VirtualNodeHttp2ConnectionPool, False),
        "TCP": (VirtualNodeTcpConnectionPool, False),
    }


class Listener(AWSProperty):
    """
    `Listener <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-listener.html>`__
    """

    props: PropsDictType = {
        "ConnectionPool": (VirtualNodeConnectionPool, False),
        "HealthCheck": (HealthCheck, False),
        "OutlierDetection": (OutlierDetection, False),
        "PortMapping": (PortMapping, True),
        "TLS": (ListenerTls, False),
        "Timeout": (ListenerTimeout, False),
    }


class FileAccessLog(AWSProperty):
    """
    `FileAccessLog <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-fileaccesslog.html>`__
    """

    props: PropsDictType = {
        "Format": (LoggingFormat, False),
        "Path": (str, True),
    }


class AccessLog(AWSProperty):
    """
    `AccessLog <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-accesslog.html>`__
    """

    props: PropsDictType = {
        "File": (FileAccessLog, False),
    }


class Logging(AWSProperty):
    """
    `Logging <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-logging.html>`__
    """

    props: PropsDictType = {
        "AccessLog": (AccessLog, False),
    }


class AwsCloudMapInstanceAttribute(AWSProperty):
    """
    `AwsCloudMapInstanceAttribute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-awscloudmapinstanceattribute.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (str, True),
    }


class AwsCloudMapServiceDiscovery(AWSProperty):
    """
    `AwsCloudMapServiceDiscovery <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-awscloudmapservicediscovery.html>`__
    """

    props: PropsDictType = {
        "Attributes": ([AwsCloudMapInstanceAttribute], False),
        "IpPreference": (str, False),
        "NamespaceName": (str, True),
        "ServiceName": (str, True),
    }


class DnsServiceDiscovery(AWSProperty):
    """
    `DnsServiceDiscovery <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-dnsservicediscovery.html>`__
    """

    props: PropsDictType = {
        "Hostname": (str, True),
        "IpPreference": (str, False),
        "ResponseType": (str, False),
    }


class ServiceDiscovery(AWSProperty):
    """
    `ServiceDiscovery <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-servicediscovery.html>`__
    """

    props: PropsDictType = {
        "AWSCloudMap": (AwsCloudMapServiceDiscovery, False),
        "DNS": (DnsServiceDiscovery, False),
    }


class VirtualNodeSpec(AWSProperty):
    """
    `VirtualNodeSpec <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualnode-virtualnodespec.html>`__
    """

    props: PropsDictType = {
        "BackendDefaults": (BackendDefaults, False),
        "Backends": ([Backend], False),
        "Listeners": ([Listener], False),
        "Logging": (Logging, False),
        "ServiceDiscovery": (ServiceDiscovery, False),
    }


class VirtualNode(AWSObject):
    """
    `VirtualNode <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appmesh-virtualnode.html>`__
    """

    resource_type = "AWS::AppMesh::VirtualNode"

    props: PropsDictType = {
        "MeshName": (str, True),
        "MeshOwner": (str, False),
        "Spec": (VirtualNodeSpec, True),
        "Tags": (Tags, False),
        "VirtualNodeName": (str, False),
    }


class VirtualRouterListener(AWSProperty):
    """
    `VirtualRouterListener <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualrouter-virtualrouterlistener.html>`__
    """

    props: PropsDictType = {
        "PortMapping": (PortMapping, True),
    }


class VirtualRouterSpec(AWSProperty):
    """
    `VirtualRouterSpec <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualrouter-virtualrouterspec.html>`__
    """

    props: PropsDictType = {
        "Listeners": ([VirtualRouterListener], True),
    }


class VirtualRouter(AWSObject):
    """
    `VirtualRouter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appmesh-virtualrouter.html>`__
    """

    resource_type = "AWS::AppMesh::VirtualRouter"

    props: PropsDictType = {
        "MeshName": (str, True),
        "MeshOwner": (str, False),
        "Spec": (VirtualRouterSpec, True),
        "Tags": (Tags, False),
        "VirtualRouterName": (str, False),
    }


class VirtualNodeServiceProvider(AWSProperty):
    """
    `VirtualNodeServiceProvider <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualservice-virtualnodeserviceprovider.html>`__
    """

    props: PropsDictType = {
        "VirtualNodeName": (str, True),
    }


class VirtualRouterServiceProvider(AWSProperty):
    """
    `VirtualRouterServiceProvider <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualservice-virtualrouterserviceprovider.html>`__
    """

    props: PropsDictType = {
        "VirtualRouterName": (str, True),
    }


class VirtualServiceProvider(AWSProperty):
    """
    `VirtualServiceProvider <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualservice-virtualserviceprovider.html>`__
    """

    props: PropsDictType = {
        "VirtualNode": (VirtualNodeServiceProvider, False),
        "VirtualRouter": (VirtualRouterServiceProvider, False),
    }


class VirtualServiceSpec(AWSProperty):
    """
    `VirtualServiceSpec <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appmesh-virtualservice-virtualservicespec.html>`__
    """

    props: PropsDictType = {
        "Provider": (VirtualServiceProvider, False),
    }


class VirtualService(AWSObject):
    """
    `VirtualService <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appmesh-virtualservice.html>`__
    """

    resource_type = "AWS::AppMesh::VirtualService"

    props: PropsDictType = {
        "MeshName": (str, True),
        "MeshOwner": (str, False),
        "Spec": (VirtualServiceSpec, True),
        "Tags": (Tags, False),
        "VirtualServiceName": (str, True),
    }
