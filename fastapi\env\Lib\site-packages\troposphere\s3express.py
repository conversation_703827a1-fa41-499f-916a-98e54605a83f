# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType
from .validators import boolean, integer


class BucketPolicy(AWSObject):
    """
    `BucketPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-bucketpolicy.html>`__
    """

    resource_type = "AWS::S3Express::BucketPolicy"

    props: PropsDictType = {
        "Bucket": (str, True),
        "PolicyDocument": (dict, True),
    }


class ServerSideEncryptionByDefault(AWSProperty):
    """
    `ServerSideEncryptionByDefault <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-serversideencryptionbydefault.html>`__
    """

    props: PropsDictType = {
        "KMSMasterKeyID": (str, False),
        "SSEAlgorithm": (str, True),
    }


class ServerSideEncryptionRule(AWSProperty):
    """
    `ServerSideEncryptionRule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-serversideencryptionrule.html>`__
    """

    props: PropsDictType = {
        "BucketKeyEnabled": (boolean, False),
        "ServerSideEncryptionByDefault": (ServerSideEncryptionByDefault, False),
    }


class BucketEncryption(AWSProperty):
    """
    `BucketEncryption <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-bucketencryption.html>`__
    """

    props: PropsDictType = {
        "ServerSideEncryptionConfiguration": ([ServerSideEncryptionRule], True),
    }


class AbortIncompleteMultipartUpload(AWSProperty):
    """
    `AbortIncompleteMultipartUpload <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-abortincompletemultipartupload.html>`__
    """

    props: PropsDictType = {
        "DaysAfterInitiation": (integer, True),
    }


class Rule(AWSProperty):
    """
    `Rule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-rule.html>`__
    """

    props: PropsDictType = {
        "AbortIncompleteMultipartUpload": (AbortIncompleteMultipartUpload, False),
        "ExpirationInDays": (integer, False),
        "Id": (str, False),
        "ObjectSizeGreaterThan": (str, False),
        "ObjectSizeLessThan": (str, False),
        "Prefix": (str, False),
        "Status": (str, True),
    }


class LifecycleConfiguration(AWSProperty):
    """
    `LifecycleConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-s3express-directorybucket-lifecycleconfiguration.html>`__
    """

    props: PropsDictType = {
        "Rules": ([Rule], True),
    }


class DirectoryBucket(AWSObject):
    """
    `DirectoryBucket <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-s3express-directorybucket.html>`__
    """

    resource_type = "AWS::S3Express::DirectoryBucket"

    props: PropsDictType = {
        "BucketEncryption": (BucketEncryption, False),
        "BucketName": (str, False),
        "DataRedundancy": (str, True),
        "LifecycleConfiguration": (LifecycleConfiguration, False),
        "LocationName": (str, True),
    }
