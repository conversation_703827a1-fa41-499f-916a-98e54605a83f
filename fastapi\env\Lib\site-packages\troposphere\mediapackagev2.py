# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer


class InputSwitchConfiguration(AWSProperty):
    """
    `InputSwitchConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-channel-inputswitchconfiguration.html>`__
    """

    props: PropsDictType = {
        "MQCSInputSwitching": (boolean, False),
    }


class OutputHeaderConfiguration(AWSProperty):
    """
    `OutputHeaderConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-channel-outputheaderconfiguration.html>`__
    """

    props: PropsDictType = {
        "PublishMQCS": (boolean, False),
    }


class Channel(AWSObject):
    """
    `Channel <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-mediapackagev2-channel.html>`__
    """

    resource_type = "AWS::MediaPackageV2::Channel"

    props: PropsDictType = {
        "ChannelGroupName": (str, True),
        "ChannelName": (str, True),
        "Description": (str, False),
        "InputSwitchConfiguration": (InputSwitchConfiguration, False),
        "InputType": (str, False),
        "OutputHeaderConfiguration": (OutputHeaderConfiguration, False),
        "Tags": (Tags, False),
    }


class ChannelGroup(AWSObject):
    """
    `ChannelGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-mediapackagev2-channelgroup.html>`__
    """

    resource_type = "AWS::MediaPackageV2::ChannelGroup"

    props: PropsDictType = {
        "ChannelGroupName": (str, True),
        "Description": (str, False),
        "Tags": (Tags, False),
    }


class ChannelPolicy(AWSObject):
    """
    `ChannelPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-mediapackagev2-channelpolicy.html>`__
    """

    resource_type = "AWS::MediaPackageV2::ChannelPolicy"

    props: PropsDictType = {
        "ChannelGroupName": (str, True),
        "ChannelName": (str, True),
        "Policy": (dict, True),
    }


class DashUtcTiming(AWSProperty):
    """
    `DashUtcTiming <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-originendpoint-dashutctiming.html>`__
    """

    props: PropsDictType = {
        "TimingMode": (str, False),
        "TimingSource": (str, False),
    }


class FilterConfiguration(AWSProperty):
    """
    `FilterConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-originendpoint-filterconfiguration.html>`__
    """

    props: PropsDictType = {
        "ClipStartTime": (str, False),
        "End": (str, False),
        "ManifestFilter": (str, False),
        "Start": (str, False),
        "TimeDelaySeconds": (integer, False),
    }


class ScteDash(AWSProperty):
    """
    `ScteDash <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-originendpoint-sctedash.html>`__
    """

    props: PropsDictType = {
        "AdMarkerDash": (str, False),
    }


class DashManifestConfiguration(AWSProperty):
    """
    `DashManifestConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-originendpoint-dashmanifestconfiguration.html>`__
    """

    props: PropsDictType = {
        "DrmSignaling": (str, False),
        "FilterConfiguration": (FilterConfiguration, False),
        "ManifestName": (str, True),
        "ManifestWindowSeconds": (integer, False),
        "MinBufferTimeSeconds": (integer, False),
        "MinUpdatePeriodSeconds": (integer, False),
        "PeriodTriggers": ([str], False),
        "ScteDash": (ScteDash, False),
        "SegmentTemplateFormat": (str, False),
        "SuggestedPresentationDelaySeconds": (integer, False),
        "UtcTiming": (DashUtcTiming, False),
    }


class ForceEndpointErrorConfiguration(AWSProperty):
    """
    `ForceEndpointErrorConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-originendpoint-forceendpointerrorconfiguration.html>`__
    """

    props: PropsDictType = {
        "EndpointErrorConditions": ([str], False),
    }


class ScteHls(AWSProperty):
    """
    `ScteHls <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-originendpoint-sctehls.html>`__
    """

    props: PropsDictType = {
        "AdMarkerHls": (str, False),
    }


class StartTag(AWSProperty):
    """
    `StartTag <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-originendpoint-starttag.html>`__
    """

    props: PropsDictType = {
        "Precise": (boolean, False),
        "TimeOffset": (double, True),
    }


class HlsManifestConfiguration(AWSProperty):
    """
    `HlsManifestConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-originendpoint-hlsmanifestconfiguration.html>`__
    """

    props: PropsDictType = {
        "ChildManifestName": (str, False),
        "FilterConfiguration": (FilterConfiguration, False),
        "ManifestName": (str, True),
        "ManifestWindowSeconds": (integer, False),
        "ProgramDateTimeIntervalSeconds": (integer, False),
        "ScteHls": (ScteHls, False),
        "StartTag": (StartTag, False),
        "Url": (str, False),
    }


class LowLatencyHlsManifestConfiguration(AWSProperty):
    """
    `LowLatencyHlsManifestConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-originendpoint-lowlatencyhlsmanifestconfiguration.html>`__
    """

    props: PropsDictType = {
        "ChildManifestName": (str, False),
        "FilterConfiguration": (FilterConfiguration, False),
        "ManifestName": (str, True),
        "ManifestWindowSeconds": (integer, False),
        "ProgramDateTimeIntervalSeconds": (integer, False),
        "ScteHls": (ScteHls, False),
        "StartTag": (StartTag, False),
        "Url": (str, False),
    }


class EncryptionMethod(AWSProperty):
    """
    `EncryptionMethod <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-originendpoint-encryptionmethod.html>`__
    """

    props: PropsDictType = {
        "CmafEncryptionMethod": (str, False),
        "TsEncryptionMethod": (str, False),
    }


class EncryptionContractConfiguration(AWSProperty):
    """
    `EncryptionContractConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-originendpoint-encryptioncontractconfiguration.html>`__
    """

    props: PropsDictType = {
        "PresetSpeke20Audio": (str, True),
        "PresetSpeke20Video": (str, True),
    }


class SpekeKeyProvider(AWSProperty):
    """
    `SpekeKeyProvider <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-originendpoint-spekekeyprovider.html>`__
    """

    props: PropsDictType = {
        "DrmSystems": ([str], True),
        "EncryptionContractConfiguration": (EncryptionContractConfiguration, True),
        "ResourceId": (str, True),
        "RoleArn": (str, True),
        "Url": (str, True),
    }


class Encryption(AWSProperty):
    """
    `Encryption <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-originendpoint-encryption.html>`__
    """

    props: PropsDictType = {
        "ConstantInitializationVector": (str, False),
        "EncryptionMethod": (EncryptionMethod, True),
        "KeyRotationIntervalSeconds": (integer, False),
        "SpekeKeyProvider": (SpekeKeyProvider, True),
    }


class Scte(AWSProperty):
    """
    `Scte <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-originendpoint-scte.html>`__
    """

    props: PropsDictType = {
        "ScteFilter": ([str], False),
    }


class Segment(AWSProperty):
    """
    `Segment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-originendpoint-segment.html>`__
    """

    props: PropsDictType = {
        "Encryption": (Encryption, False),
        "IncludeIframeOnlyStreams": (boolean, False),
        "Scte": (Scte, False),
        "SegmentDurationSeconds": (integer, False),
        "SegmentName": (str, False),
        "TsIncludeDvbSubtitles": (boolean, False),
        "TsUseAudioRenditionGroup": (boolean, False),
    }


class OriginEndpoint(AWSObject):
    """
    `OriginEndpoint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-mediapackagev2-originendpoint.html>`__
    """

    resource_type = "AWS::MediaPackageV2::OriginEndpoint"

    props: PropsDictType = {
        "ChannelGroupName": (str, True),
        "ChannelName": (str, True),
        "ContainerType": (str, True),
        "DashManifests": ([DashManifestConfiguration], False),
        "Description": (str, False),
        "ForceEndpointErrorConfiguration": (ForceEndpointErrorConfiguration, False),
        "HlsManifests": ([HlsManifestConfiguration], False),
        "LowLatencyHlsManifests": ([LowLatencyHlsManifestConfiguration], False),
        "OriginEndpointName": (str, True),
        "Segment": (Segment, False),
        "StartoverWindowSeconds": (integer, False),
        "Tags": (Tags, False),
    }


class OriginEndpointPolicy(AWSObject):
    """
    `OriginEndpointPolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-mediapackagev2-originendpointpolicy.html>`__
    """

    resource_type = "AWS::MediaPackageV2::OriginEndpointPolicy"

    props: PropsDictType = {
        "ChannelGroupName": (str, True),
        "ChannelName": (str, True),
        "OriginEndpointName": (str, True),
        "Policy": (dict, True),
    }


class IngestEndpoint(AWSProperty):
    """
    `IngestEndpoint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-mediapackagev2-channel-ingestendpoint.html>`__
    """

    props: PropsDictType = {
        "Id": (str, False),
        "Url": (str, False),
    }
