# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType


class AutoshiftObserverNotificationStatus(AWSObject):
    """
    `AutoshiftObserverNotificationStatus <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-arczonalshift-autoshiftobservernotificationstatus.html>`__
    """

    resource_type = "AWS::ARCZonalShift::AutoshiftObserverNotificationStatus"

    props: PropsDictType = {
        "Status": (str, True),
    }


class ControlCondition(AWSProperty):
    """
    `ControlCondition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-arczonalshift-zonalautoshiftconfiguration-controlcondition.html>`__
    """

    props: PropsDictType = {
        "AlarmIdentifier": (str, True),
        "Type": (str, True),
    }


class PracticeRunConfiguration(AWSProperty):
    """
    `PracticeRunConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-arczonalshift-zonalautoshiftconfiguration-practicerunconfiguration.html>`__
    """

    props: PropsDictType = {
        "BlockedDates": ([str], False),
        "BlockedWindows": ([str], False),
        "BlockingAlarms": ([ControlCondition], False),
        "OutcomeAlarms": ([ControlCondition], True),
    }


class ZonalAutoshiftConfiguration(AWSObject):
    """
    `ZonalAutoshiftConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-arczonalshift-zonalautoshiftconfiguration.html>`__
    """

    resource_type = "AWS::ARCZonalShift::ZonalAutoshiftConfiguration"

    props: PropsDictType = {
        "PracticeRunConfiguration": (PracticeRunConfiguration, False),
        "ResourceIdentifier": (str, True),
        "ZonalAutoshiftStatus": (str, False),
    }
