# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer


class LambdaConnectorProvisioningConfig(AWSProperty):
    """
    `LambdaConnectorProvisioningConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connector-lambdaconnectorprovisioningconfig.html>`__
    """

    props: PropsDictType = {
        "LambdaArn": (str, True),
    }


class ConnectorProvisioningConfig(AWSProperty):
    """
    `ConnectorProvisioningConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connector-connectorprovisioningconfig.html>`__
    """

    props: PropsDictType = {
        "Lambda": (LambdaConnectorProvisioningConfig, False),
    }


class Connector(AWSObject):
    """
    `Connector <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appflow-connector.html>`__
    """

    resource_type = "AWS::AppFlow::Connector"

    props: PropsDictType = {
        "ConnectorLabel": (str, False),
        "ConnectorProvisioningConfig": (ConnectorProvisioningConfig, True),
        "ConnectorProvisioningType": (str, True),
        "Description": (str, False),
    }


class AmplitudeConnectorProfileCredentials(AWSProperty):
    """
    `AmplitudeConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-amplitudeconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "ApiKey": (str, True),
        "SecretKey": (str, True),
    }


class ApiKeyCredentials(AWSProperty):
    """
    `ApiKeyCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-apikeycredentials.html>`__
    """

    props: PropsDictType = {
        "ApiKey": (str, True),
        "ApiSecretKey": (str, False),
    }


class BasicAuthCredentials(AWSProperty):
    """
    `BasicAuthCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-basicauthcredentials.html>`__
    """

    props: PropsDictType = {
        "Password": (str, True),
        "Username": (str, True),
    }


class CustomAuthCredentials(AWSProperty):
    """
    `CustomAuthCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-customauthcredentials.html>`__
    """

    props: PropsDictType = {
        "CredentialsMap": (dict, False),
        "CustomAuthenticationType": (str, True),
    }


class ConnectorOAuthRequest(AWSProperty):
    """
    `ConnectorOAuthRequest <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-connectoroauthrequest.html>`__
    """

    props: PropsDictType = {
        "AuthCode": (str, False),
        "RedirectUri": (str, False),
    }


class OAuth2Credentials(AWSProperty):
    """
    `OAuth2Credentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-oauth2credentials.html>`__
    """

    props: PropsDictType = {
        "AccessToken": (str, False),
        "ClientId": (str, False),
        "ClientSecret": (str, False),
        "OAuthRequest": (ConnectorOAuthRequest, False),
        "RefreshToken": (str, False),
    }


class CustomConnectorProfileCredentials(AWSProperty):
    """
    `CustomConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-customconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "ApiKey": (ApiKeyCredentials, False),
        "AuthenticationType": (str, True),
        "Basic": (BasicAuthCredentials, False),
        "Custom": (CustomAuthCredentials, False),
        "Oauth2": (OAuth2Credentials, False),
    }


class DatadogConnectorProfileCredentials(AWSProperty):
    """
    `DatadogConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-datadogconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "ApiKey": (str, True),
        "ApplicationKey": (str, True),
    }


class DynatraceConnectorProfileCredentials(AWSProperty):
    """
    `DynatraceConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-dynatraceconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "ApiToken": (str, True),
    }


class GoogleAnalyticsConnectorProfileCredentials(AWSProperty):
    """
    `GoogleAnalyticsConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-googleanalyticsconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "AccessToken": (str, False),
        "ClientId": (str, True),
        "ClientSecret": (str, True),
        "ConnectorOAuthRequest": (ConnectorOAuthRequest, False),
        "RefreshToken": (str, False),
    }


class InforNexusConnectorProfileCredentials(AWSProperty):
    """
    `InforNexusConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-infornexusconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "AccessKeyId": (str, True),
        "Datakey": (str, True),
        "SecretAccessKey": (str, True),
        "UserId": (str, True),
    }


class MarketoConnectorProfileCredentials(AWSProperty):
    """
    `MarketoConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-marketoconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "AccessToken": (str, False),
        "ClientId": (str, True),
        "ClientSecret": (str, True),
        "ConnectorOAuthRequest": (ConnectorOAuthRequest, False),
    }


class PardotConnectorProfileCredentials(AWSProperty):
    """
    `PardotConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-pardotconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "AccessToken": (str, False),
        "ClientCredentialsArn": (str, False),
        "ConnectorOAuthRequest": (ConnectorOAuthRequest, False),
        "RefreshToken": (str, False),
    }


class RedshiftConnectorProfileCredentials(AWSProperty):
    """
    `RedshiftConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-redshiftconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "Password": (str, False),
        "Username": (str, False),
    }


class OAuthCredentials(AWSProperty):
    """
    `OAuthCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-oauthcredentials.html>`__
    """

    props: PropsDictType = {
        "AccessToken": (str, False),
        "ClientId": (str, False),
        "ClientSecret": (str, False),
        "ConnectorOAuthRequest": (ConnectorOAuthRequest, False),
        "RefreshToken": (str, False),
    }


class SAPODataConnectorProfileCredentials(AWSProperty):
    """
    `SAPODataConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-sapodataconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "BasicAuthCredentials": (BasicAuthCredentials, False),
        "OAuthCredentials": (OAuthCredentials, False),
    }


class SalesforceConnectorProfileCredentials(AWSProperty):
    """
    `SalesforceConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-salesforceconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "AccessToken": (str, False),
        "ClientCredentialsArn": (str, False),
        "ConnectorOAuthRequest": (ConnectorOAuthRequest, False),
        "JwtToken": (str, False),
        "OAuth2GrantType": (str, False),
        "RefreshToken": (str, False),
    }


class ServiceNowConnectorProfileCredentials(AWSProperty):
    """
    `ServiceNowConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-servicenowconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "OAuth2Credentials": (OAuth2Credentials, False),
        "Password": (str, False),
        "Username": (str, False),
    }


class SingularConnectorProfileCredentials(AWSProperty):
    """
    `SingularConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-singularconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "ApiKey": (str, True),
    }


class SlackConnectorProfileCredentials(AWSProperty):
    """
    `SlackConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-slackconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "AccessToken": (str, False),
        "ClientId": (str, True),
        "ClientSecret": (str, True),
        "ConnectorOAuthRequest": (ConnectorOAuthRequest, False),
    }


class SnowflakeConnectorProfileCredentials(AWSProperty):
    """
    `SnowflakeConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-snowflakeconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "Password": (str, True),
        "Username": (str, True),
    }


class TrendmicroConnectorProfileCredentials(AWSProperty):
    """
    `TrendmicroConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-trendmicroconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "ApiSecretKey": (str, True),
    }


class VeevaConnectorProfileCredentials(AWSProperty):
    """
    `VeevaConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-veevaconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "Password": (str, True),
        "Username": (str, True),
    }


class ZendeskConnectorProfileCredentials(AWSProperty):
    """
    `ZendeskConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-zendeskconnectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "AccessToken": (str, False),
        "ClientId": (str, True),
        "ClientSecret": (str, True),
        "ConnectorOAuthRequest": (ConnectorOAuthRequest, False),
    }


class ConnectorProfileCredentials(AWSProperty):
    """
    `ConnectorProfileCredentials <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-connectorprofilecredentials.html>`__
    """

    props: PropsDictType = {
        "Amplitude": (AmplitudeConnectorProfileCredentials, False),
        "CustomConnector": (CustomConnectorProfileCredentials, False),
        "Datadog": (DatadogConnectorProfileCredentials, False),
        "Dynatrace": (DynatraceConnectorProfileCredentials, False),
        "GoogleAnalytics": (GoogleAnalyticsConnectorProfileCredentials, False),
        "InforNexus": (InforNexusConnectorProfileCredentials, False),
        "Marketo": (MarketoConnectorProfileCredentials, False),
        "Pardot": (PardotConnectorProfileCredentials, False),
        "Redshift": (RedshiftConnectorProfileCredentials, False),
        "SAPOData": (SAPODataConnectorProfileCredentials, False),
        "Salesforce": (SalesforceConnectorProfileCredentials, False),
        "ServiceNow": (ServiceNowConnectorProfileCredentials, False),
        "Singular": (SingularConnectorProfileCredentials, False),
        "Slack": (SlackConnectorProfileCredentials, False),
        "Snowflake": (SnowflakeConnectorProfileCredentials, False),
        "Trendmicro": (TrendmicroConnectorProfileCredentials, False),
        "Veeva": (VeevaConnectorProfileCredentials, False),
        "Zendesk": (ZendeskConnectorProfileCredentials, False),
    }


class OAuth2Properties(AWSProperty):
    """
    `OAuth2Properties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-oauth2properties.html>`__
    """

    props: PropsDictType = {
        "OAuth2GrantType": (str, False),
        "TokenUrl": (str, False),
        "TokenUrlCustomProperties": (dict, False),
    }


class CustomConnectorProfileProperties(AWSProperty):
    """
    `CustomConnectorProfileProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-customconnectorprofileproperties.html>`__
    """

    props: PropsDictType = {
        "OAuth2Properties": (OAuth2Properties, False),
        "ProfileProperties": (dict, False),
    }


class DatadogConnectorProfileProperties(AWSProperty):
    """
    `DatadogConnectorProfileProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-datadogconnectorprofileproperties.html>`__
    """

    props: PropsDictType = {
        "InstanceUrl": (str, True),
    }


class DynatraceConnectorProfileProperties(AWSProperty):
    """
    `DynatraceConnectorProfileProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-dynatraceconnectorprofileproperties.html>`__
    """

    props: PropsDictType = {
        "InstanceUrl": (str, True),
    }


class InforNexusConnectorProfileProperties(AWSProperty):
    """
    `InforNexusConnectorProfileProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-infornexusconnectorprofileproperties.html>`__
    """

    props: PropsDictType = {
        "InstanceUrl": (str, True),
    }


class MarketoConnectorProfileProperties(AWSProperty):
    """
    `MarketoConnectorProfileProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-marketoconnectorprofileproperties.html>`__
    """

    props: PropsDictType = {
        "InstanceUrl": (str, True),
    }


class PardotConnectorProfileProperties(AWSProperty):
    """
    `PardotConnectorProfileProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-pardotconnectorprofileproperties.html>`__
    """

    props: PropsDictType = {
        "BusinessUnitId": (str, True),
        "InstanceUrl": (str, False),
        "IsSandboxEnvironment": (boolean, False),
    }


class RedshiftConnectorProfileProperties(AWSProperty):
    """
    `RedshiftConnectorProfileProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-redshiftconnectorprofileproperties.html>`__
    """

    props: PropsDictType = {
        "BucketName": (str, True),
        "BucketPrefix": (str, False),
        "ClusterIdentifier": (str, False),
        "DataApiRoleArn": (str, False),
        "DatabaseName": (str, False),
        "DatabaseUrl": (str, False),
        "IsRedshiftServerless": (boolean, False),
        "RoleArn": (str, True),
        "WorkgroupName": (str, False),
    }


class OAuthProperties(AWSProperty):
    """
    `OAuthProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-oauthproperties.html>`__
    """

    props: PropsDictType = {
        "AuthCodeUrl": (str, False),
        "OAuthScopes": ([str], False),
        "TokenUrl": (str, False),
    }


class SAPODataConnectorProfileProperties(AWSProperty):
    """
    `SAPODataConnectorProfileProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-sapodataconnectorprofileproperties.html>`__
    """

    props: PropsDictType = {
        "ApplicationHostUrl": (str, False),
        "ApplicationServicePath": (str, False),
        "ClientNumber": (str, False),
        "DisableSSO": (boolean, False),
        "LogonLanguage": (str, False),
        "OAuthProperties": (OAuthProperties, False),
        "PortNumber": (integer, False),
        "PrivateLinkServiceName": (str, False),
    }


class SalesforceConnectorProfileProperties(AWSProperty):
    """
    `SalesforceConnectorProfileProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-salesforceconnectorprofileproperties.html>`__
    """

    props: PropsDictType = {
        "InstanceUrl": (str, False),
        "isSandboxEnvironment": (boolean, False),
        "usePrivateLinkForMetadataAndAuthorization": (boolean, False),
    }


class ServiceNowConnectorProfileProperties(AWSProperty):
    """
    `ServiceNowConnectorProfileProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-servicenowconnectorprofileproperties.html>`__
    """

    props: PropsDictType = {
        "InstanceUrl": (str, True),
    }


class SlackConnectorProfileProperties(AWSProperty):
    """
    `SlackConnectorProfileProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-slackconnectorprofileproperties.html>`__
    """

    props: PropsDictType = {
        "InstanceUrl": (str, True),
    }


class SnowflakeConnectorProfileProperties(AWSProperty):
    """
    `SnowflakeConnectorProfileProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-snowflakeconnectorprofileproperties.html>`__
    """

    props: PropsDictType = {
        "AccountName": (str, False),
        "BucketName": (str, True),
        "BucketPrefix": (str, False),
        "PrivateLinkServiceName": (str, False),
        "Region": (str, False),
        "Stage": (str, True),
        "Warehouse": (str, True),
    }


class VeevaConnectorProfileProperties(AWSProperty):
    """
    `VeevaConnectorProfileProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-veevaconnectorprofileproperties.html>`__
    """

    props: PropsDictType = {
        "InstanceUrl": (str, True),
    }


class ZendeskConnectorProfileProperties(AWSProperty):
    """
    `ZendeskConnectorProfileProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-zendeskconnectorprofileproperties.html>`__
    """

    props: PropsDictType = {
        "InstanceUrl": (str, True),
    }


class ConnectorProfileProperties(AWSProperty):
    """
    `ConnectorProfileProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-connectorprofileproperties.html>`__
    """

    props: PropsDictType = {
        "CustomConnector": (CustomConnectorProfileProperties, False),
        "Datadog": (DatadogConnectorProfileProperties, False),
        "Dynatrace": (DynatraceConnectorProfileProperties, False),
        "InforNexus": (InforNexusConnectorProfileProperties, False),
        "Marketo": (MarketoConnectorProfileProperties, False),
        "Pardot": (PardotConnectorProfileProperties, False),
        "Redshift": (RedshiftConnectorProfileProperties, False),
        "SAPOData": (SAPODataConnectorProfileProperties, False),
        "Salesforce": (SalesforceConnectorProfileProperties, False),
        "ServiceNow": (ServiceNowConnectorProfileProperties, False),
        "Slack": (SlackConnectorProfileProperties, False),
        "Snowflake": (SnowflakeConnectorProfileProperties, False),
        "Veeva": (VeevaConnectorProfileProperties, False),
        "Zendesk": (ZendeskConnectorProfileProperties, False),
    }


class ConnectorProfileConfig(AWSProperty):
    """
    `ConnectorProfileConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-connectorprofile-connectorprofileconfig.html>`__
    """

    props: PropsDictType = {
        "ConnectorProfileCredentials": (ConnectorProfileCredentials, False),
        "ConnectorProfileProperties": (ConnectorProfileProperties, False),
    }


class ConnectorProfile(AWSObject):
    """
    `ConnectorProfile <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appflow-connectorprofile.html>`__
    """

    resource_type = "AWS::AppFlow::ConnectorProfile"

    props: PropsDictType = {
        "ConnectionMode": (str, True),
        "ConnectorLabel": (str, False),
        "ConnectorProfileConfig": (ConnectorProfileConfig, False),
        "ConnectorProfileName": (str, True),
        "ConnectorType": (str, True),
        "KMSArn": (str, False),
    }


class ErrorHandlingConfig(AWSProperty):
    """
    `ErrorHandlingConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-errorhandlingconfig.html>`__
    """

    props: PropsDictType = {
        "BucketName": (str, False),
        "BucketPrefix": (str, False),
        "FailOnFirstError": (boolean, False),
    }


class CustomConnectorDestinationProperties(AWSProperty):
    """
    `CustomConnectorDestinationProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-customconnectordestinationproperties.html>`__
    """

    props: PropsDictType = {
        "CustomProperties": (dict, False),
        "EntityName": (str, True),
        "ErrorHandlingConfig": (ErrorHandlingConfig, False),
        "IdFieldNames": ([str], False),
        "WriteOperationType": (str, False),
    }


class EventBridgeDestinationProperties(AWSProperty):
    """
    `EventBridgeDestinationProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-eventbridgedestinationproperties.html>`__
    """

    props: PropsDictType = {
        "ErrorHandlingConfig": (ErrorHandlingConfig, False),
        "Object": (str, True),
    }


class LookoutMetricsDestinationProperties(AWSProperty):
    """
    `LookoutMetricsDestinationProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-lookoutmetricsdestinationproperties.html>`__
    """

    props: PropsDictType = {
        "Object": (str, False),
    }


class MarketoDestinationProperties(AWSProperty):
    """
    `MarketoDestinationProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-marketodestinationproperties.html>`__
    """

    props: PropsDictType = {
        "ErrorHandlingConfig": (ErrorHandlingConfig, False),
        "Object": (str, True),
    }


class RedshiftDestinationProperties(AWSProperty):
    """
    `RedshiftDestinationProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-redshiftdestinationproperties.html>`__
    """

    props: PropsDictType = {
        "BucketPrefix": (str, False),
        "ErrorHandlingConfig": (ErrorHandlingConfig, False),
        "IntermediateBucketName": (str, True),
        "Object": (str, True),
    }


class AggregationConfig(AWSProperty):
    """
    `AggregationConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-aggregationconfig.html>`__
    """

    props: PropsDictType = {
        "AggregationType": (str, False),
        "TargetFileSize": (integer, False),
    }


class PrefixConfig(AWSProperty):
    """
    `PrefixConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-prefixconfig.html>`__
    """

    props: PropsDictType = {
        "PathPrefixHierarchy": ([str], False),
        "PrefixFormat": (str, False),
        "PrefixType": (str, False),
    }


class S3OutputFormatConfig(AWSProperty):
    """
    `S3OutputFormatConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-s3outputformatconfig.html>`__
    """

    props: PropsDictType = {
        "AggregationConfig": (AggregationConfig, False),
        "FileType": (str, False),
        "PrefixConfig": (PrefixConfig, False),
        "PreserveSourceDataTyping": (boolean, False),
    }


class S3DestinationProperties(AWSProperty):
    """
    `S3DestinationProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-s3destinationproperties.html>`__
    """

    props: PropsDictType = {
        "BucketName": (str, True),
        "BucketPrefix": (str, False),
        "S3OutputFormatConfig": (S3OutputFormatConfig, False),
    }


class SuccessResponseHandlingConfig(AWSProperty):
    """
    `SuccessResponseHandlingConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-successresponsehandlingconfig.html>`__
    """

    props: PropsDictType = {
        "BucketName": (str, False),
        "BucketPrefix": (str, False),
    }


class SAPODataDestinationProperties(AWSProperty):
    """
    `SAPODataDestinationProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-sapodatadestinationproperties.html>`__
    """

    props: PropsDictType = {
        "ErrorHandlingConfig": (ErrorHandlingConfig, False),
        "IdFieldNames": ([str], False),
        "ObjectPath": (str, True),
        "SuccessResponseHandlingConfig": (SuccessResponseHandlingConfig, False),
        "WriteOperationType": (str, False),
    }


class SalesforceDestinationProperties(AWSProperty):
    """
    `SalesforceDestinationProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-salesforcedestinationproperties.html>`__
    """

    props: PropsDictType = {
        "DataTransferApi": (str, False),
        "ErrorHandlingConfig": (ErrorHandlingConfig, False),
        "IdFieldNames": ([str], False),
        "Object": (str, True),
        "WriteOperationType": (str, False),
    }


class SnowflakeDestinationProperties(AWSProperty):
    """
    `SnowflakeDestinationProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-snowflakedestinationproperties.html>`__
    """

    props: PropsDictType = {
        "BucketPrefix": (str, False),
        "ErrorHandlingConfig": (ErrorHandlingConfig, False),
        "IntermediateBucketName": (str, True),
        "Object": (str, True),
    }


class UpsolverS3OutputFormatConfig(AWSProperty):
    """
    `UpsolverS3OutputFormatConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-upsolvers3outputformatconfig.html>`__
    """

    props: PropsDictType = {
        "AggregationConfig": (AggregationConfig, False),
        "FileType": (str, False),
        "PrefixConfig": (PrefixConfig, True),
    }


class UpsolverDestinationProperties(AWSProperty):
    """
    `UpsolverDestinationProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-upsolverdestinationproperties.html>`__
    """

    props: PropsDictType = {
        "BucketName": (str, True),
        "BucketPrefix": (str, False),
        "S3OutputFormatConfig": (UpsolverS3OutputFormatConfig, True),
    }


class ZendeskDestinationProperties(AWSProperty):
    """
    `ZendeskDestinationProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-zendeskdestinationproperties.html>`__
    """

    props: PropsDictType = {
        "ErrorHandlingConfig": (ErrorHandlingConfig, False),
        "IdFieldNames": ([str], False),
        "Object": (str, True),
        "WriteOperationType": (str, False),
    }


class DestinationConnectorProperties(AWSProperty):
    """
    `DestinationConnectorProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-destinationconnectorproperties.html>`__
    """

    props: PropsDictType = {
        "CustomConnector": (CustomConnectorDestinationProperties, False),
        "EventBridge": (EventBridgeDestinationProperties, False),
        "LookoutMetrics": (LookoutMetricsDestinationProperties, False),
        "Marketo": (MarketoDestinationProperties, False),
        "Redshift": (RedshiftDestinationProperties, False),
        "S3": (S3DestinationProperties, False),
        "SAPOData": (SAPODataDestinationProperties, False),
        "Salesforce": (SalesforceDestinationProperties, False),
        "Snowflake": (SnowflakeDestinationProperties, False),
        "Upsolver": (UpsolverDestinationProperties, False),
        "Zendesk": (ZendeskDestinationProperties, False),
    }


class DestinationFlowConfig(AWSProperty):
    """
    `DestinationFlowConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-destinationflowconfig.html>`__
    """

    props: PropsDictType = {
        "ApiVersion": (str, False),
        "ConnectorProfileName": (str, False),
        "ConnectorType": (str, True),
        "DestinationConnectorProperties": (DestinationConnectorProperties, True),
    }


class GlueDataCatalog(AWSProperty):
    """
    `GlueDataCatalog <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-gluedatacatalog.html>`__
    """

    props: PropsDictType = {
        "DatabaseName": (str, True),
        "RoleArn": (str, True),
        "TablePrefix": (str, True),
    }


class MetadataCatalogConfig(AWSProperty):
    """
    `MetadataCatalogConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-metadatacatalogconfig.html>`__
    """

    props: PropsDictType = {
        "GlueDataCatalog": (GlueDataCatalog, False),
    }


class IncrementalPullConfig(AWSProperty):
    """
    `IncrementalPullConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-incrementalpullconfig.html>`__
    """

    props: PropsDictType = {
        "DatetimeTypeFieldName": (str, False),
    }


class AmplitudeSourceProperties(AWSProperty):
    """
    `AmplitudeSourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-amplitudesourceproperties.html>`__
    """

    props: PropsDictType = {
        "Object": (str, True),
    }


class DataTransferApi(AWSProperty):
    """
    `DataTransferApi <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-datatransferapi.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "Type": (str, True),
    }


class CustomConnectorSourceProperties(AWSProperty):
    """
    `CustomConnectorSourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-customconnectorsourceproperties.html>`__
    """

    props: PropsDictType = {
        "CustomProperties": (dict, False),
        "DataTransferApi": (DataTransferApi, False),
        "EntityName": (str, True),
    }


class DatadogSourceProperties(AWSProperty):
    """
    `DatadogSourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-datadogsourceproperties.html>`__
    """

    props: PropsDictType = {
        "Object": (str, True),
    }


class DynatraceSourceProperties(AWSProperty):
    """
    `DynatraceSourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-dynatracesourceproperties.html>`__
    """

    props: PropsDictType = {
        "Object": (str, True),
    }


class GoogleAnalyticsSourceProperties(AWSProperty):
    """
    `GoogleAnalyticsSourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-googleanalyticssourceproperties.html>`__
    """

    props: PropsDictType = {
        "Object": (str, True),
    }


class InforNexusSourceProperties(AWSProperty):
    """
    `InforNexusSourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-infornexussourceproperties.html>`__
    """

    props: PropsDictType = {
        "Object": (str, True),
    }


class MarketoSourceProperties(AWSProperty):
    """
    `MarketoSourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-marketosourceproperties.html>`__
    """

    props: PropsDictType = {
        "Object": (str, True),
    }


class PardotSourceProperties(AWSProperty):
    """
    `PardotSourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-pardotsourceproperties.html>`__
    """

    props: PropsDictType = {
        "Object": (str, True),
    }


class S3InputFormatConfig(AWSProperty):
    """
    `S3InputFormatConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-s3inputformatconfig.html>`__
    """

    props: PropsDictType = {
        "S3InputFileType": (str, False),
    }


class S3SourceProperties(AWSProperty):
    """
    `S3SourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-s3sourceproperties.html>`__
    """

    props: PropsDictType = {
        "BucketName": (str, True),
        "BucketPrefix": (str, True),
        "S3InputFormatConfig": (S3InputFormatConfig, False),
    }


class SAPODataPaginationConfig(AWSProperty):
    """
    `SAPODataPaginationConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-sapodatapaginationconfig.html>`__
    """

    props: PropsDictType = {
        "maxPageSize": (integer, True),
    }


class SAPODataParallelismConfig(AWSProperty):
    """
    `SAPODataParallelismConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-sapodataparallelismconfig.html>`__
    """

    props: PropsDictType = {
        "maxParallelism": (integer, True),
    }


class SAPODataSourceProperties(AWSProperty):
    """
    `SAPODataSourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-sapodatasourceproperties.html>`__
    """

    props: PropsDictType = {
        "ObjectPath": (str, True),
        "paginationConfig": (SAPODataPaginationConfig, False),
        "parallelismConfig": (SAPODataParallelismConfig, False),
    }


class SalesforceSourceProperties(AWSProperty):
    """
    `SalesforceSourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-salesforcesourceproperties.html>`__
    """

    props: PropsDictType = {
        "DataTransferApi": (str, False),
        "EnableDynamicFieldUpdate": (boolean, False),
        "IncludeDeletedRecords": (boolean, False),
        "Object": (str, True),
    }


class ServiceNowSourceProperties(AWSProperty):
    """
    `ServiceNowSourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-servicenowsourceproperties.html>`__
    """

    props: PropsDictType = {
        "Object": (str, True),
    }


class SingularSourceProperties(AWSProperty):
    """
    `SingularSourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-singularsourceproperties.html>`__
    """

    props: PropsDictType = {
        "Object": (str, True),
    }


class SlackSourceProperties(AWSProperty):
    """
    `SlackSourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-slacksourceproperties.html>`__
    """

    props: PropsDictType = {
        "Object": (str, True),
    }


class TrendmicroSourceProperties(AWSProperty):
    """
    `TrendmicroSourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-trendmicrosourceproperties.html>`__
    """

    props: PropsDictType = {
        "Object": (str, True),
    }


class VeevaSourceProperties(AWSProperty):
    """
    `VeevaSourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-veevasourceproperties.html>`__
    """

    props: PropsDictType = {
        "DocumentType": (str, False),
        "IncludeAllVersions": (boolean, False),
        "IncludeRenditions": (boolean, False),
        "IncludeSourceFiles": (boolean, False),
        "Object": (str, True),
    }


class ZendeskSourceProperties(AWSProperty):
    """
    `ZendeskSourceProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-zendesksourceproperties.html>`__
    """

    props: PropsDictType = {
        "Object": (str, True),
    }


class SourceConnectorProperties(AWSProperty):
    """
    `SourceConnectorProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-sourceconnectorproperties.html>`__
    """

    props: PropsDictType = {
        "Amplitude": (AmplitudeSourceProperties, False),
        "CustomConnector": (CustomConnectorSourceProperties, False),
        "Datadog": (DatadogSourceProperties, False),
        "Dynatrace": (DynatraceSourceProperties, False),
        "GoogleAnalytics": (GoogleAnalyticsSourceProperties, False),
        "InforNexus": (InforNexusSourceProperties, False),
        "Marketo": (MarketoSourceProperties, False),
        "Pardot": (PardotSourceProperties, False),
        "S3": (S3SourceProperties, False),
        "SAPOData": (SAPODataSourceProperties, False),
        "Salesforce": (SalesforceSourceProperties, False),
        "ServiceNow": (ServiceNowSourceProperties, False),
        "Singular": (SingularSourceProperties, False),
        "Slack": (SlackSourceProperties, False),
        "Trendmicro": (TrendmicroSourceProperties, False),
        "Veeva": (VeevaSourceProperties, False),
        "Zendesk": (ZendeskSourceProperties, False),
    }


class SourceFlowConfig(AWSProperty):
    """
    `SourceFlowConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-sourceflowconfig.html>`__
    """

    props: PropsDictType = {
        "ApiVersion": (str, False),
        "ConnectorProfileName": (str, False),
        "ConnectorType": (str, True),
        "IncrementalPullConfig": (IncrementalPullConfig, False),
        "SourceConnectorProperties": (SourceConnectorProperties, True),
    }


class ConnectorOperator(AWSProperty):
    """
    `ConnectorOperator <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-connectoroperator.html>`__
    """

    props: PropsDictType = {
        "Amplitude": (str, False),
        "CustomConnector": (str, False),
        "Datadog": (str, False),
        "Dynatrace": (str, False),
        "GoogleAnalytics": (str, False),
        "InforNexus": (str, False),
        "Marketo": (str, False),
        "Pardot": (str, False),
        "S3": (str, False),
        "SAPOData": (str, False),
        "Salesforce": (str, False),
        "ServiceNow": (str, False),
        "Singular": (str, False),
        "Slack": (str, False),
        "Trendmicro": (str, False),
        "Veeva": (str, False),
        "Zendesk": (str, False),
    }


class TaskPropertiesObject(AWSProperty):
    """
    `TaskPropertiesObject <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-taskpropertiesobject.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (str, True),
    }


class Task(AWSProperty):
    """
    `Task <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-task.html>`__
    """

    props: PropsDictType = {
        "ConnectorOperator": (ConnectorOperator, False),
        "DestinationField": (str, False),
        "SourceFields": ([str], True),
        "TaskProperties": ([TaskPropertiesObject], False),
        "TaskType": (str, True),
    }


class ScheduledTriggerProperties(AWSProperty):
    """
    `ScheduledTriggerProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-scheduledtriggerproperties.html>`__
    """

    props: PropsDictType = {
        "DataPullMode": (str, False),
        "FirstExecutionFrom": (double, False),
        "FlowErrorDeactivationThreshold": (integer, False),
        "ScheduleEndTime": (double, False),
        "ScheduleExpression": (str, True),
        "ScheduleOffset": (double, False),
        "ScheduleStartTime": (double, False),
        "TimeZone": (str, False),
    }


class TriggerConfig(AWSProperty):
    """
    `TriggerConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-appflow-flow-triggerconfig.html>`__
    """

    props: PropsDictType = {
        "TriggerProperties": (ScheduledTriggerProperties, False),
        "TriggerType": (str, True),
    }


class Flow(AWSObject):
    """
    `Flow <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-appflow-flow.html>`__
    """

    resource_type = "AWS::AppFlow::Flow"

    props: PropsDictType = {
        "Description": (str, False),
        "DestinationFlowConfigList": ([DestinationFlowConfig], True),
        "FlowName": (str, True),
        "FlowStatus": (str, False),
        "KMSArn": (str, False),
        "MetadataCatalogConfig": (MetadataCatalogConfig, False),
        "SourceFlowConfig": (SourceFlowConfig, True),
        "Tags": (Tags, False),
        "Tasks": ([Task], True),
        "TriggerConfig": (TriggerConfig, True),
    }
