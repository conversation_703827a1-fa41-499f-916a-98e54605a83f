# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags


class SignInOptions(AWSProperty):
    """
    `SignInOptions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sso-application-signinoptions.html>`__
    """

    props: PropsDictType = {
        "ApplicationUrl": (str, False),
        "Origin": (str, True),
    }


class PortalOptionsConfiguration(AWSProperty):
    """
    `PortalOptionsConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sso-application-portaloptionsconfiguration.html>`__
    """

    props: PropsDictType = {
        "SignInOptions": (SignInOptions, False),
        "Visibility": (str, False),
    }


class Application(AWSObject):
    """
    `Application <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sso-application.html>`__
    """

    resource_type = "AWS::SSO::Application"

    props: PropsDictType = {
        "ApplicationProviderArn": (str, True),
        "Description": (str, False),
        "InstanceArn": (str, True),
        "Name": (str, True),
        "PortalOptions": (PortalOptionsConfiguration, False),
        "Status": (str, False),
        "Tags": (Tags, False),
    }


class ApplicationAssignment(AWSObject):
    """
    `ApplicationAssignment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sso-applicationassignment.html>`__
    """

    resource_type = "AWS::SSO::ApplicationAssignment"

    props: PropsDictType = {
        "ApplicationArn": (str, True),
        "PrincipalId": (str, True),
        "PrincipalType": (str, True),
    }


class Assignment(AWSObject):
    """
    `Assignment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sso-assignment.html>`__
    """

    resource_type = "AWS::SSO::Assignment"

    props: PropsDictType = {
        "InstanceArn": (str, True),
        "PermissionSetArn": (str, True),
        "PrincipalId": (str, True),
        "PrincipalType": (str, True),
        "TargetId": (str, True),
        "TargetType": (str, True),
    }


class Instance(AWSObject):
    """
    `Instance <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sso-instance.html>`__
    """

    resource_type = "AWS::SSO::Instance"

    props: PropsDictType = {
        "Name": (str, False),
        "Tags": (Tags, False),
    }


class AccessControlAttributeValue(AWSProperty):
    """
    `AccessControlAttributeValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sso-instanceaccesscontrolattributeconfiguration-accesscontrolattributevalue.html>`__
    """

    props: PropsDictType = {
        "Source": ([str], True),
    }


class AccessControlAttribute(AWSProperty):
    """
    `AccessControlAttribute <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sso-instanceaccesscontrolattributeconfiguration-accesscontrolattribute.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (AccessControlAttributeValue, True),
    }


class InstanceAccessControlAttributeConfiguration(AWSObject):
    """
    `InstanceAccessControlAttributeConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sso-instanceaccesscontrolattributeconfiguration.html>`__
    """

    resource_type = "AWS::SSO::InstanceAccessControlAttributeConfiguration"

    props: PropsDictType = {
        "AccessControlAttributes": ([AccessControlAttribute], False),
        "InstanceArn": (str, True),
    }


class CustomerManagedPolicyReference(AWSProperty):
    """
    `CustomerManagedPolicyReference <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sso-permissionset-customermanagedpolicyreference.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "Path": (str, False),
    }


class PermissionsBoundary(AWSProperty):
    """
    `PermissionsBoundary <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sso-permissionset-permissionsboundary.html>`__
    """

    props: PropsDictType = {
        "CustomerManagedPolicyReference": (CustomerManagedPolicyReference, False),
        "ManagedPolicyArn": (str, False),
    }


class PermissionSet(AWSObject):
    """
    `PermissionSet <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sso-permissionset.html>`__
    """

    resource_type = "AWS::SSO::PermissionSet"

    props: PropsDictType = {
        "CustomerManagedPolicyReferences": ([CustomerManagedPolicyReference], False),
        "Description": (str, False),
        "InlinePolicy": (dict, False),
        "InstanceArn": (str, True),
        "ManagedPolicies": ([str], False),
        "Name": (str, True),
        "PermissionsBoundary": (PermissionsBoundary, False),
        "RelayStateType": (str, False),
        "SessionDuration": (str, False),
        "Tags": (Tags, False),
    }
