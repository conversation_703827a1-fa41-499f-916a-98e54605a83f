# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, PropsDictType, Tags
from .validators import integer


class SignalingChannel(AWSObject):
    """
    `SignalingChannel <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-kinesisvideo-signalingchannel.html>`__
    """

    resource_type = "AWS::KinesisVideo::SignalingChannel"

    props: PropsDictType = {
        "MessageTtlSeconds": (integer, False),
        "Name": (str, False),
        "Tags": (Tags, False),
        "Type": (str, False),
    }


class Stream(AWSObject):
    """
    `Stream <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-kinesisvideo-stream.html>`__
    """

    resource_type = "AWS::KinesisVideo::Stream"

    props: PropsDictType = {
        "DataRetentionInHours": (integer, False),
        "DeviceName": (str, False),
        "KmsKeyId": (str, False),
        "MediaType": (str, False),
        "Name": (str, False),
        "Tags": (Tags, False),
    }
