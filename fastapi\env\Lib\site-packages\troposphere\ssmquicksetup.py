# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType


class ConfigurationDefinition(AWSProperty):
    """
    `ConfigurationDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ssmquicksetup-configurationmanager-configurationdefinition.html>`__
    """

    props: PropsDictType = {
        "LocalDeploymentAdministrationRoleArn": (str, False),
        "LocalDeploymentExecutionRoleName": (str, False),
        "Parameters": (dict, True),
        "Type": (str, True),
        "TypeVersion": (str, False),
        "id": (str, False),
    }


class ConfigurationManager(AWSObject):
    """
    `ConfigurationManager <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-ssmquicksetup-configurationmanager.html>`__
    """

    resource_type = "AWS::SSMQuickSetup::ConfigurationManager"

    props: PropsDictType = {
        "ConfigurationDefinitions": ([ConfigurationDefinition], True),
        "Description": (str, False),
        "Name": (str, False),
        "Tags": (dict, False),
    }


class StatusSummary(AWSProperty):
    """
    `StatusSummary <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ssmquicksetup-configurationmanager-statussummary.html>`__
    """

    props: PropsDictType = {
        "LastUpdatedAt": (str, True),
        "Status": (str, False),
        "StatusDetails": (dict, False),
        "StatusMessage": (str, False),
        "StatusType": (str, True),
    }
