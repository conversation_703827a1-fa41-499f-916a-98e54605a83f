# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, integer


class EncryptionConfiguration(AWSProperty):
    """
    `EncryptionConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-stepfunctions-statemachine-encryptionconfiguration.html>`__
    """

    props: PropsDictType = {
        "KmsDataKeyReusePeriodSeconds": (integer, False),
        "KmsKeyId": (str, False),
        "Type": (str, True),
    }


class Activity(AWSObject):
    """
    `Activity <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-stepfunctions-activity.html>`__
    """

    resource_type = "AWS::StepFunctions::Activity"

    props: PropsDictType = {
        "EncryptionConfiguration": (EncryptionConfiguration, False),
        "Name": (str, True),
        "Tags": (Tags, False),
    }


class CloudWatchLogsLogGroup(AWSProperty):
    """
    `CloudWatchLogsLogGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-stepfunctions-statemachine-cloudwatchlogsloggroup.html>`__
    """

    props: PropsDictType = {
        "LogGroupArn": (str, False),
    }


class LogDestination(AWSProperty):
    """
    `LogDestination <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-stepfunctions-statemachine-logdestination.html>`__
    """

    props: PropsDictType = {
        "CloudWatchLogsLogGroup": (CloudWatchLogsLogGroup, False),
    }


class LoggingConfiguration(AWSProperty):
    """
    `LoggingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-stepfunctions-statemachine-loggingconfiguration.html>`__
    """

    props: PropsDictType = {
        "Destinations": ([LogDestination], False),
        "IncludeExecutionData": (boolean, False),
        "Level": (str, False),
    }


class S3Location(AWSProperty):
    """
    `S3Location <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-stepfunctions-statemachine-s3location.html>`__
    """

    props: PropsDictType = {
        "Bucket": (str, True),
        "Key": (str, True),
        "Version": (str, False),
    }


class TracingConfiguration(AWSProperty):
    """
    `TracingConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-stepfunctions-statemachine-tracingconfiguration.html>`__
    """

    props: PropsDictType = {
        "Enabled": (boolean, False),
    }


class StateMachine(AWSObject):
    """
    `StateMachine <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-stepfunctions-statemachine.html>`__
    """

    resource_type = "AWS::StepFunctions::StateMachine"

    props: PropsDictType = {
        "Definition": (dict, False),
        "DefinitionS3Location": (S3Location, False),
        "DefinitionString": (str, False),
        "DefinitionSubstitutions": (dict, False),
        "EncryptionConfiguration": (EncryptionConfiguration, False),
        "LoggingConfiguration": (LoggingConfiguration, False),
        "RoleArn": (str, True),
        "StateMachineName": (str, False),
        "StateMachineType": (str, False),
        "Tags": (Tags, False),
        "TracingConfiguration": (TracingConfiguration, False),
    }


class DeploymentPreference(AWSProperty):
    """
    `DeploymentPreference <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-stepfunctions-statemachinealias-deploymentpreference.html>`__
    """

    props: PropsDictType = {
        "Alarms": ([str], False),
        "Interval": (integer, False),
        "Percentage": (integer, False),
        "StateMachineVersionArn": (str, True),
        "Type": (str, True),
    }


class RoutingConfigurationVersion(AWSProperty):
    """
    `RoutingConfigurationVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-stepfunctions-statemachinealias-routingconfigurationversion.html>`__
    """

    props: PropsDictType = {
        "StateMachineVersionArn": (str, True),
        "Weight": (integer, True),
    }


class StateMachineAlias(AWSObject):
    """
    `StateMachineAlias <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-stepfunctions-statemachinealias.html>`__
    """

    resource_type = "AWS::StepFunctions::StateMachineAlias"

    props: PropsDictType = {
        "DeploymentPreference": (DeploymentPreference, False),
        "Description": (str, False),
        "Name": (str, False),
        "RoutingConfiguration": ([RoutingConfigurationVersion], False),
    }


class StateMachineVersion(AWSObject):
    """
    `StateMachineVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-stepfunctions-statemachineversion.html>`__
    """

    resource_type = "AWS::StepFunctions::StateMachineVersion"

    props: PropsDictType = {
        "Description": (str, False),
        "StateMachineArn": (str, True),
        "StateMachineRevisionId": (str, False),
    }
