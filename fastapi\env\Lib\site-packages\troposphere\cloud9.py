# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import integer


class Repository(AWSProperty):
    """
    `Repository <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-cloud9-environmentec2-repository.html>`__
    """

    props: PropsDictType = {
        "PathComponent": (str, True),
        "RepositoryUrl": (str, True),
    }


class EnvironmentEC2(AWSObject):
    """
    `EnvironmentEC2 <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-cloud9-environmentec2.html>`__
    """

    resource_type = "AWS::Cloud9::EnvironmentEC2"

    props: PropsDictType = {
        "AutomaticStopTimeMinutes": (integer, False),
        "ConnectionType": (str, False),
        "Description": (str, False),
        "ImageId": (str, True),
        "InstanceType": (str, True),
        "Name": (str, False),
        "OwnerArn": (str, False),
        "Repositories": ([Repository], False),
        "SubnetId": (str, False),
        "Tags": (Tags, False),
    }
