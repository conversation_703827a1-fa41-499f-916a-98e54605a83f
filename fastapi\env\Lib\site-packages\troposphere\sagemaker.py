# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer


class ResourceSpec(AWSProperty):
    """
    `ResourceSpec <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-resourcespec.html>`__
    """

    props: PropsDictType = {
        "InstanceType": (str, False),
        "LifecycleConfigArn": (str, False),
        "SageMakerImageArn": (str, False),
        "SageMakerImageVersionArn": (str, False),
    }


class App(AWSObject):
    """
    `App <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-app.html>`__
    """

    resource_type = "AWS::SageMaker::App"

    props: PropsDictType = {
        "AppName": (str, True),
        "AppType": (str, True),
        "DomainId": (str, True),
        "ResourceSpec": (ResourceSpec, False),
        "Tags": (Tags, False),
        "UserProfileName": (str, True),
    }


class CustomImageContainerEnvironmentVariable(AWSProperty):
    """
    `CustomImageContainerEnvironmentVariable <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-appimageconfig-customimagecontainerenvironmentvariable.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (str, True),
    }


class ContainerConfig(AWSProperty):
    """
    `ContainerConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-appimageconfig-containerconfig.html>`__
    """

    props: PropsDictType = {
        "ContainerArguments": ([str], False),
        "ContainerEntrypoint": ([str], False),
        "ContainerEnvironmentVariables": (
            [CustomImageContainerEnvironmentVariable],
            False,
        ),
    }


class CodeEditorAppImageConfig(AWSProperty):
    """
    `CodeEditorAppImageConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-appimageconfig-codeeditorappimageconfig.html>`__
    """

    props: PropsDictType = {
        "ContainerConfig": (ContainerConfig, False),
    }


class JupyterLabAppImageConfig(AWSProperty):
    """
    `JupyterLabAppImageConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-appimageconfig-jupyterlabappimageconfig.html>`__
    """

    props: PropsDictType = {
        "ContainerConfig": (ContainerConfig, False),
    }


class FileSystemConfig(AWSProperty):
    """
    `FileSystemConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-appimageconfig-filesystemconfig.html>`__
    """

    props: PropsDictType = {
        "DefaultGid": (integer, False),
        "DefaultUid": (integer, False),
        "MountPath": (str, False),
    }


class KernelSpec(AWSProperty):
    """
    `KernelSpec <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-appimageconfig-kernelspec.html>`__
    """

    props: PropsDictType = {
        "DisplayName": (str, False),
        "Name": (str, True),
    }


class KernelGatewayImageConfig(AWSProperty):
    """
    `KernelGatewayImageConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-appimageconfig-kernelgatewayimageconfig.html>`__
    """

    props: PropsDictType = {
        "FileSystemConfig": (FileSystemConfig, False),
        "KernelSpecs": ([KernelSpec], True),
    }


class AppImageConfig(AWSObject):
    """
    `AppImageConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-appimageconfig.html>`__
    """

    resource_type = "AWS::SageMaker::AppImageConfig"

    props: PropsDictType = {
        "AppImageConfigName": (str, True),
        "CodeEditorAppImageConfig": (CodeEditorAppImageConfig, False),
        "JupyterLabAppImageConfig": (JupyterLabAppImageConfig, False),
        "KernelGatewayImageConfig": (KernelGatewayImageConfig, False),
        "Tags": (Tags, False),
    }


class ClusterEbsVolumeConfig(AWSProperty):
    """
    `ClusterEbsVolumeConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-cluster-clusterebsvolumeconfig.html>`__
    """

    props: PropsDictType = {
        "VolumeSizeInGB": (integer, False),
    }


class ClusterInstanceStorageConfig(AWSProperty):
    """
    `ClusterInstanceStorageConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-cluster-clusterinstancestorageconfig.html>`__
    """

    props: PropsDictType = {
        "EbsVolumeConfig": (ClusterEbsVolumeConfig, False),
    }


class ClusterLifeCycleConfig(AWSProperty):
    """
    `ClusterLifeCycleConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-cluster-clusterlifecycleconfig.html>`__
    """

    props: PropsDictType = {
        "OnCreate": (str, True),
        "SourceS3Uri": (str, True),
    }


class VpcConfig(AWSProperty):
    """
    `VpcConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-vpcconfig.html>`__
    """

    props: PropsDictType = {
        "SecurityGroupIds": ([str], True),
        "Subnets": ([str], True),
    }


class ClusterInstanceGroup(AWSProperty):
    """
    `ClusterInstanceGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-cluster-clusterinstancegroup.html>`__
    """

    props: PropsDictType = {
        "CurrentCount": (integer, False),
        "ExecutionRole": (str, True),
        "InstanceCount": (integer, True),
        "InstanceGroupName": (str, True),
        "InstanceStorageConfigs": ([ClusterInstanceStorageConfig], False),
        "InstanceType": (str, True),
        "LifeCycleConfig": (ClusterLifeCycleConfig, True),
        "OnStartDeepHealthChecks": ([str], False),
        "OverrideVpcConfig": (VpcConfig, False),
        "ThreadsPerCore": (integer, False),
    }


class ClusterOrchestratorEksConfig(AWSProperty):
    """
    `ClusterOrchestratorEksConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-cluster-clusterorchestratoreksconfig.html>`__
    """

    props: PropsDictType = {
        "ClusterArn": (str, True),
    }


class Orchestrator(AWSProperty):
    """
    `Orchestrator <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-cluster-orchestrator.html>`__
    """

    props: PropsDictType = {
        "Eks": (ClusterOrchestratorEksConfig, True),
    }


class Cluster(AWSObject):
    """
    `Cluster <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-cluster.html>`__
    """

    resource_type = "AWS::SageMaker::Cluster"

    props: PropsDictType = {
        "ClusterName": (str, False),
        "InstanceGroups": ([ClusterInstanceGroup], True),
        "NodeRecovery": (str, False),
        "Orchestrator": (Orchestrator, False),
        "Tags": (Tags, False),
        "VpcConfig": (VpcConfig, False),
    }


class GitConfig(AWSProperty):
    """
    `GitConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-coderepository-gitconfig.html>`__
    """

    props: PropsDictType = {
        "Branch": (str, False),
        "RepositoryUrl": (str, True),
        "SecretArn": (str, False),
    }


class CodeRepository(AWSObject):
    """
    `CodeRepository <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-coderepository.html>`__
    """

    resource_type = "AWS::SageMaker::CodeRepository"

    props: PropsDictType = {
        "CodeRepositoryName": (str, False),
        "GitConfig": (GitConfig, True),
        "Tags": (Tags, False),
    }


class DataQualityAppSpecification(AWSProperty):
    """
    `DataQualityAppSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-dataqualityjobdefinition-dataqualityappspecification.html>`__
    """

    props: PropsDictType = {
        "ContainerArguments": ([str], False),
        "ContainerEntrypoint": ([str], False),
        "Environment": (dict, False),
        "ImageUri": (str, True),
        "PostAnalyticsProcessorSourceUri": (str, False),
        "RecordPreprocessorSourceUri": (str, False),
    }


class ConstraintsResource(AWSProperty):
    """
    `ConstraintsResource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-constraintsresource.html>`__
    """

    props: PropsDictType = {
        "S3Uri": (str, False),
    }


class StatisticsResource(AWSProperty):
    """
    `StatisticsResource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-statisticsresource.html>`__
    """

    props: PropsDictType = {
        "S3Uri": (str, False),
    }


class DataQualityBaselineConfig(AWSProperty):
    """
    `DataQualityBaselineConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-dataqualityjobdefinition-dataqualitybaselineconfig.html>`__
    """

    props: PropsDictType = {
        "BaseliningJobName": (str, False),
        "ConstraintsResource": (ConstraintsResource, False),
        "StatisticsResource": (StatisticsResource, False),
    }


class Csv(AWSProperty):
    """
    `Csv <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-csv.html>`__
    """

    props: PropsDictType = {
        "Header": (boolean, False),
    }


class Json(AWSProperty):
    """
    `Json <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-json.html>`__
    """

    props: PropsDictType = {
        "Line": (boolean, False),
    }


class DatasetFormat(AWSProperty):
    """
    `DatasetFormat <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-datasetformat.html>`__
    """

    props: PropsDictType = {
        "Csv": (Csv, False),
        "Json": (Json, False),
        "Parquet": (boolean, False),
    }


class BatchTransformInput(AWSProperty):
    """
    `BatchTransformInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-batchtransforminput.html>`__
    """

    props: PropsDictType = {
        "DataCapturedDestinationS3Uri": (str, True),
        "DatasetFormat": (DatasetFormat, True),
        "ExcludeFeaturesAttribute": (str, False),
        "LocalPath": (str, True),
        "S3DataDistributionType": (str, False),
        "S3InputMode": (str, False),
    }


class EndpointInput(AWSProperty):
    """
    `EndpointInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-endpointinput.html>`__
    """

    props: PropsDictType = {
        "EndpointName": (str, True),
        "ExcludeFeaturesAttribute": (str, False),
        "LocalPath": (str, True),
        "S3DataDistributionType": (str, False),
        "S3InputMode": (str, False),
    }


class DataQualityJobInput(AWSProperty):
    """
    `DataQualityJobInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-dataqualityjobdefinition-dataqualityjobinput.html>`__
    """

    props: PropsDictType = {
        "BatchTransformInput": (BatchTransformInput, False),
        "EndpointInput": (EndpointInput, False),
    }


class S3Output(AWSProperty):
    """
    `S3Output <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-s3output.html>`__
    """

    props: PropsDictType = {
        "LocalPath": (str, True),
        "S3UploadMode": (str, False),
        "S3Uri": (str, True),
    }


class MonitoringOutput(AWSProperty):
    """
    `MonitoringOutput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-monitoringoutput.html>`__
    """

    props: PropsDictType = {
        "S3Output": (S3Output, True),
    }


class MonitoringOutputConfig(AWSProperty):
    """
    `MonitoringOutputConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-monitoringoutputconfig.html>`__
    """

    props: PropsDictType = {
        "KmsKeyId": (str, False),
        "MonitoringOutputs": ([MonitoringOutput], True),
    }


class ClusterConfig(AWSProperty):
    """
    `ClusterConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-clusterconfig.html>`__
    """

    props: PropsDictType = {
        "InstanceCount": (integer, True),
        "InstanceType": (str, True),
        "VolumeKmsKeyId": (str, False),
        "VolumeSizeInGB": (integer, True),
    }


class MonitoringResources(AWSProperty):
    """
    `MonitoringResources <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-monitoringresources.html>`__
    """

    props: PropsDictType = {
        "ClusterConfig": (ClusterConfig, True),
    }


class NetworkConfig(AWSProperty):
    """
    `NetworkConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-networkconfig.html>`__
    """

    props: PropsDictType = {
        "EnableInterContainerTrafficEncryption": (boolean, False),
        "EnableNetworkIsolation": (boolean, False),
        "VpcConfig": (VpcConfig, False),
    }


class StoppingCondition(AWSProperty):
    """
    `StoppingCondition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-stoppingcondition.html>`__
    """

    props: PropsDictType = {
        "MaxRuntimeInSeconds": (integer, True),
    }


class DataQualityJobDefinition(AWSObject):
    """
    `DataQualityJobDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-dataqualityjobdefinition.html>`__
    """

    resource_type = "AWS::SageMaker::DataQualityJobDefinition"

    props: PropsDictType = {
        "DataQualityAppSpecification": (DataQualityAppSpecification, True),
        "DataQualityBaselineConfig": (DataQualityBaselineConfig, False),
        "DataQualityJobInput": (DataQualityJobInput, True),
        "DataQualityJobOutputConfig": (MonitoringOutputConfig, True),
        "EndpointName": (str, False),
        "JobDefinitionName": (str, False),
        "JobResources": (MonitoringResources, True),
        "NetworkConfig": (NetworkConfig, False),
        "RoleArn": (str, True),
        "StoppingCondition": (StoppingCondition, False),
        "Tags": (Tags, False),
    }


class DeviceProperty(AWSProperty):
    """
    `DeviceProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-device-device.html>`__
    """

    props: PropsDictType = {
        "Description": (str, False),
        "DeviceName": (str, True),
        "IotThingName": (str, False),
    }


class Device(AWSObject):
    """
    `Device <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-device.html>`__
    """

    resource_type = "AWS::SageMaker::Device"

    props: PropsDictType = {
        "Device": (DeviceProperty, False),
        "DeviceFleetName": (str, True),
        "Tags": (Tags, False),
    }


class EdgeOutputConfig(AWSProperty):
    """
    `EdgeOutputConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-devicefleet-edgeoutputconfig.html>`__
    """

    props: PropsDictType = {
        "KmsKeyId": (str, False),
        "S3OutputLocation": (str, True),
    }


class DeviceFleet(AWSObject):
    """
    `DeviceFleet <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-devicefleet.html>`__
    """

    resource_type = "AWS::SageMaker::DeviceFleet"

    props: PropsDictType = {
        "Description": (str, False),
        "DeviceFleetName": (str, True),
        "OutputConfig": (EdgeOutputConfig, True),
        "RoleArn": (str, True),
        "Tags": (Tags, False),
    }


class EFSFileSystemConfig(AWSProperty):
    """
    `EFSFileSystemConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-efsfilesystemconfig.html>`__
    """

    props: PropsDictType = {
        "FileSystemId": (str, True),
        "FileSystemPath": (str, False),
    }


class FSxLustreFileSystemConfig(AWSProperty):
    """
    `FSxLustreFileSystemConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-fsxlustrefilesystemconfig.html>`__
    """

    props: PropsDictType = {
        "FileSystemId": (str, True),
        "FileSystemPath": (str, False),
    }


class CustomFileSystemConfig(AWSProperty):
    """
    `CustomFileSystemConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-customfilesystemconfig.html>`__
    """

    props: PropsDictType = {
        "EFSFileSystemConfig": (EFSFileSystemConfig, False),
        "FSxLustreFileSystemConfig": (FSxLustreFileSystemConfig, False),
    }


class CustomPosixUserConfig(AWSProperty):
    """
    `CustomPosixUserConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-customposixuserconfig.html>`__
    """

    props: PropsDictType = {
        "Gid": (integer, True),
        "Uid": (integer, True),
    }


class DefaultEbsStorageSettings(AWSProperty):
    """
    `DefaultEbsStorageSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-defaultebsstoragesettings.html>`__
    """

    props: PropsDictType = {
        "DefaultEbsVolumeSizeInGb": (integer, True),
        "MaximumEbsVolumeSizeInGb": (integer, True),
    }


class DefaultSpaceStorageSettings(AWSProperty):
    """
    `DefaultSpaceStorageSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-defaultspacestoragesettings.html>`__
    """

    props: PropsDictType = {
        "DefaultEbsStorageSettings": (DefaultEbsStorageSettings, False),
    }


class IdleSettings(AWSProperty):
    """
    `IdleSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-idlesettings.html>`__
    """

    props: PropsDictType = {
        "IdleTimeoutInMinutes": (integer, False),
        "LifecycleManagement": (str, False),
        "MaxIdleTimeoutInMinutes": (integer, False),
        "MinIdleTimeoutInMinutes": (integer, False),
    }


class AppLifecycleManagement(AWSProperty):
    """
    `AppLifecycleManagement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-applifecyclemanagement.html>`__
    """

    props: PropsDictType = {
        "IdleSettings": (IdleSettings, False),
    }


class CodeRepositoryProperty(AWSProperty):
    """
    `CodeRepositoryProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-coderepository.html>`__
    """

    props: PropsDictType = {
        "RepositoryUrl": (str, True),
    }


class CustomImage(AWSProperty):
    """
    `CustomImage <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-customimage.html>`__
    """

    props: PropsDictType = {
        "AppImageConfigName": (str, True),
        "ImageName": (str, True),
        "ImageVersionNumber": (integer, False),
    }


class JupyterLabAppSettings(AWSProperty):
    """
    `JupyterLabAppSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-jupyterlabappsettings.html>`__
    """

    props: PropsDictType = {
        "AppLifecycleManagement": (AppLifecycleManagement, False),
        "CodeRepositories": ([CodeRepositoryProperty], False),
        "CustomImages": ([CustomImage], False),
        "DefaultResourceSpec": (ResourceSpec, False),
        "LifecycleConfigArns": ([str], False),
    }


class JupyterServerAppSettings(AWSProperty):
    """
    `JupyterServerAppSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-jupyterserverappsettings.html>`__
    """

    props: PropsDictType = {
        "DefaultResourceSpec": (ResourceSpec, False),
        "LifecycleConfigArns": ([str], False),
    }


class KernelGatewayAppSettings(AWSProperty):
    """
    `KernelGatewayAppSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-kernelgatewayappsettings.html>`__
    """

    props: PropsDictType = {
        "CustomImages": ([CustomImage], False),
        "DefaultResourceSpec": (ResourceSpec, False),
        "LifecycleConfigArns": ([str], False),
    }


class DefaultSpaceSettings(AWSProperty):
    """
    `DefaultSpaceSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-domain-defaultspacesettings.html>`__
    """

    props: PropsDictType = {
        "CustomFileSystemConfigs": ([CustomFileSystemConfig], False),
        "CustomPosixUserConfig": (CustomPosixUserConfig, False),
        "ExecutionRole": (str, True),
        "JupyterLabAppSettings": (JupyterLabAppSettings, False),
        "JupyterServerAppSettings": (JupyterServerAppSettings, False),
        "KernelGatewayAppSettings": (KernelGatewayAppSettings, False),
        "SecurityGroups": ([str], False),
        "SpaceStorageSettings": (DefaultSpaceStorageSettings, False),
    }


class DockerSettings(AWSProperty):
    """
    `DockerSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-domain-dockersettings.html>`__
    """

    props: PropsDictType = {
        "EnableDockerAccess": (str, False),
        "VpcOnlyTrustedAccounts": ([str], False),
    }


class RStudioServerProDomainSettings(AWSProperty):
    """
    `RStudioServerProDomainSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-domain-rstudioserverprodomainsettings.html>`__
    """

    props: PropsDictType = {
        "DefaultResourceSpec": (ResourceSpec, False),
        "DomainExecutionRoleArn": (str, True),
        "RStudioConnectUrl": (str, False),
        "RStudioPackageManagerUrl": (str, False),
    }


class DomainSettings(AWSProperty):
    """
    `DomainSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-domain-domainsettings.html>`__
    """

    props: PropsDictType = {
        "DockerSettings": (DockerSettings, False),
        "ExecutionRoleIdentityConfig": (str, False),
        "RStudioServerProDomainSettings": (RStudioServerProDomainSettings, False),
        "SecurityGroupIds": ([str], False),
    }


class CodeEditorAppSettings(AWSProperty):
    """
    `CodeEditorAppSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-codeeditorappsettings.html>`__
    """

    props: PropsDictType = {
        "AppLifecycleManagement": (AppLifecycleManagement, False),
        "CustomImages": ([CustomImage], False),
        "DefaultResourceSpec": (ResourceSpec, False),
        "LifecycleConfigArns": ([str], False),
    }


class RStudioServerProAppSettings(AWSProperty):
    """
    `RStudioServerProAppSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-rstudioserverproappsettings.html>`__
    """

    props: PropsDictType = {
        "AccessStatus": (str, False),
        "UserGroup": (str, False),
    }


class SharingSettings(AWSProperty):
    """
    `SharingSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-sharingsettings.html>`__
    """

    props: PropsDictType = {
        "NotebookOutputOption": (str, False),
        "S3KmsKeyId": (str, False),
        "S3OutputPath": (str, False),
    }


class StudioWebPortalSettings(AWSProperty):
    """
    `StudioWebPortalSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-studiowebportalsettings.html>`__
    """

    props: PropsDictType = {
        "HiddenAppTypes": ([str], False),
        "HiddenMlTools": ([str], False),
    }


class UserSettings(AWSProperty):
    """
    `UserSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-userprofile-usersettings.html>`__
    """

    props: PropsDictType = {
        "CodeEditorAppSettings": (CodeEditorAppSettings, False),
        "CustomFileSystemConfigs": ([CustomFileSystemConfig], False),
        "CustomPosixUserConfig": (CustomPosixUserConfig, False),
        "DefaultLandingUri": (str, False),
        "ExecutionRole": (str, False),
        "JupyterLabAppSettings": (JupyterLabAppSettings, False),
        "JupyterServerAppSettings": (JupyterServerAppSettings, False),
        "KernelGatewayAppSettings": (KernelGatewayAppSettings, False),
        "RStudioServerProAppSettings": (RStudioServerProAppSettings, False),
        "SecurityGroups": ([str], False),
        "SharingSettings": (SharingSettings, False),
        "SpaceStorageSettings": (DefaultSpaceStorageSettings, False),
        "StudioWebPortal": (str, False),
        "StudioWebPortalSettings": (StudioWebPortalSettings, False),
    }


class Domain(AWSObject):
    """
    `Domain <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-domain.html>`__
    """

    resource_type = "AWS::SageMaker::Domain"

    props: PropsDictType = {
        "AppNetworkAccessType": (str, False),
        "AppSecurityGroupManagement": (str, False),
        "AuthMode": (str, True),
        "DefaultSpaceSettings": (DefaultSpaceSettings, False),
        "DefaultUserSettings": (UserSettings, True),
        "DomainName": (str, True),
        "DomainSettings": (DomainSettings, False),
        "KmsKeyId": (str, False),
        "SubnetIds": ([str], True),
        "TagPropagation": (str, False),
        "Tags": (Tags, False),
        "VpcId": (str, True),
    }


class Alarm(AWSProperty):
    """
    `Alarm <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferencecomponent-alarm.html>`__
    """

    props: PropsDictType = {
        "AlarmName": (str, True),
    }


class AutoRollbackConfig(AWSProperty):
    """
    `AutoRollbackConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpoint-autorollbackconfig.html>`__
    """

    props: PropsDictType = {
        "Alarms": ([Alarm], True),
    }


class CapacitySize(AWSProperty):
    """
    `CapacitySize <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpoint-capacitysize.html>`__
    """

    props: PropsDictType = {
        "Type": (str, True),
        "Value": (integer, True),
    }


class TrafficRoutingConfig(AWSProperty):
    """
    `TrafficRoutingConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpoint-trafficroutingconfig.html>`__
    """

    props: PropsDictType = {
        "CanarySize": (CapacitySize, False),
        "LinearStepSize": (CapacitySize, False),
        "Type": (str, True),
        "WaitIntervalInSeconds": (integer, False),
    }


class BlueGreenUpdatePolicy(AWSProperty):
    """
    `BlueGreenUpdatePolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpoint-bluegreenupdatepolicy.html>`__
    """

    props: PropsDictType = {
        "MaximumExecutionTimeoutInSeconds": (integer, False),
        "TerminationWaitInSeconds": (integer, False),
        "TrafficRoutingConfiguration": (TrafficRoutingConfig, True),
    }


class RollingUpdatePolicy(AWSProperty):
    """
    `RollingUpdatePolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpoint-rollingupdatepolicy.html>`__
    """

    props: PropsDictType = {
        "MaximumBatchSize": (CapacitySize, True),
        "MaximumExecutionTimeoutInSeconds": (integer, False),
        "RollbackMaximumBatchSize": (CapacitySize, False),
        "WaitIntervalInSeconds": (integer, True),
    }


class DeploymentConfig(AWSProperty):
    """
    `DeploymentConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpoint-deploymentconfig.html>`__
    """

    props: PropsDictType = {
        "AutoRollbackConfiguration": (AutoRollbackConfig, False),
        "BlueGreenUpdatePolicy": (BlueGreenUpdatePolicy, False),
        "RollingUpdatePolicy": (RollingUpdatePolicy, False),
    }


class VariantProperty(AWSProperty):
    """
    `VariantProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpoint-variantproperty.html>`__
    """

    props: PropsDictType = {
        "VariantPropertyType": (str, False),
    }


class Endpoint(AWSObject):
    """
    `Endpoint <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-endpoint.html>`__
    """

    resource_type = "AWS::SageMaker::Endpoint"

    props: PropsDictType = {
        "DeploymentConfig": (DeploymentConfig, False),
        "EndpointConfigName": (str, True),
        "EndpointName": (str, False),
        "ExcludeRetainedVariantProperties": ([VariantProperty], False),
        "RetainAllVariantProperties": (boolean, False),
        "RetainDeploymentConfig": (boolean, False),
        "Tags": (Tags, False),
    }


class AsyncInferenceClientConfig(AWSProperty):
    """
    `AsyncInferenceClientConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpointconfig-asyncinferenceclientconfig.html>`__
    """

    props: PropsDictType = {
        "MaxConcurrentInvocationsPerInstance": (integer, False),
    }


class AsyncInferenceNotificationConfig(AWSProperty):
    """
    `AsyncInferenceNotificationConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpointconfig-asyncinferencenotificationconfig.html>`__
    """

    props: PropsDictType = {
        "ErrorTopic": (str, False),
        "IncludeInferenceResponseIn": ([str], False),
        "SuccessTopic": (str, False),
    }


class AsyncInferenceOutputConfig(AWSProperty):
    """
    `AsyncInferenceOutputConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpointconfig-asyncinferenceoutputconfig.html>`__
    """

    props: PropsDictType = {
        "KmsKeyId": (str, False),
        "NotificationConfig": (AsyncInferenceNotificationConfig, False),
        "S3FailurePath": (str, False),
        "S3OutputPath": (str, False),
    }


class AsyncInferenceConfig(AWSProperty):
    """
    `AsyncInferenceConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpointconfig-asyncinferenceconfig.html>`__
    """

    props: PropsDictType = {
        "ClientConfig": (AsyncInferenceClientConfig, False),
        "OutputConfig": (AsyncInferenceOutputConfig, True),
    }


class CaptureContentTypeHeader(AWSProperty):
    """
    `CaptureContentTypeHeader <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferenceexperiment-capturecontenttypeheader.html>`__
    """

    props: PropsDictType = {
        "CsvContentTypes": ([str], False),
        "JsonContentTypes": ([str], False),
    }


class CaptureOption(AWSProperty):
    """
    `CaptureOption <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpointconfig-captureoption.html>`__
    """

    props: PropsDictType = {
        "CaptureMode": (str, True),
    }


class DataCaptureConfig(AWSProperty):
    """
    `DataCaptureConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpointconfig-datacaptureconfig.html>`__
    """

    props: PropsDictType = {
        "CaptureContentTypeHeader": (CaptureContentTypeHeader, False),
        "CaptureOptions": ([CaptureOption], True),
        "DestinationS3Uri": (str, True),
        "EnableCapture": (boolean, False),
        "InitialSamplingPercentage": (integer, True),
        "KmsKeyId": (str, False),
    }


class ClarifyInferenceConfig(AWSProperty):
    """
    `ClarifyInferenceConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpointconfig-clarifyinferenceconfig.html>`__
    """

    props: PropsDictType = {
        "ContentTemplate": (str, False),
        "FeatureHeaders": ([str], False),
        "FeatureTypes": ([str], False),
        "FeaturesAttribute": (str, False),
        "LabelAttribute": (str, False),
        "LabelHeaders": ([str], False),
        "LabelIndex": (integer, False),
        "MaxPayloadInMB": (integer, False),
        "MaxRecordCount": (integer, False),
        "ProbabilityAttribute": (str, False),
        "ProbabilityIndex": (integer, False),
    }


class ClarifyShapBaselineConfig(AWSProperty):
    """
    `ClarifyShapBaselineConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpointconfig-clarifyshapbaselineconfig.html>`__
    """

    props: PropsDictType = {
        "MimeType": (str, False),
        "ShapBaseline": (str, False),
        "ShapBaselineUri": (str, False),
    }


class ClarifyTextConfig(AWSProperty):
    """
    `ClarifyTextConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpointconfig-clarifytextconfig.html>`__
    """

    props: PropsDictType = {
        "Granularity": (str, True),
        "Language": (str, True),
    }


class ClarifyShapConfig(AWSProperty):
    """
    `ClarifyShapConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpointconfig-clarifyshapconfig.html>`__
    """

    props: PropsDictType = {
        "NumberOfSamples": (integer, False),
        "Seed": (integer, False),
        "ShapBaselineConfig": (ClarifyShapBaselineConfig, True),
        "TextConfig": (ClarifyTextConfig, False),
        "UseLogit": (boolean, False),
    }


class ClarifyExplainerConfig(AWSProperty):
    """
    `ClarifyExplainerConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpointconfig-clarifyexplainerconfig.html>`__
    """

    props: PropsDictType = {
        "EnableExplanations": (str, False),
        "InferenceConfig": (ClarifyInferenceConfig, False),
        "ShapConfig": (ClarifyShapConfig, True),
    }


class ExplainerConfig(AWSProperty):
    """
    `ExplainerConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpointconfig-explainerconfig.html>`__
    """

    props: PropsDictType = {
        "ClarifyExplainerConfig": (ClarifyExplainerConfig, False),
    }


class ManagedInstanceScaling(AWSProperty):
    """
    `ManagedInstanceScaling <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpointconfig-productionvariant-managedinstancescaling.html>`__
    """

    props: PropsDictType = {
        "MaxInstanceCount": (integer, False),
        "MinInstanceCount": (integer, False),
        "Status": (str, False),
    }


class RoutingConfig(AWSProperty):
    """
    `RoutingConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpointconfig-productionvariant-routingconfig.html>`__
    """

    props: PropsDictType = {
        "RoutingStrategy": (str, False),
    }


class ServerlessConfig(AWSProperty):
    """
    `ServerlessConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpointconfig-productionvariant-serverlessconfig.html>`__
    """

    props: PropsDictType = {
        "MaxConcurrency": (integer, True),
        "MemorySizeInMB": (integer, True),
        "ProvisionedConcurrency": (integer, False),
    }


class ProductionVariant(AWSProperty):
    """
    `ProductionVariant <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-endpointconfig-productionvariant.html>`__
    """

    props: PropsDictType = {
        "ContainerStartupHealthCheckTimeoutInSeconds": (integer, False),
        "EnableSSMAccess": (boolean, False),
        "InferenceAmiVersion": (str, False),
        "InitialInstanceCount": (integer, False),
        "InitialVariantWeight": (double, False),
        "InstanceType": (str, False),
        "ManagedInstanceScaling": (ManagedInstanceScaling, False),
        "ModelDataDownloadTimeoutInSeconds": (integer, False),
        "ModelName": (str, False),
        "RoutingConfig": (RoutingConfig, False),
        "ServerlessConfig": (ServerlessConfig, False),
        "VariantName": (str, True),
        "VolumeSizeInGB": (integer, False),
    }


class EndpointConfig(AWSObject):
    """
    `EndpointConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-endpointconfig.html>`__
    """

    resource_type = "AWS::SageMaker::EndpointConfig"

    props: PropsDictType = {
        "AsyncInferenceConfig": (AsyncInferenceConfig, False),
        "DataCaptureConfig": (DataCaptureConfig, False),
        "EnableNetworkIsolation": (boolean, False),
        "EndpointConfigName": (str, False),
        "ExecutionRoleArn": (str, False),
        "ExplainerConfig": (ExplainerConfig, False),
        "KmsKeyId": (str, False),
        "ProductionVariants": ([ProductionVariant], True),
        "ShadowProductionVariants": ([ProductionVariant], False),
        "Tags": (Tags, False),
        "VpcConfig": (VpcConfig, False),
    }


class FeatureDefinition(AWSProperty):
    """
    `FeatureDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-featuregroup-featuredefinition.html>`__
    """

    props: PropsDictType = {
        "FeatureName": (str, True),
        "FeatureType": (str, True),
    }


class DataCatalogConfig(AWSProperty):
    """
    `DataCatalogConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-featuregroup-datacatalogconfig.html>`__
    """

    props: PropsDictType = {
        "Catalog": (str, True),
        "Database": (str, True),
        "TableName": (str, True),
    }


class S3StorageConfig(AWSProperty):
    """
    `S3StorageConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-featuregroup-s3storageconfig.html>`__
    """

    props: PropsDictType = {
        "KmsKeyId": (str, False),
        "S3Uri": (str, True),
    }


class OfflineStoreConfig(AWSProperty):
    """
    `OfflineStoreConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-featuregroup-offlinestoreconfig.html>`__
    """

    props: PropsDictType = {
        "DataCatalogConfig": (DataCatalogConfig, False),
        "DisableGlueTableCreation": (boolean, False),
        "S3StorageConfig": (S3StorageConfig, True),
        "TableFormat": (str, False),
    }


class OnlineStoreSecurityConfig(AWSProperty):
    """
    `OnlineStoreSecurityConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-featuregroup-onlinestoresecurityconfig.html>`__
    """

    props: PropsDictType = {
        "KmsKeyId": (str, False),
    }


class TtlDuration(AWSProperty):
    """
    `TtlDuration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-featuregroup-ttlduration.html>`__
    """

    props: PropsDictType = {
        "Unit": (str, False),
        "Value": (integer, False),
    }


class OnlineStoreConfig(AWSProperty):
    """
    `OnlineStoreConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-featuregroup-onlinestoreconfig.html>`__
    """

    props: PropsDictType = {
        "EnableOnlineStore": (boolean, False),
        "SecurityConfig": (OnlineStoreSecurityConfig, False),
        "StorageType": (str, False),
        "TtlDuration": (TtlDuration, False),
    }


class ThroughputConfig(AWSProperty):
    """
    `ThroughputConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-featuregroup-throughputconfig.html>`__
    """

    props: PropsDictType = {
        "ProvisionedReadCapacityUnits": (integer, False),
        "ProvisionedWriteCapacityUnits": (integer, False),
        "ThroughputMode": (str, True),
    }


class FeatureGroup(AWSObject):
    """
    `FeatureGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-featuregroup.html>`__
    """

    resource_type = "AWS::SageMaker::FeatureGroup"

    props: PropsDictType = {
        "Description": (str, False),
        "EventTimeFeatureName": (str, True),
        "FeatureDefinitions": ([FeatureDefinition], True),
        "FeatureGroupName": (str, True),
        "OfflineStoreConfig": (OfflineStoreConfig, False),
        "OnlineStoreConfig": (OnlineStoreConfig, False),
        "RecordIdentifierFeatureName": (str, True),
        "RoleArn": (str, False),
        "Tags": (Tags, False),
        "ThroughputConfig": (ThroughputConfig, False),
    }


class Image(AWSObject):
    """
    `Image <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-image.html>`__
    """

    resource_type = "AWS::SageMaker::Image"

    props: PropsDictType = {
        "ImageDescription": (str, False),
        "ImageDisplayName": (str, False),
        "ImageName": (str, True),
        "ImageRoleArn": (str, True),
        "Tags": (Tags, False),
    }


class ImageVersion(AWSObject):
    """
    `ImageVersion <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-imageversion.html>`__
    """

    resource_type = "AWS::SageMaker::ImageVersion"

    props: PropsDictType = {
        "Alias": (str, False),
        "Aliases": ([str], False),
        "BaseImage": (str, True),
        "Horovod": (boolean, False),
        "ImageName": (str, True),
        "JobType": (str, False),
        "MLFramework": (str, False),
        "Processor": (str, False),
        "ProgrammingLang": (str, False),
        "ReleaseNotes": (str, False),
        "VendorGuidance": (str, False),
    }


class AutoRollbackConfiguration(AWSProperty):
    """
    `AutoRollbackConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferencecomponent-autorollbackconfiguration.html>`__
    """

    props: PropsDictType = {
        "Alarms": ([Alarm], True),
    }


class InferenceComponentCapacitySize(AWSProperty):
    """
    `InferenceComponentCapacitySize <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferencecomponent-inferencecomponentcapacitysize.html>`__
    """

    props: PropsDictType = {
        "Type": (str, True),
        "Value": (integer, True),
    }


class InferenceComponentRollingUpdatePolicy(AWSProperty):
    """
    `InferenceComponentRollingUpdatePolicy <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferencecomponent-inferencecomponentrollingupdatepolicy.html>`__
    """

    props: PropsDictType = {
        "MaximumBatchSize": (InferenceComponentCapacitySize, False),
        "MaximumExecutionTimeoutInSeconds": (integer, False),
        "RollbackMaximumBatchSize": (InferenceComponentCapacitySize, False),
        "WaitIntervalInSeconds": (integer, False),
    }


class InferenceComponentDeploymentConfig(AWSProperty):
    """
    `InferenceComponentDeploymentConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferencecomponent-inferencecomponentdeploymentconfig.html>`__
    """

    props: PropsDictType = {
        "AutoRollbackConfiguration": (AutoRollbackConfiguration, False),
        "RollingUpdatePolicy": (InferenceComponentRollingUpdatePolicy, False),
    }


class InferenceComponentRuntimeConfig(AWSProperty):
    """
    `InferenceComponentRuntimeConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferencecomponent-inferencecomponentruntimeconfig.html>`__
    """

    props: PropsDictType = {
        "CopyCount": (integer, False),
        "CurrentCopyCount": (integer, False),
        "DesiredCopyCount": (integer, False),
    }


class InferenceComponentComputeResourceRequirements(AWSProperty):
    """
    `InferenceComponentComputeResourceRequirements <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferencecomponent-inferencecomponentcomputeresourcerequirements.html>`__
    """

    props: PropsDictType = {
        "MaxMemoryRequiredInMb": (integer, False),
        "MinMemoryRequiredInMb": (integer, False),
        "NumberOfAcceleratorDevicesRequired": (double, False),
        "NumberOfCpuCoresRequired": (double, False),
    }


class DeployedImage(AWSProperty):
    """
    `DeployedImage <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferencecomponent-deployedimage.html>`__
    """

    props: PropsDictType = {
        "ResolutionTime": (str, False),
        "ResolvedImage": (str, False),
        "SpecifiedImage": (str, False),
    }


class InferenceComponentContainerSpecification(AWSProperty):
    """
    `InferenceComponentContainerSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferencecomponent-inferencecomponentcontainerspecification.html>`__
    """

    props: PropsDictType = {
        "ArtifactUrl": (str, False),
        "DeployedImage": (DeployedImage, False),
        "Environment": (dict, False),
        "Image": (str, False),
    }


class InferenceComponentStartupParameters(AWSProperty):
    """
    `InferenceComponentStartupParameters <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferencecomponent-inferencecomponentstartupparameters.html>`__
    """

    props: PropsDictType = {
        "ContainerStartupHealthCheckTimeoutInSeconds": (integer, False),
        "ModelDataDownloadTimeoutInSeconds": (integer, False),
    }


class InferenceComponentSpecification(AWSProperty):
    """
    `InferenceComponentSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferencecomponent-inferencecomponentspecification.html>`__
    """

    props: PropsDictType = {
        "BaseInferenceComponentName": (str, False),
        "ComputeResourceRequirements": (
            InferenceComponentComputeResourceRequirements,
            False,
        ),
        "Container": (InferenceComponentContainerSpecification, False),
        "ModelName": (str, False),
        "StartupParameters": (InferenceComponentStartupParameters, False),
    }


class InferenceComponent(AWSObject):
    """
    `InferenceComponent <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-inferencecomponent.html>`__
    """

    resource_type = "AWS::SageMaker::InferenceComponent"

    props: PropsDictType = {
        "DeploymentConfig": (InferenceComponentDeploymentConfig, False),
        "EndpointArn": (str, False),
        "EndpointName": (str, True),
        "InferenceComponentName": (str, False),
        "RuntimeConfig": (InferenceComponentRuntimeConfig, False),
        "Specification": (InferenceComponentSpecification, True),
        "Tags": (Tags, False),
        "VariantName": (str, False),
    }


class DataStorageConfig(AWSProperty):
    """
    `DataStorageConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferenceexperiment-datastorageconfig.html>`__
    """

    props: PropsDictType = {
        "ContentType": (CaptureContentTypeHeader, False),
        "Destination": (str, True),
        "KmsKey": (str, False),
    }


class InferenceExperimentSchedule(AWSProperty):
    """
    `InferenceExperimentSchedule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferenceexperiment-inferenceexperimentschedule.html>`__
    """

    props: PropsDictType = {
        "EndTime": (str, False),
        "StartTime": (str, False),
    }


class RealTimeInferenceConfig(AWSProperty):
    """
    `RealTimeInferenceConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferenceexperiment-realtimeinferenceconfig.html>`__
    """

    props: PropsDictType = {
        "InstanceCount": (integer, True),
        "InstanceType": (str, True),
    }


class ModelInfrastructureConfig(AWSProperty):
    """
    `ModelInfrastructureConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferenceexperiment-modelinfrastructureconfig.html>`__
    """

    props: PropsDictType = {
        "InfrastructureType": (str, True),
        "RealTimeInferenceConfig": (RealTimeInferenceConfig, True),
    }


class ModelVariantConfig(AWSProperty):
    """
    `ModelVariantConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferenceexperiment-modelvariantconfig.html>`__
    """

    props: PropsDictType = {
        "InfrastructureConfig": (ModelInfrastructureConfig, True),
        "ModelName": (str, True),
        "VariantName": (str, True),
    }


class ShadowModelVariantConfig(AWSProperty):
    """
    `ShadowModelVariantConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferenceexperiment-shadowmodelvariantconfig.html>`__
    """

    props: PropsDictType = {
        "SamplingPercentage": (integer, True),
        "ShadowModelVariantName": (str, True),
    }


class ShadowModeConfig(AWSProperty):
    """
    `ShadowModeConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferenceexperiment-shadowmodeconfig.html>`__
    """

    props: PropsDictType = {
        "ShadowModelVariants": ([ShadowModelVariantConfig], True),
        "SourceModelVariantName": (str, True),
    }


class InferenceExperiment(AWSObject):
    """
    `InferenceExperiment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-inferenceexperiment.html>`__
    """

    resource_type = "AWS::SageMaker::InferenceExperiment"

    props: PropsDictType = {
        "DataStorageConfig": (DataStorageConfig, False),
        "Description": (str, False),
        "DesiredState": (str, False),
        "EndpointName": (str, True),
        "KmsKey": (str, False),
        "ModelVariants": ([ModelVariantConfig], True),
        "Name": (str, True),
        "RoleArn": (str, True),
        "Schedule": (InferenceExperimentSchedule, False),
        "ShadowModeConfig": (ShadowModeConfig, False),
        "StatusReason": (str, False),
        "Tags": (Tags, False),
        "Type": (str, True),
    }


class MlflowTrackingServer(AWSObject):
    """
    `MlflowTrackingServer <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-mlflowtrackingserver.html>`__
    """

    resource_type = "AWS::SageMaker::MlflowTrackingServer"

    props: PropsDictType = {
        "ArtifactStoreUri": (str, True),
        "AutomaticModelRegistration": (boolean, False),
        "MlflowVersion": (str, False),
        "RoleArn": (str, True),
        "Tags": (Tags, False),
        "TrackingServerName": (str, True),
        "TrackingServerSize": (str, False),
        "WeeklyMaintenanceWindowStart": (str, False),
    }


class RepositoryAuthConfig(AWSProperty):
    """
    `RepositoryAuthConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-model-containerdefinition-imageconfig-repositoryauthconfig.html>`__
    """

    props: PropsDictType = {
        "RepositoryCredentialsProviderArn": (str, True),
    }


class ImageConfig(AWSProperty):
    """
    `ImageConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-model-containerdefinition-imageconfig.html>`__
    """

    props: PropsDictType = {
        "RepositoryAccessMode": (str, True),
        "RepositoryAuthConfig": (RepositoryAuthConfig, False),
    }


class ModelAccessConfig(AWSProperty):
    """
    `ModelAccessConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-modelaccessconfig.html>`__
    """

    props: PropsDictType = {
        "AcceptEula": (boolean, True),
    }


class S3ModelDataSource(AWSProperty):
    """
    `S3ModelDataSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-s3modeldatasource.html>`__
    """

    props: PropsDictType = {
        "CompressionType": (str, True),
        "ModelAccessConfig": (ModelAccessConfig, False),
        "S3DataType": (str, True),
        "S3Uri": (str, True),
    }


class ModelDataSource(AWSProperty):
    """
    `ModelDataSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-modeldatasource.html>`__
    """

    props: PropsDictType = {
        "S3DataSource": (S3ModelDataSource, False),
    }


class MultiModelConfig(AWSProperty):
    """
    `MultiModelConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-model-containerdefinition-multimodelconfig.html>`__
    """

    props: PropsDictType = {
        "ModelCacheSetting": (str, False),
    }


class ContainerDefinition(AWSProperty):
    """
    `ContainerDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-model-containerdefinition.html>`__
    """

    props: PropsDictType = {
        "ContainerHostname": (str, False),
        "Environment": (dict, False),
        "Image": (str, False),
        "ImageConfig": (ImageConfig, False),
        "InferenceSpecificationName": (str, False),
        "Mode": (str, False),
        "ModelDataSource": (ModelDataSource, False),
        "ModelDataUrl": (str, False),
        "ModelPackageName": (str, False),
        "MultiModelConfig": (MultiModelConfig, False),
    }


class InferenceExecutionConfig(AWSProperty):
    """
    `InferenceExecutionConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-model-inferenceexecutionconfig.html>`__
    """

    props: PropsDictType = {
        "Mode": (str, True),
    }


class Model(AWSObject):
    """
    `Model <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-model.html>`__
    """

    resource_type = "AWS::SageMaker::Model"

    props: PropsDictType = {
        "Containers": ([ContainerDefinition], False),
        "EnableNetworkIsolation": (boolean, False),
        "ExecutionRoleArn": (str, False),
        "InferenceExecutionConfig": (InferenceExecutionConfig, False),
        "ModelName": (str, False),
        "PrimaryContainer": (ContainerDefinition, False),
        "Tags": (Tags, False),
        "VpcConfig": (VpcConfig, False),
    }


class ModelBiasAppSpecification(AWSProperty):
    """
    `ModelBiasAppSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelbiasjobdefinition-modelbiasappspecification.html>`__
    """

    props: PropsDictType = {
        "ConfigUri": (str, True),
        "Environment": (dict, False),
        "ImageUri": (str, True),
    }


class ModelBiasBaselineConfig(AWSProperty):
    """
    `ModelBiasBaselineConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelbiasjobdefinition-modelbiasbaselineconfig.html>`__
    """

    props: PropsDictType = {
        "BaseliningJobName": (str, False),
        "ConstraintsResource": (ConstraintsResource, False),
    }


class ModelBiasEndpointInput(AWSProperty):
    """
    `ModelBiasEndpointInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelbiasjobdefinition-endpointinput.html>`__
    """

    props: PropsDictType = {
        "EndTimeOffset": (str, False),
        "EndpointName": (str, True),
        "FeaturesAttribute": (str, False),
        "InferenceAttribute": (str, False),
        "LocalPath": (str, True),
        "ProbabilityAttribute": (str, False),
        "ProbabilityThresholdAttribute": (double, False),
        "S3DataDistributionType": (str, False),
        "S3InputMode": (str, False),
        "StartTimeOffset": (str, False),
    }


class MonitoringGroundTruthS3Input(AWSProperty):
    """
    `MonitoringGroundTruthS3Input <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelqualityjobdefinition-monitoringgroundtruths3input.html>`__
    """

    props: PropsDictType = {
        "S3Uri": (str, True),
    }


class ModelBiasJobInput(AWSProperty):
    """
    `ModelBiasJobInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelbiasjobdefinition-modelbiasjobinput.html>`__
    """

    props: PropsDictType = {
        "BatchTransformInput": (BatchTransformInput, False),
        "EndpointInput": (ModelBiasEndpointInput, False),
        "GroundTruthS3Input": (MonitoringGroundTruthS3Input, True),
    }


class ModelBiasJobDefinition(AWSObject):
    """
    `ModelBiasJobDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-modelbiasjobdefinition.html>`__
    """

    resource_type = "AWS::SageMaker::ModelBiasJobDefinition"

    props: PropsDictType = {
        "EndpointName": (str, False),
        "JobDefinitionName": (str, False),
        "JobResources": (MonitoringResources, True),
        "ModelBiasAppSpecification": (ModelBiasAppSpecification, True),
        "ModelBiasBaselineConfig": (ModelBiasBaselineConfig, False),
        "ModelBiasJobInput": (ModelBiasJobInput, True),
        "ModelBiasJobOutputConfig": (MonitoringOutputConfig, True),
        "NetworkConfig": (NetworkConfig, False),
        "RoleArn": (str, True),
        "StoppingCondition": (StoppingCondition, False),
        "Tags": (Tags, False),
    }


class AdditionalInformation(AWSProperty):
    """
    `AdditionalInformation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-additionalinformation.html>`__
    """

    props: PropsDictType = {
        "CaveatsAndRecommendations": (str, False),
        "CustomDetails": (dict, False),
        "EthicalConsiderations": (str, False),
    }


class BusinessDetails(AWSProperty):
    """
    `BusinessDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-businessdetails.html>`__
    """

    props: PropsDictType = {
        "BusinessProblem": (str, False),
        "BusinessStakeholders": (str, False),
        "LineOfBusiness": (str, False),
    }


class MetricDataItems(AWSProperty):
    """
    `MetricDataItems <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-metricdataitems.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "Notes": (str, False),
        "Type": (str, True),
        "Value": (dict, True),
        "XAxisName": ([str], False),
        "YAxisName": ([str], False),
    }


class MetricGroup(AWSProperty):
    """
    `MetricGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-metricgroup.html>`__
    """

    props: PropsDictType = {
        "MetricData": ([MetricDataItems], True),
        "Name": (str, True),
    }


class EvaluationDetail(AWSProperty):
    """
    `EvaluationDetail <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-evaluationdetail.html>`__
    """

    props: PropsDictType = {
        "Datasets": ([str], False),
        "EvaluationJobArn": (str, False),
        "EvaluationObservation": (str, False),
        "Metadata": (dict, False),
        "MetricGroups": ([MetricGroup], False),
        "Name": (str, True),
    }


class IntendedUses(AWSProperty):
    """
    `IntendedUses <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-intendeduses.html>`__
    """

    props: PropsDictType = {
        "ExplanationsForRiskRating": (str, False),
        "FactorsAffectingModelEfficiency": (str, False),
        "IntendedUses": (str, False),
        "PurposeOfModel": (str, False),
        "RiskRating": (str, False),
    }


class InferenceEnvironment(AWSProperty):
    """
    `InferenceEnvironment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-inferenceenvironment.html>`__
    """

    props: PropsDictType = {
        "ContainerImage": ([str], False),
    }


class ModelOverview(AWSProperty):
    """
    `ModelOverview <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-modeloverview.html>`__
    """

    props: PropsDictType = {
        "AlgorithmType": (str, False),
        "InferenceEnvironment": (InferenceEnvironment, False),
        "ModelArtifact": ([str], False),
        "ModelCreator": (str, False),
        "ModelDescription": (str, False),
        "ModelId": (str, False),
        "ModelName": (str, False),
        "ModelOwner": (str, False),
        "ModelVersion": (double, False),
        "ProblemType": (str, False),
    }


class ModelInput(AWSProperty):
    """
    `ModelInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-modelinput.html>`__
    """

    props: PropsDictType = {
        "DataInputConfig": (str, True),
    }


class ModelPackageContainerDefinition(AWSProperty):
    """
    `ModelPackageContainerDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-modelpackagecontainerdefinition.html>`__
    """

    props: PropsDictType = {
        "ContainerHostname": (str, False),
        "Environment": (dict, False),
        "Framework": (str, False),
        "FrameworkVersion": (str, False),
        "Image": (str, True),
        "ImageDigest": (str, False),
        "ModelDataSource": (ModelDataSource, False),
        "ModelDataUrl": (str, False),
        "ModelInput": (ModelInput, False),
        "NearestModelName": (str, False),
    }


class InferenceSpecification(AWSProperty):
    """
    `InferenceSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-inferencespecification.html>`__
    """

    props: PropsDictType = {
        "Containers": ([ModelPackageContainerDefinition], True),
        "SupportedContentTypes": ([str], True),
        "SupportedRealtimeInferenceInstanceTypes": ([str], False),
        "SupportedResponseMIMETypes": ([str], True),
        "SupportedTransformInstanceTypes": ([str], False),
    }


class ModelPackageCreator(AWSProperty):
    """
    `ModelPackageCreator <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-modelpackagecreator.html>`__
    """

    props: PropsDictType = {
        "UserProfileName": (str, False),
    }


class SourceAlgorithm(AWSProperty):
    """
    `SourceAlgorithm <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-sourcealgorithm.html>`__
    """

    props: PropsDictType = {
        "AlgorithmName": (str, True),
        "ModelDataUrl": (str, False),
    }


class ModelPackageDetails(AWSProperty):
    """
    `ModelPackageDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-modelpackagedetails.html>`__
    """

    props: PropsDictType = {
        "ApprovalDescription": (str, False),
        "CreatedBy": (ModelPackageCreator, False),
        "Domain": (str, False),
        "InferenceSpecification": (InferenceSpecification, False),
        "ModelApprovalStatus": (str, False),
        "ModelPackageArn": (str, False),
        "ModelPackageDescription": (str, False),
        "ModelPackageGroupName": (str, False),
        "ModelPackageName": (str, False),
        "ModelPackageStatus": (str, False),
        "ModelPackageVersion": (double, False),
        "SourceAlgorithms": ([SourceAlgorithm], False),
        "Task": (str, False),
    }


class Function(AWSProperty):
    """
    `Function <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-function.html>`__
    """

    props: PropsDictType = {
        "Condition": (str, False),
        "Facet": (str, False),
        "Function": (str, False),
    }


class ObjectiveFunction(AWSProperty):
    """
    `ObjectiveFunction <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-objectivefunction.html>`__
    """

    props: PropsDictType = {
        "Function": (Function, False),
        "Notes": (str, False),
    }


class TrainingEnvironment(AWSProperty):
    """
    `TrainingEnvironment <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-trainingenvironment.html>`__
    """

    props: PropsDictType = {
        "ContainerImage": ([str], False),
    }


class TrainingHyperParameter(AWSProperty):
    """
    `TrainingHyperParameter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-traininghyperparameter.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "Value": (str, True),
    }


class TrainingMetric(AWSProperty):
    """
    `TrainingMetric <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-trainingmetric.html>`__
    """

    props: PropsDictType = {
        "Name": (str, True),
        "Notes": (str, False),
        "Value": (double, True),
    }


class TrainingJobDetails(AWSProperty):
    """
    `TrainingJobDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-trainingjobdetails.html>`__
    """

    props: PropsDictType = {
        "HyperParameters": ([TrainingHyperParameter], False),
        "TrainingArn": (str, False),
        "TrainingDatasets": ([str], False),
        "TrainingEnvironment": (TrainingEnvironment, False),
        "TrainingMetrics": ([TrainingMetric], False),
        "UserProvidedHyperParameters": ([TrainingHyperParameter], False),
        "UserProvidedTrainingMetrics": ([TrainingMetric], False),
    }


class TrainingDetails(AWSProperty):
    """
    `TrainingDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-trainingdetails.html>`__
    """

    props: PropsDictType = {
        "ObjectiveFunction": (ObjectiveFunction, False),
        "TrainingJobDetails": (TrainingJobDetails, False),
        "TrainingObservations": (str, False),
    }


class Content(AWSProperty):
    """
    `Content <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-content.html>`__
    """

    props: PropsDictType = {
        "AdditionalInformation": (AdditionalInformation, False),
        "BusinessDetails": (BusinessDetails, False),
        "EvaluationDetails": ([EvaluationDetail], False),
        "IntendedUses": (IntendedUses, False),
        "ModelOverview": (ModelOverview, False),
        "ModelPackageDetails": (ModelPackageDetails, False),
        "TrainingDetails": (TrainingDetails, False),
    }


class SecurityConfig(AWSProperty):
    """
    `SecurityConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-securityconfig.html>`__
    """

    props: PropsDictType = {
        "KmsKeyId": (str, True),
    }


class UserContext(AWSProperty):
    """
    `UserContext <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-usercontext.html>`__
    """

    props: PropsDictType = {
        "DomainId": (str, False),
        "UserProfileArn": (str, False),
        "UserProfileName": (str, False),
    }


class ModelCard(AWSObject):
    """
    `ModelCard <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-modelcard.html>`__
    """

    resource_type = "AWS::SageMaker::ModelCard"

    props: PropsDictType = {
        "Content": (Content, True),
        "CreatedBy": (UserContext, False),
        "LastModifiedBy": (UserContext, False),
        "ModelCardName": (str, True),
        "ModelCardStatus": (str, True),
        "SecurityConfig": (SecurityConfig, False),
        "Tags": (Tags, False),
    }


class ModelExplainabilityAppSpecification(AWSProperty):
    """
    `ModelExplainabilityAppSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelexplainabilityjobdefinition-modelexplainabilityappspecification.html>`__
    """

    props: PropsDictType = {
        "ConfigUri": (str, True),
        "Environment": (dict, False),
        "ImageUri": (str, True),
    }


class ModelExplainabilityBaselineConfig(AWSProperty):
    """
    `ModelExplainabilityBaselineConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelexplainabilityjobdefinition-modelexplainabilitybaselineconfig.html>`__
    """

    props: PropsDictType = {
        "BaseliningJobName": (str, False),
        "ConstraintsResource": (ConstraintsResource, False),
    }


class ModelExplainabilityEndpointInput(AWSProperty):
    """
    `ModelExplainabilityEndpointInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelexplainabilityjobdefinition-endpointinput.html>`__
    """

    props: PropsDictType = {
        "EndpointName": (str, True),
        "FeaturesAttribute": (str, False),
        "InferenceAttribute": (str, False),
        "LocalPath": (str, True),
        "ProbabilityAttribute": (str, False),
        "S3DataDistributionType": (str, False),
        "S3InputMode": (str, False),
    }


class ModelExplainabilityJobInput(AWSProperty):
    """
    `ModelExplainabilityJobInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelexplainabilityjobdefinition-modelexplainabilityjobinput.html>`__
    """

    props: PropsDictType = {
        "BatchTransformInput": (BatchTransformInput, False),
        "EndpointInput": (ModelExplainabilityEndpointInput, False),
    }


class ModelExplainabilityJobDefinition(AWSObject):
    """
    `ModelExplainabilityJobDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-modelexplainabilityjobdefinition.html>`__
    """

    resource_type = "AWS::SageMaker::ModelExplainabilityJobDefinition"

    props: PropsDictType = {
        "EndpointName": (str, False),
        "JobDefinitionName": (str, False),
        "JobResources": (MonitoringResources, True),
        "ModelExplainabilityAppSpecification": (
            ModelExplainabilityAppSpecification,
            True,
        ),
        "ModelExplainabilityBaselineConfig": (ModelExplainabilityBaselineConfig, False),
        "ModelExplainabilityJobInput": (ModelExplainabilityJobInput, True),
        "ModelExplainabilityJobOutputConfig": (MonitoringOutputConfig, True),
        "NetworkConfig": (NetworkConfig, False),
        "RoleArn": (str, True),
        "StoppingCondition": (StoppingCondition, False),
        "Tags": (Tags, False),
    }


class AdditionalInferenceSpecificationDefinition(AWSProperty):
    """
    `AdditionalInferenceSpecificationDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-additionalinferencespecificationdefinition.html>`__
    """

    props: PropsDictType = {
        "Containers": ([ModelPackageContainerDefinition], True),
        "Description": (str, False),
        "Name": (str, True),
        "SupportedContentTypes": ([str], False),
        "SupportedRealtimeInferenceInstanceTypes": ([str], False),
        "SupportedResponseMIMETypes": ([str], False),
        "SupportedTransformInstanceTypes": ([str], False),
    }


class FileSource(AWSProperty):
    """
    `FileSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-filesource.html>`__
    """

    props: PropsDictType = {
        "ContentDigest": (str, False),
        "ContentType": (str, False),
        "S3Uri": (str, True),
    }


class MetricsSource(AWSProperty):
    """
    `MetricsSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-metricssource.html>`__
    """

    props: PropsDictType = {
        "ContentDigest": (str, False),
        "ContentType": (str, True),
        "S3Uri": (str, True),
    }


class DriftCheckBias(AWSProperty):
    """
    `DriftCheckBias <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-driftcheckbias.html>`__
    """

    props: PropsDictType = {
        "ConfigFile": (FileSource, False),
        "PostTrainingConstraints": (MetricsSource, False),
        "PreTrainingConstraints": (MetricsSource, False),
    }


class DriftCheckExplainability(AWSProperty):
    """
    `DriftCheckExplainability <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-driftcheckexplainability.html>`__
    """

    props: PropsDictType = {
        "ConfigFile": (FileSource, False),
        "Constraints": (MetricsSource, False),
    }


class DriftCheckModelDataQuality(AWSProperty):
    """
    `DriftCheckModelDataQuality <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-driftcheckmodeldataquality.html>`__
    """

    props: PropsDictType = {
        "Constraints": (MetricsSource, False),
        "Statistics": (MetricsSource, False),
    }


class DriftCheckModelQuality(AWSProperty):
    """
    `DriftCheckModelQuality <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-driftcheckmodelquality.html>`__
    """

    props: PropsDictType = {
        "Constraints": (MetricsSource, False),
        "Statistics": (MetricsSource, False),
    }


class DriftCheckBaselines(AWSProperty):
    """
    `DriftCheckBaselines <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-driftcheckbaselines.html>`__
    """

    props: PropsDictType = {
        "Bias": (DriftCheckBias, False),
        "Explainability": (DriftCheckExplainability, False),
        "ModelDataQuality": (DriftCheckModelDataQuality, False),
        "ModelQuality": (DriftCheckModelQuality, False),
    }


class MetadataProperties(AWSProperty):
    """
    `MetadataProperties <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-metadataproperties.html>`__
    """

    props: PropsDictType = {
        "CommitId": (str, False),
        "GeneratedBy": (str, False),
        "ProjectId": (str, False),
        "Repository": (str, False),
    }


class ModelCardProperty(AWSProperty):
    """
    `ModelCardProperty <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-modelcard.html>`__
    """

    props: PropsDictType = {
        "ModelCardContent": (str, True),
        "ModelCardStatus": (str, True),
    }


class Bias(AWSProperty):
    """
    `Bias <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-bias.html>`__
    """

    props: PropsDictType = {
        "PostTrainingReport": (MetricsSource, False),
        "PreTrainingReport": (MetricsSource, False),
        "Report": (MetricsSource, False),
    }


class Explainability(AWSProperty):
    """
    `Explainability <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-explainability.html>`__
    """

    props: PropsDictType = {
        "Report": (MetricsSource, False),
    }


class ModelDataQuality(AWSProperty):
    """
    `ModelDataQuality <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-modeldataquality.html>`__
    """

    props: PropsDictType = {
        "Constraints": (MetricsSource, False),
        "Statistics": (MetricsSource, False),
    }


class ModelQuality(AWSProperty):
    """
    `ModelQuality <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-modelquality.html>`__
    """

    props: PropsDictType = {
        "Constraints": (MetricsSource, False),
        "Statistics": (MetricsSource, False),
    }


class ModelMetrics(AWSProperty):
    """
    `ModelMetrics <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-modelmetrics.html>`__
    """

    props: PropsDictType = {
        "Bias": (Bias, False),
        "Explainability": (Explainability, False),
        "ModelDataQuality": (ModelDataQuality, False),
        "ModelQuality": (ModelQuality, False),
    }


class ModelPackageStatusItem(AWSProperty):
    """
    `ModelPackageStatusItem <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-modelpackagestatusitem.html>`__
    """

    props: PropsDictType = {
        "FailureReason": (str, False),
        "Name": (str, True),
        "Status": (str, True),
    }


class ModelPackageStatusDetails(AWSProperty):
    """
    `ModelPackageStatusDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-modelpackagestatusdetails.html>`__
    """

    props: PropsDictType = {
        "ValidationStatuses": ([ModelPackageStatusItem], False),
    }


class SourceAlgorithmSpecification(AWSProperty):
    """
    `SourceAlgorithmSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-sourcealgorithmspecification.html>`__
    """

    props: PropsDictType = {
        "SourceAlgorithms": ([SourceAlgorithm], True),
    }


class S3DataSource(AWSProperty):
    """
    `S3DataSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-s3datasource.html>`__
    """

    props: PropsDictType = {
        "S3DataType": (str, True),
        "S3Uri": (str, True),
    }


class DataSource(AWSProperty):
    """
    `DataSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-datasource.html>`__
    """

    props: PropsDictType = {
        "S3DataSource": (S3DataSource, True),
    }


class TransformInput(AWSProperty):
    """
    `TransformInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-transforminput.html>`__
    """

    props: PropsDictType = {
        "CompressionType": (str, False),
        "ContentType": (str, False),
        "DataSource": (DataSource, True),
        "SplitType": (str, False),
    }


class TransformOutput(AWSProperty):
    """
    `TransformOutput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-transformoutput.html>`__
    """

    props: PropsDictType = {
        "Accept": (str, False),
        "AssembleWith": (str, False),
        "KmsKeyId": (str, False),
        "S3OutputPath": (str, True),
    }


class TransformResources(AWSProperty):
    """
    `TransformResources <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-transformresources.html>`__
    """

    props: PropsDictType = {
        "InstanceCount": (integer, True),
        "InstanceType": (str, True),
        "VolumeKmsKeyId": (str, False),
    }


class TransformJobDefinition(AWSProperty):
    """
    `TransformJobDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-transformjobdefinition.html>`__
    """

    props: PropsDictType = {
        "BatchStrategy": (str, False),
        "Environment": (dict, False),
        "MaxConcurrentTransforms": (integer, False),
        "MaxPayloadInMB": (integer, False),
        "TransformInput": (TransformInput, True),
        "TransformOutput": (TransformOutput, True),
        "TransformResources": (TransformResources, True),
    }


class ValidationProfile(AWSProperty):
    """
    `ValidationProfile <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-validationprofile.html>`__
    """

    props: PropsDictType = {
        "ProfileName": (str, True),
        "TransformJobDefinition": (TransformJobDefinition, True),
    }


class ValidationSpecification(AWSProperty):
    """
    `ValidationSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelpackage-validationspecification.html>`__
    """

    props: PropsDictType = {
        "ValidationProfiles": ([ValidationProfile], True),
        "ValidationRole": (str, True),
    }


class ModelPackage(AWSObject):
    """
    `ModelPackage <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-modelpackage.html>`__
    """

    resource_type = "AWS::SageMaker::ModelPackage"

    props: PropsDictType = {
        "AdditionalInferenceSpecifications": (
            [AdditionalInferenceSpecificationDefinition],
            False,
        ),
        "AdditionalInferenceSpecificationsToAdd": (
            [AdditionalInferenceSpecificationDefinition],
            False,
        ),
        "ApprovalDescription": (str, False),
        "CertifyForMarketplace": (boolean, False),
        "ClientToken": (str, False),
        "CustomerMetadataProperties": (dict, False),
        "Domain": (str, False),
        "DriftCheckBaselines": (DriftCheckBaselines, False),
        "InferenceSpecification": (InferenceSpecification, False),
        "LastModifiedTime": (str, False),
        "MetadataProperties": (MetadataProperties, False),
        "ModelApprovalStatus": (str, False),
        "ModelCard": (ModelCardProperty, False),
        "ModelMetrics": (ModelMetrics, False),
        "ModelPackageDescription": (str, False),
        "ModelPackageGroupName": (str, False),
        "ModelPackageName": (str, False),
        "ModelPackageStatusDetails": (ModelPackageStatusDetails, False),
        "ModelPackageVersion": (integer, False),
        "SamplePayloadUrl": (str, False),
        "SecurityConfig": (SecurityConfig, False),
        "SkipModelValidation": (str, False),
        "SourceAlgorithmSpecification": (SourceAlgorithmSpecification, False),
        "SourceUri": (str, False),
        "Tags": (Tags, False),
        "Task": (str, False),
        "ValidationSpecification": (ValidationSpecification, False),
    }


class ModelPackageGroup(AWSObject):
    """
    `ModelPackageGroup <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-modelpackagegroup.html>`__
    """

    resource_type = "AWS::SageMaker::ModelPackageGroup"

    props: PropsDictType = {
        "ModelPackageGroupDescription": (str, False),
        "ModelPackageGroupName": (str, True),
        "ModelPackageGroupPolicy": (dict, False),
        "Tags": (Tags, False),
    }


class ModelQualityAppSpecification(AWSProperty):
    """
    `ModelQualityAppSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelqualityjobdefinition-modelqualityappspecification.html>`__
    """

    props: PropsDictType = {
        "ContainerArguments": ([str], False),
        "ContainerEntrypoint": ([str], False),
        "Environment": (dict, False),
        "ImageUri": (str, True),
        "PostAnalyticsProcessorSourceUri": (str, False),
        "ProblemType": (str, True),
        "RecordPreprocessorSourceUri": (str, False),
    }


class ModelQualityBaselineConfig(AWSProperty):
    """
    `ModelQualityBaselineConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelqualityjobdefinition-modelqualitybaselineconfig.html>`__
    """

    props: PropsDictType = {
        "BaseliningJobName": (str, False),
        "ConstraintsResource": (ConstraintsResource, False),
    }


class ModelQualityEndpointInput(AWSProperty):
    """
    `ModelQualityEndpointInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelqualityjobdefinition-endpointinput.html>`__
    """

    props: PropsDictType = {
        "EndTimeOffset": (str, False),
        "EndpointName": (str, True),
        "InferenceAttribute": (str, False),
        "LocalPath": (str, True),
        "ProbabilityAttribute": (str, False),
        "ProbabilityThresholdAttribute": (double, False),
        "S3DataDistributionType": (str, False),
        "S3InputMode": (str, False),
        "StartTimeOffset": (str, False),
    }


class ModelQualityJobInput(AWSProperty):
    """
    `ModelQualityJobInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelqualityjobdefinition-modelqualityjobinput.html>`__
    """

    props: PropsDictType = {
        "BatchTransformInput": (BatchTransformInput, False),
        "EndpointInput": (ModelQualityEndpointInput, False),
        "GroundTruthS3Input": (MonitoringGroundTruthS3Input, True),
    }


class ModelQualityJobDefinition(AWSObject):
    """
    `ModelQualityJobDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-modelqualityjobdefinition.html>`__
    """

    resource_type = "AWS::SageMaker::ModelQualityJobDefinition"

    props: PropsDictType = {
        "EndpointName": (str, False),
        "JobDefinitionName": (str, False),
        "JobResources": (MonitoringResources, True),
        "ModelQualityAppSpecification": (ModelQualityAppSpecification, True),
        "ModelQualityBaselineConfig": (ModelQualityBaselineConfig, False),
        "ModelQualityJobInput": (ModelQualityJobInput, True),
        "ModelQualityJobOutputConfig": (MonitoringOutputConfig, True),
        "NetworkConfig": (NetworkConfig, False),
        "RoleArn": (str, True),
        "StoppingCondition": (StoppingCondition, False),
        "Tags": (Tags, False),
    }


class MonitoringExecutionSummary(AWSProperty):
    """
    `MonitoringExecutionSummary <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-monitoringexecutionsummary.html>`__
    """

    props: PropsDictType = {
        "CreationTime": (str, True),
        "EndpointName": (str, False),
        "FailureReason": (str, False),
        "LastModifiedTime": (str, True),
        "MonitoringExecutionStatus": (str, True),
        "MonitoringScheduleName": (str, True),
        "ProcessingJobArn": (str, False),
        "ScheduledTime": (str, True),
    }


class BaselineConfig(AWSProperty):
    """
    `BaselineConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-baselineconfig.html>`__
    """

    props: PropsDictType = {
        "ConstraintsResource": (ConstraintsResource, False),
        "StatisticsResource": (StatisticsResource, False),
    }


class MonitoringAppSpecification(AWSProperty):
    """
    `MonitoringAppSpecification <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-monitoringappspecification.html>`__
    """

    props: PropsDictType = {
        "ContainerArguments": ([str], False),
        "ContainerEntrypoint": ([str], False),
        "ImageUri": (str, True),
        "PostAnalyticsProcessorSourceUri": (str, False),
        "RecordPreprocessorSourceUri": (str, False),
    }


class MonitoringInput(AWSProperty):
    """
    `MonitoringInput <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-monitoringinput.html>`__
    """

    props: PropsDictType = {
        "BatchTransformInput": (BatchTransformInput, False),
        "EndpointInput": (EndpointInput, False),
    }


class MonitoringJobDefinition(AWSProperty):
    """
    `MonitoringJobDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-monitoringjobdefinition.html>`__
    """

    props: PropsDictType = {
        "BaselineConfig": (BaselineConfig, False),
        "Environment": (dict, False),
        "MonitoringAppSpecification": (MonitoringAppSpecification, True),
        "MonitoringInputs": ([MonitoringInput], True),
        "MonitoringOutputConfig": (MonitoringOutputConfig, True),
        "MonitoringResources": (MonitoringResources, True),
        "NetworkConfig": (NetworkConfig, False),
        "RoleArn": (str, True),
        "StoppingCondition": (StoppingCondition, False),
    }


class ScheduleConfig(AWSProperty):
    """
    `ScheduleConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-scheduleconfig.html>`__
    """

    props: PropsDictType = {
        "DataAnalysisEndTime": (str, False),
        "DataAnalysisStartTime": (str, False),
        "ScheduleExpression": (str, True),
    }


class MonitoringScheduleConfig(AWSProperty):
    """
    `MonitoringScheduleConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-monitoringschedule-monitoringscheduleconfig.html>`__
    """

    props: PropsDictType = {
        "MonitoringJobDefinition": (MonitoringJobDefinition, False),
        "MonitoringJobDefinitionName": (str, False),
        "MonitoringType": (str, False),
        "ScheduleConfig": (ScheduleConfig, False),
    }


class MonitoringSchedule(AWSObject):
    """
    `MonitoringSchedule <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-monitoringschedule.html>`__
    """

    resource_type = "AWS::SageMaker::MonitoringSchedule"

    props: PropsDictType = {
        "EndpointName": (str, False),
        "FailureReason": (str, False),
        "LastMonitoringExecutionSummary": (MonitoringExecutionSummary, False),
        "MonitoringScheduleConfig": (MonitoringScheduleConfig, True),
        "MonitoringScheduleName": (str, True),
        "MonitoringScheduleStatus": (str, False),
        "Tags": (Tags, False),
    }


class InstanceMetadataServiceConfiguration(AWSProperty):
    """
    `InstanceMetadataServiceConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-notebookinstance-instancemetadataserviceconfiguration.html>`__
    """

    props: PropsDictType = {
        "MinimumInstanceMetadataServiceVersion": (str, True),
    }


class NotebookInstance(AWSObject):
    """
    `NotebookInstance <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-notebookinstance.html>`__
    """

    resource_type = "AWS::SageMaker::NotebookInstance"

    props: PropsDictType = {
        "AcceleratorTypes": ([str], False),
        "AdditionalCodeRepositories": ([str], False),
        "DefaultCodeRepository": (str, False),
        "DirectInternetAccess": (str, False),
        "InstanceMetadataServiceConfiguration": (
            InstanceMetadataServiceConfiguration,
            False,
        ),
        "InstanceType": (str, True),
        "KmsKeyId": (str, False),
        "LifecycleConfigName": (str, False),
        "NotebookInstanceName": (str, False),
        "PlatformIdentifier": (str, False),
        "RoleArn": (str, True),
        "RootAccess": (str, False),
        "SecurityGroupIds": ([str], False),
        "SubnetId": (str, False),
        "Tags": (Tags, False),
        "VolumeSizeInGB": (integer, False),
    }


class NotebookInstanceLifecycleHook(AWSProperty):
    """
    `NotebookInstanceLifecycleHook <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-notebookinstancelifecycleconfig-notebookinstancelifecyclehook.html>`__
    """

    props: PropsDictType = {
        "Content": (str, False),
    }


class NotebookInstanceLifecycleConfig(AWSObject):
    """
    `NotebookInstanceLifecycleConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-notebookinstancelifecycleconfig.html>`__
    """

    resource_type = "AWS::SageMaker::NotebookInstanceLifecycleConfig"

    props: PropsDictType = {
        "NotebookInstanceLifecycleConfigName": (str, False),
        "OnCreate": ([NotebookInstanceLifecycleHook], False),
        "OnStart": ([NotebookInstanceLifecycleHook], False),
    }


class PartnerAppConfig(AWSProperty):
    """
    `PartnerAppConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-partnerapp-partnerappconfig.html>`__
    """

    props: PropsDictType = {
        "AdminUsers": ([str], False),
        "Arguments": (dict, False),
    }


class PartnerAppMaintenanceConfig(AWSProperty):
    """
    `PartnerAppMaintenanceConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-partnerapp-partnerappmaintenanceconfig.html>`__
    """

    props: PropsDictType = {
        "MaintenanceWindowStart": (str, True),
    }


class PartnerApp(AWSObject):
    """
    `PartnerApp <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-partnerapp.html>`__
    """

    resource_type = "AWS::SageMaker::PartnerApp"

    props: PropsDictType = {
        "ApplicationConfig": (PartnerAppConfig, False),
        "AuthType": (str, True),
        "EnableIamSessionBasedIdentity": (boolean, False),
        "ExecutionRoleArn": (str, True),
        "KmsKeyId": (str, False),
        "MaintenanceConfig": (PartnerAppMaintenanceConfig, False),
        "Name": (str, True),
        "Tags": (Tags, False),
        "Tier": (str, True),
        "Type": (str, True),
    }


class ParallelismConfiguration(AWSProperty):
    """
    `ParallelismConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-pipeline-parallelismconfiguration.html>`__
    """

    props: PropsDictType = {
        "MaxParallelExecutionSteps": (integer, True),
    }


class S3Location(AWSProperty):
    """
    `S3Location <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-pipeline-s3location.html>`__
    """

    props: PropsDictType = {
        "Bucket": (str, True),
        "ETag": (str, False),
        "Key": (str, True),
        "Version": (str, False),
    }


class PipelineDefinition(AWSProperty):
    """
    `PipelineDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-pipeline-pipelinedefinition.html>`__
    """

    props: PropsDictType = {
        "PipelineDefinitionBody": (str, False),
        "PipelineDefinitionS3Location": (S3Location, False),
    }


class Pipeline(AWSObject):
    """
    `Pipeline <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-pipeline.html>`__
    """

    resource_type = "AWS::SageMaker::Pipeline"

    props: PropsDictType = {
        "ParallelismConfiguration": (ParallelismConfiguration, False),
        "PipelineDefinition": (PipelineDefinition, True),
        "PipelineDescription": (str, False),
        "PipelineDisplayName": (str, False),
        "PipelineName": (str, True),
        "RoleArn": (str, True),
        "Tags": (Tags, False),
    }


class ServiceCatalogProvisionedProductDetails(AWSProperty):
    """
    `ServiceCatalogProvisionedProductDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-project-servicecatalogprovisionedproductdetails.html>`__
    """

    props: PropsDictType = {
        "ProvisionedProductId": (str, False),
        "ProvisionedProductStatusMessage": (str, False),
    }


class ProvisioningParameter(AWSProperty):
    """
    `ProvisioningParameter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-project-provisioningparameter.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (str, True),
    }


class ServiceCatalogProvisioningDetails(AWSProperty):
    """
    `ServiceCatalogProvisioningDetails <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-project-servicecatalogprovisioningdetails.html>`__
    """

    props: PropsDictType = {
        "PathId": (str, False),
        "ProductId": (str, True),
        "ProvisioningArtifactId": (str, False),
        "ProvisioningParameters": ([ProvisioningParameter], False),
    }


class Project(AWSObject):
    """
    `Project <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-project.html>`__
    """

    resource_type = "AWS::SageMaker::Project"

    props: PropsDictType = {
        "ProjectDescription": (str, False),
        "ProjectName": (str, True),
        "ServiceCatalogProvisionedProductDetails": (
            ServiceCatalogProvisionedProductDetails,
            False,
        ),
        "ServiceCatalogProvisioningDetails": (ServiceCatalogProvisioningDetails, True),
        "Tags": (Tags, False),
    }


class OwnershipSettings(AWSProperty):
    """
    `OwnershipSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-space-ownershipsettings.html>`__
    """

    props: PropsDictType = {
        "OwnerUserProfileName": (str, True),
    }


class EFSFileSystem(AWSProperty):
    """
    `EFSFileSystem <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-space-efsfilesystem.html>`__
    """

    props: PropsDictType = {
        "FileSystemId": (str, True),
    }


class FSxLustreFileSystem(AWSProperty):
    """
    `FSxLustreFileSystem <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-space-fsxlustrefilesystem.html>`__
    """

    props: PropsDictType = {
        "FileSystemId": (str, True),
    }


class CustomFileSystem(AWSProperty):
    """
    `CustomFileSystem <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-space-customfilesystem.html>`__
    """

    props: PropsDictType = {
        "EFSFileSystem": (EFSFileSystem, False),
        "FSxLustreFileSystem": (FSxLustreFileSystem, False),
    }


class SpaceIdleSettings(AWSProperty):
    """
    `SpaceIdleSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-space-spaceidlesettings.html>`__
    """

    props: PropsDictType = {
        "IdleTimeoutInMinutes": (integer, False),
    }


class SpaceAppLifecycleManagement(AWSProperty):
    """
    `SpaceAppLifecycleManagement <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-space-spaceapplifecyclemanagement.html>`__
    """

    props: PropsDictType = {
        "IdleSettings": (SpaceIdleSettings, False),
    }


class SpaceCodeEditorAppSettings(AWSProperty):
    """
    `SpaceCodeEditorAppSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-space-spacecodeeditorappsettings.html>`__
    """

    props: PropsDictType = {
        "AppLifecycleManagement": (SpaceAppLifecycleManagement, False),
        "DefaultResourceSpec": (ResourceSpec, False),
    }


class SpaceJupyterLabAppSettings(AWSProperty):
    """
    `SpaceJupyterLabAppSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-space-spacejupyterlabappsettings.html>`__
    """

    props: PropsDictType = {
        "AppLifecycleManagement": (SpaceAppLifecycleManagement, False),
        "CodeRepositories": ([CodeRepositoryProperty], False),
        "DefaultResourceSpec": (ResourceSpec, False),
    }


class EbsStorageSettings(AWSProperty):
    """
    `EbsStorageSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-space-ebsstoragesettings.html>`__
    """

    props: PropsDictType = {
        "EbsVolumeSizeInGb": (integer, True),
    }


class SpaceStorageSettings(AWSProperty):
    """
    `SpaceStorageSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-space-spacestoragesettings.html>`__
    """

    props: PropsDictType = {
        "EbsStorageSettings": (EbsStorageSettings, False),
    }


class SpaceSettings(AWSProperty):
    """
    `SpaceSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-space-spacesettings.html>`__
    """

    props: PropsDictType = {
        "AppType": (str, False),
        "CodeEditorAppSettings": (SpaceCodeEditorAppSettings, False),
        "CustomFileSystems": ([CustomFileSystem], False),
        "JupyterLabAppSettings": (SpaceJupyterLabAppSettings, False),
        "JupyterServerAppSettings": (JupyterServerAppSettings, False),
        "KernelGatewayAppSettings": (KernelGatewayAppSettings, False),
        "SpaceStorageSettings": (SpaceStorageSettings, False),
    }


class SpaceSharingSettings(AWSProperty):
    """
    `SpaceSharingSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-space-spacesharingsettings.html>`__
    """

    props: PropsDictType = {
        "SharingType": (str, True),
    }


class Space(AWSObject):
    """
    `Space <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-space.html>`__
    """

    resource_type = "AWS::SageMaker::Space"

    props: PropsDictType = {
        "DomainId": (str, True),
        "OwnershipSettings": (OwnershipSettings, False),
        "SpaceDisplayName": (str, False),
        "SpaceName": (str, True),
        "SpaceSettings": (SpaceSettings, False),
        "SpaceSharingSettings": (SpaceSharingSettings, False),
        "Tags": (Tags, False),
    }


class StudioLifecycleConfig(AWSObject):
    """
    `StudioLifecycleConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-studiolifecycleconfig.html>`__
    """

    resource_type = "AWS::SageMaker::StudioLifecycleConfig"

    props: PropsDictType = {
        "StudioLifecycleConfigAppType": (str, True),
        "StudioLifecycleConfigContent": (str, True),
        "StudioLifecycleConfigName": (str, True),
        "Tags": (Tags, False),
    }


class UserProfile(AWSObject):
    """
    `UserProfile <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-userprofile.html>`__
    """

    resource_type = "AWS::SageMaker::UserProfile"

    props: PropsDictType = {
        "DomainId": (str, True),
        "SingleSignOnUserIdentifier": (str, False),
        "SingleSignOnUserValue": (str, False),
        "Tags": (Tags, False),
        "UserProfileName": (str, True),
        "UserSettings": (UserSettings, False),
    }


class CognitoMemberDefinition(AWSProperty):
    """
    `CognitoMemberDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-workteam-cognitomemberdefinition.html>`__
    """

    props: PropsDictType = {
        "CognitoClientId": (str, True),
        "CognitoUserGroup": (str, True),
        "CognitoUserPool": (str, True),
    }


class OidcMemberDefinition(AWSProperty):
    """
    `OidcMemberDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-workteam-oidcmemberdefinition.html>`__
    """

    props: PropsDictType = {
        "OidcGroups": ([str], True),
    }


class MemberDefinition(AWSProperty):
    """
    `MemberDefinition <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-workteam-memberdefinition.html>`__
    """

    props: PropsDictType = {
        "CognitoMemberDefinition": (CognitoMemberDefinition, False),
        "OidcMemberDefinition": (OidcMemberDefinition, False),
    }


class NotificationConfiguration(AWSProperty):
    """
    `NotificationConfiguration <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-workteam-notificationconfiguration.html>`__
    """

    props: PropsDictType = {
        "NotificationTopicArn": (str, True),
    }


class Workteam(AWSObject):
    """
    `Workteam <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-sagemaker-workteam.html>`__
    """

    resource_type = "AWS::SageMaker::Workteam"

    props: PropsDictType = {
        "Description": (str, False),
        "MemberDefinitions": ([MemberDefinition], False),
        "NotificationConfiguration": (NotificationConfiguration, False),
        "Tags": (Tags, False),
        "WorkforceName": (str, False),
        "WorkteamName": (str, False),
    }


class AdditionalModelDataSource(AWSProperty):
    """
    `AdditionalModelDataSource <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-model-additionalmodeldatasource.html>`__
    """

    props: PropsDictType = {
        "ChannelName": (str, True),
        "S3DataSource": (S3DataSource, True),
    }


class Container(AWSProperty):
    """
    `Container <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-modelcard-container.html>`__
    """

    props: PropsDictType = {
        "Image": (str, True),
        "ModelDataUrl": (str, False),
        "NearestModelName": (str, False),
    }


class EndpointMetadata(AWSProperty):
    """
    `EndpointMetadata <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-inferenceexperiment-endpointmetadata.html>`__
    """

    props: PropsDictType = {
        "EndpointConfigName": (str, False),
        "EndpointName": (str, True),
        "EndpointStatus": (str, False),
    }


class HubAccessConfig(AWSProperty):
    """
    `HubAccessConfig <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-model-s3datasource-hubaccessconfig.html>`__
    """

    props: PropsDictType = {
        "HubContentArn": (str, True),
    }


class RSessionAppSettings(AWSProperty):
    """
    `RSessionAppSettings <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-sagemaker-domain-rsessionappsettings.html>`__
    """

    props: PropsDictType = {
        "CustomImages": ([CustomImage], False),
        "DefaultResourceSpec": (ResourceSpec, False),
    }
