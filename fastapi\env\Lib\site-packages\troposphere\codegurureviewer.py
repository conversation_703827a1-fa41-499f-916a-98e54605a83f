# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, PropsDictType, Tags


class RepositoryAssociation(AWSObject):
    """
    `RepositoryAssociation <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-codegurureviewer-repositoryassociation.html>`__
    """

    resource_type = "AWS::CodeGuruReviewer::RepositoryAssociation"

    props: PropsDictType = {
        "BucketName": (str, False),
        "ConnectionArn": (str, False),
        "Name": (str, True),
        "Owner": (str, False),
        "Tags": (Tags, False),
        "Type": (str, True),
    }
