# Copyright (c) 2012-2025, <PERSON> <<EMAIL>>
# All rights reserved.
#
# See LICENSE file for full license.
#
# *** Do not modify - this file is autogenerated ***


from . import AWSObject, AWSProperty, PropsDictType, Tags
from .validators import boolean, double, integer
from .validators.backup import (
    backup_vault_name,
    validate_backup_selection,
    validate_json_checker,
)


class AdvancedBackupSettingResourceType(AWSProperty):
    """
    `AdvancedBackupSettingResourceType <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-backupplan-advancedbackupsettingresourcetype.html>`__
    """

    props: PropsDictType = {
        "BackupOptions": (dict, True),
        "ResourceType": (str, True),
    }


class LifecycleResourceType(AWSProperty):
    """
    `LifecycleResourceType <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-backupplan-lifecycleresourcetype.html>`__
    """

    props: PropsDictType = {
        "DeleteAfterDays": (double, False),
        "MoveToColdStorageAfterDays": (double, False),
        "OptInToArchiveForSupportedResources": (boolean, False),
    }


class CopyActionResourceType(AWSProperty):
    """
    `CopyActionResourceType <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-backupplan-copyactionresourcetype.html>`__
    """

    props: PropsDictType = {
        "DestinationBackupVaultArn": (str, True),
        "Lifecycle": (LifecycleResourceType, False),
    }


class IndexActionsResourceType(AWSProperty):
    """
    `IndexActionsResourceType <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-backupplan-indexactionsresourcetype.html>`__
    """

    props: PropsDictType = {
        "ResourceTypes": ([str], False),
    }


class BackupRuleResourceType(AWSProperty):
    """
    `BackupRuleResourceType <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-backupplan-backupruleresourcetype.html>`__
    """

    props: PropsDictType = {
        "CompletionWindowMinutes": (double, False),
        "CopyActions": ([CopyActionResourceType], False),
        "EnableContinuousBackup": (boolean, False),
        "IndexActions": ([IndexActionsResourceType], False),
        "Lifecycle": (LifecycleResourceType, False),
        "RecoveryPointTags": (dict, False),
        "RuleName": (str, True),
        "ScheduleExpression": (str, False),
        "ScheduleExpressionTimezone": (str, False),
        "StartWindowMinutes": (double, False),
        "TargetBackupVault": (str, True),
    }


class BackupPlanResourceType(AWSProperty):
    """
    `BackupPlanResourceType <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-backupplan-backupplanresourcetype.html>`__
    """

    props: PropsDictType = {
        "AdvancedBackupSettings": ([AdvancedBackupSettingResourceType], False),
        "BackupPlanName": (str, True),
        "BackupPlanRule": ([BackupRuleResourceType], True),
    }


class BackupPlan(AWSObject):
    """
    `BackupPlan <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-backup-backupplan.html>`__
    """

    resource_type = "AWS::Backup::BackupPlan"

    props: PropsDictType = {
        "BackupPlan": (BackupPlanResourceType, True),
        "BackupPlanTags": (dict, False),
    }


class ConditionResourceType(AWSProperty):
    """
    `ConditionResourceType <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-backupselection-conditionresourcetype.html>`__
    """

    props: PropsDictType = {
        "ConditionKey": (str, True),
        "ConditionType": (str, True),
        "ConditionValue": (str, True),
    }


class ConditionParameter(AWSProperty):
    """
    `ConditionParameter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-backupselection-conditionparameter.html>`__
    """

    props: PropsDictType = {
        "ConditionKey": (str, False),
        "ConditionValue": (str, False),
    }


class Conditions(AWSProperty):
    """
    `Conditions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-backupselection-conditions.html>`__
    """

    props: PropsDictType = {
        "StringEquals": ([ConditionParameter], False),
        "StringLike": ([ConditionParameter], False),
        "StringNotEquals": ([ConditionParameter], False),
        "StringNotLike": ([ConditionParameter], False),
    }


class BackupSelectionResourceType(AWSProperty):
    """
    `BackupSelectionResourceType <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-backupselection-backupselectionresourcetype.html>`__
    """

    props: PropsDictType = {
        "Conditions": (Conditions, False),
        "IamRoleArn": (str, True),
        "ListOfTags": ([ConditionResourceType], False),
        "NotResources": ([str], False),
        "Resources": ([str], False),
        "SelectionName": (str, True),
    }

    def validate(self):
        validate_backup_selection(self)


class BackupSelection(AWSObject):
    """
    `BackupSelection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-backup-backupselection.html>`__
    """

    resource_type = "AWS::Backup::BackupSelection"

    props: PropsDictType = {
        "BackupPlanId": (str, True),
        "BackupSelection": (BackupSelectionResourceType, True),
    }


class LockConfigurationType(AWSProperty):
    """
    `LockConfigurationType <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-backupvault-lockconfigurationtype.html>`__
    """

    props: PropsDictType = {
        "ChangeableForDays": (integer, False),
        "MaxRetentionDays": (integer, False),
        "MinRetentionDays": (integer, True),
    }


class NotificationObjectType(AWSProperty):
    """
    `NotificationObjectType <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-logicallyairgappedbackupvault-notificationobjecttype.html>`__
    """

    props: PropsDictType = {
        "BackupVaultEvents": ([str], True),
        "SNSTopicArn": (str, True),
    }


class BackupVault(AWSObject):
    """
    `BackupVault <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-backup-backupvault.html>`__
    """

    resource_type = "AWS::Backup::BackupVault"

    props: PropsDictType = {
        "AccessPolicy": (validate_json_checker, False),
        "BackupVaultName": (backup_vault_name, True),
        "BackupVaultTags": (dict, False),
        "EncryptionKeyArn": (str, False),
        "LockConfiguration": (LockConfigurationType, False),
        "Notifications": (NotificationObjectType, False),
    }


class ControlInputParameter(AWSProperty):
    """
    `ControlInputParameter <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-framework-controlinputparameter.html>`__
    """

    props: PropsDictType = {
        "ParameterName": (str, True),
        "ParameterValue": (str, True),
    }


class ControlScope(AWSProperty):
    """
    `ControlScope <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-framework-controlscope.html>`__
    """

    props: PropsDictType = {
        "ComplianceResourceIds": ([str], False),
        "ComplianceResourceTypes": ([str], False),
        "Tags": (Tags, False),
    }


class FrameworkControl(AWSProperty):
    """
    `FrameworkControl <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-framework-frameworkcontrol.html>`__
    """

    props: PropsDictType = {
        "ControlInputParameters": ([ControlInputParameter], False),
        "ControlName": (str, True),
        "ControlScope": (ControlScope, False),
    }


class Framework(AWSObject):
    """
    `Framework <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-backup-framework.html>`__
    """

    resource_type = "AWS::Backup::Framework"

    props: PropsDictType = {
        "FrameworkControls": ([FrameworkControl], True),
        "FrameworkDescription": (str, False),
        "FrameworkName": (str, False),
        "FrameworkTags": (Tags, False),
    }


class LogicallyAirGappedBackupVault(AWSObject):
    """
    `LogicallyAirGappedBackupVault <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-backup-logicallyairgappedbackupvault.html>`__
    """

    resource_type = "AWS::Backup::LogicallyAirGappedBackupVault"

    props: PropsDictType = {
        "AccessPolicy": (dict, False),
        "BackupVaultName": (str, True),
        "BackupVaultTags": (dict, False),
        "MaxRetentionDays": (integer, True),
        "MinRetentionDays": (integer, True),
        "Notifications": (NotificationObjectType, False),
    }


class ReportDeliveryChannel(AWSProperty):
    """
    `ReportDeliveryChannel <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-reportplan-reportdeliverychannel.html>`__
    """

    props: PropsDictType = {
        "Formats": ([str], False),
        "S3BucketName": (str, True),
        "S3KeyPrefix": (str, False),
    }


class ReportSetting(AWSProperty):
    """
    `ReportSetting <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-reportplan-reportsetting.html>`__
    """

    props: PropsDictType = {
        "Accounts": ([str], False),
        "FrameworkArns": ([str], False),
        "OrganizationUnits": ([str], False),
        "Regions": ([str], False),
        "ReportTemplate": (str, True),
    }


class ReportPlan(AWSObject):
    """
    `ReportPlan <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-backup-reportplan.html>`__
    """

    resource_type = "AWS::Backup::ReportPlan"

    props: PropsDictType = {
        "ReportDeliveryChannel": (ReportDeliveryChannel, True),
        "ReportPlanDescription": (str, False),
        "ReportPlanName": (str, False),
        "ReportPlanTags": (Tags, False),
        "ReportSetting": (ReportSetting, True),
    }


class RestoreTestingRecoveryPointSelection(AWSProperty):
    """
    `RestoreTestingRecoveryPointSelection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-restoretestingplan-restoretestingrecoverypointselection.html>`__
    """

    props: PropsDictType = {
        "Algorithm": (str, True),
        "ExcludeVaults": ([str], False),
        "IncludeVaults": ([str], True),
        "RecoveryPointTypes": ([str], True),
        "SelectionWindowDays": (integer, False),
    }


class RestoreTestingPlan(AWSObject):
    """
    `RestoreTestingPlan <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-backup-restoretestingplan.html>`__
    """

    resource_type = "AWS::Backup::RestoreTestingPlan"

    props: PropsDictType = {
        "RecoveryPointSelection": (RestoreTestingRecoveryPointSelection, True),
        "RestoreTestingPlanName": (str, True),
        "ScheduleExpression": (str, True),
        "ScheduleExpressionTimezone": (str, False),
        "StartWindowHours": (integer, False),
        "Tags": (Tags, False),
    }


class KeyValue(AWSProperty):
    """
    `KeyValue <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-restoretestingselection-keyvalue.html>`__
    """

    props: PropsDictType = {
        "Key": (str, True),
        "Value": (str, True),
    }


class ProtectedResourceConditions(AWSProperty):
    """
    `ProtectedResourceConditions <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-backup-restoretestingselection-protectedresourceconditions.html>`__
    """

    props: PropsDictType = {
        "StringEquals": ([KeyValue], False),
        "StringNotEquals": ([KeyValue], False),
    }


class RestoreTestingSelection(AWSObject):
    """
    `RestoreTestingSelection <http://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-resource-backup-restoretestingselection.html>`__
    """

    resource_type = "AWS::Backup::RestoreTestingSelection"

    props: PropsDictType = {
        "IamRoleArn": (str, True),
        "ProtectedResourceArns": ([str], False),
        "ProtectedResourceConditions": (ProtectedResourceConditions, False),
        "ProtectedResourceType": (str, True),
        "RestoreMetadataOverrides": (dict, False),
        "RestoreTestingPlanName": (str, True),
        "RestoreTestingSelectionName": (str, True),
        "ValidationWindowHours": (integer, False),
    }
